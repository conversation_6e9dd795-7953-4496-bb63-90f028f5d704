from io import BytesIO
from typing import Optional
import numpy as np
import pandas as pd
from main.config.config_BSE import ConfigBSE
from main.config.config_factory import ConfigFactory
from daily_appends.daily_appends_base import DailyAppendsBase

from main.data.utility import get_library_name, previous_date
from main.enums import StorageType


class DailyAppendsBSE(DailyAppendsBase):
    def __init__(self, exchange_type: str) -> None:
        super().__init__(exchange_type=exchange_type)

    def __preprocess_bhav_data(self, bhav_universe, df, prev_date):
        if bhav_universe in [
            "cm_bhav",
            "futidx_bhav",
            "futstk_bhav",
            "optstk_bhav",
            "optidx_bhav",
        ]:
            df.columns = [x.lower() for x in df.columns]

            if "expiry_dt" in df.columns:
                df["expiry_dt"] = pd.to_datetime(df["expiry_dt"], format="%d-%b-%Y")
                df["expiry_rank"] = df.groupby("expiry_dt").expiry_dt.rank(
                    method="dense"
                )
            df["date"] = prev_date
            df = df.set_index("date")
            df["balte_id"] = df.symbol.map(self._symbol_to_balte_id)
            df = df[~(df["balte_id"].isna())]
            df["eod_price"] = df["close"]
            df["close_raw"] = df["close"]
            df["adj_factor"] = 1

            return df
        else:
            raise Exception(f"Bhavcopy handling not added for {bhav_universe}")

    def append_raw_bhav_copy(self, symbol, date=None):
        if date is None:
            date = self._config.DATE_TODAY - pd.Timedelta(days=1)

        raw_symbol = f"raw_{symbol}"

        library_name = get_library_name(
            exchange_name=self._config.EXCHANGE,
            frequency=1440,
            universe_name=raw_symbol,
            dtype="trd",
            storage=StorageType.DB,
        )
        bhav_copy = pd.read_csv(
            BytesIO(
                self._get_from_minio(
                    file_location="commondata",
                    file_name=self._config.AFTER_MARKET_DICT_MINIO[
                        self._config.UNIVERSE_TO_BALTE_UNIVERSE_MAPPING[symbol]
                    ],
                ).data
            )
        )

        bhav_copy.columns = [col.lower() for col in bhav_copy.columns]

        if "expiry_dt" in bhav_copy.columns:
            bhav_copy["expiry_dt"] = pd.to_datetime(
                bhav_copy["expiry_dt"], format="%d-%b-%Y"
            )

        bhav_copy["date"] = date
        bhav_copy = bhav_copy.set_index("date")

        bhav_copy = self._handle_data(universe=raw_symbol, data=bhav_copy)

        symbol_list = self._tanki[library_name].list_symbols()

        failed_syms = []

        for symbol_bhav, symbol_bhav_df in bhav_copy.groupby("symbol"):
            try:
                self._push_data_to_db(
                    library_name=library_name,
                    symbol=symbol_bhav,
                    data=symbol_bhav_df,
                    date=date,
                    symbol_list=symbol_list,
                )
            except Exception as e:
                failed_syms.append(f"{symbol_bhav}: {e}")

        if len(failed_syms):
            raise Exception(
                f"DailyAppendsError: Following raw bhav symbols are failed to append:\n{failed_syms}"
            )

    def append_daily_data_1440(self, universe, timing, symbol=None):
        library_name = get_library_name(
            exchange_name=self._config.EXCHANGE,
            frequency=1440,
            universe_name=universe,
            dtype="trd",
            storage=StorageType.DB,
        )
        bhav_universe = symbol

        if timing == "morning":
            prev_date = previous_date(self._all_dates)

            bhav_copy = pd.read_csv(
                BytesIO(
                    self._get_from_minio(
                        file_location="commondata",
                        file_name=self._config.AFTER_MARKET_DICT_MINIO[
                            self._config.UNIVERSE_TO_BALTE_UNIVERSE_MAPPING[
                                bhav_universe
                            ]
                        ],
                    ).data
                )
            )

            try:
                processed_copy = self.__preprocess_bhav_data(
                    bhav_universe,
                    bhav_copy,
                    prev_date,
                )
                if bhav_universe != "cm_bhav":
                    processed_copy = processed_copy[
                        processed_copy.instrument
                        == (symbol.split("_")[0].upper() + "_" + "BSE")
                    ]

                processed_copy = self._handle_data(bhav_universe, processed_copy)

                self._push_data_to_db(
                    library_name=library_name,
                    symbol=self._config.UNIVERSE_TO_BALTE_UNIVERSE_MAPPING[
                        bhav_universe
                    ],
                    data=processed_copy,
                    date=prev_date,
                )
            except Exception as exception:
                raise Exception(
                    f"DailyAppendsError: {bhav_universe} failed to append due to {exception}\n"
                )

        elif timing == "evening":
            symbols_failed_to_append = []
            symbol_list = self._tanki[library_name].list_symbols()
            for symbol in self._config.AFTER_MARKET_DICT_GRPC:
                symbol_data = self._toti_obj.get_fresh_1440(
                    symbol, self._config.DATE_TODAY
                )
                if len(symbol_data) == 0:
                    continue

                try:
                    symbol_data = symbol_data.set_index("date")
                    symbol_data = self._handle_data(universe=symbol, data=symbol_data)
                    self._push_data_to_db(
                        library_name=library_name,
                        symbol=symbol,
                        data=symbol_data,
                        symbol_list=symbol_list,
                    )
                except Exception as exception:
                    symbols_failed_to_append.append([symbol, exception])

            if symbols_failed_to_append:
                raise Exception(
                    f"DailyAppendsError: Following symbols are failed to append:\n{symbols_failed_to_append}\n"
                )
        else:
            raise Exception("DailyAppendsError: Invalid market timing!")
