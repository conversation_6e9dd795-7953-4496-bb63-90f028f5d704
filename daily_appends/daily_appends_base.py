from abc import ABC, abstractmethod
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Union
from io import BytesIO
import pickle
from main.config.config_base import ConfigBase
from main.config.config_factory import ConfigFactory
from main.data.operation import Operator
from main.enums import StorageType
from main.tanki import Tanki
from minio import Minio
from main.data.utility import (
    get_library_name,
)

import sys


sys.path.append("/home/<USER>/balte")
import balte
from toti import Toti


class DailyAppendsBase(ABC):
    def __init__(self, exchange_type: str) -> None:
        self._config: ConfigBase = ConfigFactory(exchange_type=exchange_type)
        self._tanki: Optional[Tanki] = Tanki(exchange_type=exchange_type, write=True)
        self._tanki.login(username="airflow", password="airflow")
        self._symbol_to_balte_id = pickle.load(
            BytesIO(
                self._get_from_minio(
                    self._config.FILE_DICT["MAPPING_DICT"][0],
                    self._config.FILE_DICT["MAPPING_DICT"][1],
                ).data
            )
        )

        balte.utility_inbuilt.balte_initializer(self._config.EXCHANGE)
        self._toti_obj = Toti(exchange=self._config.EXCHANGE_ENUM)
        self._all_dates = balte.balte_config.ALL_DATES

    def _get_from_minio(self, file_location, file_name):
        ministore = Minio("192.168.0.198:11009", "super", "doopersecret", secure=False)
        data = ministore.get_object(file_location, file_name)
        return data

    def _upload_to_minio(self, file_location, file_name, file):
        ministore = Minio("192.168.0.198:11009", "super", "doopersecret", secure=False)
        ministore.fput_object(file_location, file_name, file)

    def _push_data_to_db(
        self,
        library_name: str,
        symbol: Union[int, str],
        data: pd.DataFrame,
        date: Optional[pd.Timestamp] = None,
        symbol_list: Optional[List[str]] = None,
    ):
        if date is None:
            date = self._config.DATE_TODAY

        if symbol_list is None:
            symbol_list = self._tanki[library_name].list_symbols()

        if str(symbol) not in symbol_list:
            self._tanki[library_name].write_metadata(symbol=str(symbol), data=data)
            self._tanki[library_name].write(symbol=str(symbol), data=data)
        else:
            metadata_dict = self._tanki[library_name].read_metadata(symbol=str(symbol))
            if (metadata_dict is not None) and (
                pd.Timestamp(metadata_dict["last_timestamp"]) >= date
            ):
                return
            self._tanki[library_name].append(str(symbol), data)

    def _generate_time_strings(
        self, start_time_str: str, end_time_str: str, frequency: int
    ) -> List[str]:
        start = pd.to_datetime(start_time_str, format="%H:%M") + pd.Timedelta(
            minutes=frequency
        )
        end = pd.to_datetime(end_time_str, format="%H:%M")

        time_list = (
            pd.date_range(start=start, end=end, freq=f"{frequency}T")
            .strftime("%H:%M")
            .tolist()
        )
        return time_list

    def _handle_data(
        self, universe: str, data: pd.DataFrame, frequency: int = 5, dtype: str = "trd"
    ) -> pd.DataFrame:
        try:
            if dtype == "column_bucket":
                time_str_list = self._generate_time_strings(
                    start_time_str=self._config.MARKET_HOURS_DICT[universe]["open"],
                    end_time_str=self._config.MARKET_HOURS_DICT[universe]["close"],
                    frequency=frequency,
                )
                data = data.reindex(
                    columns=time_str_list,
                    fill_value=pd.NaT
                    if np.issubdtype(data.iloc[:, 0].dtype, "datetime64[ns]")
                    else np.nan,
                )
                return data

            index_names = data.index.names

            data = data.reset_index()

            if universe in self._config.UNIVERSE_TO_RENAME_COLUMN_DICT:
                rename_map = self._config.UNIVERSE_TO_RENAME_COLUMN_DICT[universe]
                data = data.rename(columns=rename_map)
                index_names = [rename_map.get(index, index) for index in index_names]

            universe_dtype = f"{universe}_{dtype}"
            col_list = self._config.UNIVERSE_DTYPE_TO_COLUMN_DICT.get(
                universe_dtype, self._config.COLUMNS_DICT.get(universe)
            )
            if not col_list:
                raise ValueError(
                    f"No columns added for universe '{universe}', dtype '{dtype}'"
                )

            data_types_dict = self._config.DATA_TYPES_DICT.copy()
            data_types_dict.update(
                self._config.UNIVERSE_TO_DATA_TYPE_DICT_UPDATE_DICT.get(universe, {})
            )

            data = data[col_list]
            for column in data.columns:
                data[column] = data[column].astype(dtype=data_types_dict[column])

            data = data.set_index(index_names)
        except Exception as exception:
            raise Exception(
                f"Error occurred during daily data appending process: {exception}"
            )

        return data

    def append_daily_data_raw(
        self,
        universe: str,
        dtype: str = "trd",
        date: Optional[pd.Timestamp] = None,
        freq: int = 1,
    ) -> bool:
        """Appends raw daily data for the given exchange (config)

        Args:
            universe (str): name of the universe

        Returns:
            bool: Returns True on successful daily appends
        """
        if date is None:
            date = self._config.DATE_TODAY
        balte_universe = self._config.UNIVERSE_TO_BALTE_UNIVERSE_MAPPING.get(
            universe, universe
        )

        if freq == 1:
            raw_balte_universe = f"raw_{balte_universe}_onemin"
            if universe.endswith("_oi"):
                raw_balte_universe = f"raw_{balte_universe}"
            data = self._toti_obj.get_fresh_1_full_day(raw_balte_universe, date, dtype)
        elif freq == 5:
            raw_balte_universe = f"raw_{balte_universe}"
            data = self._toti_obj.get_fresh_5_full_day(raw_balte_universe, date, dtype)

        raw_universe = "raw_" + universe

        library = get_library_name(
            exchange_name=self._config.EXCHANGE,
            frequency=freq,
            universe_name=raw_universe,
            dtype=dtype,
            storage=StorageType.DB,
        )

        data = self._handle_data(universe=raw_universe, data=data, dtype=dtype)

        if data["ID"].dtype == "object":
            data["Symbol_ID"] = data["ID"]
        else:
            data["Symbol_ID"] = data["ID"] % 10000

        symbol_list = self._tanki[library].list_symbols()
        failed_syms = []
        for symbol_id, data_id in data.groupby("Symbol_ID"):
            try:
                data_id = data_id.drop(columns=["Symbol_ID"])
                self._push_data_to_db(
                    library_name=library,
                    symbol=symbol_id,
                    data=data_id,
                    date=date,
                    symbol_list=symbol_list,
                )
            except Exception as exception:
                failed_syms.append(f"{symbol_id}: {exception}")

        if len(failed_syms):
            raise Exception(
                "DailyAppendsError: Following symbols failed to append:\n"
                + "\n".join(failed_syms)
            )

        return True

    def append_daily_data(
        self,
        universe: str,
        dtype: str = "trd",
        date: Optional[pd.Timestamp] = None,
        freq: int = 1,
    ) -> bool:
        """Appends 1 min or 5 min daily data for the given exchange (config)
        Makes calls to toti get_fresh_1_full_day or get_fresh_5_full_day

        Args:
            universe (str): name of the universe

        Returns:
            bool: Returns True on successful daily appends
        """
        if date is None:
            date = self._config.DATE_TODAY
        balte_universe = self._config.UNIVERSE_TO_BALTE_UNIVERSE_MAPPING.get(
            universe, universe
        )

        if freq == 1:
            data = self._toti_obj.get_fresh_1_full_day(
                balte_universe + "_onemin", date, dtype
            )
        elif freq == 5:
            data = self._toti_obj.get_fresh_5_full_day(balte_universe, date, dtype)
        else:
            raise ValueError("frequency can only be either 1min or 5min")

        library = get_library_name(
            exchange_name=self._config.EXCHANGE,
            frequency=freq,
            universe_name=universe,
            dtype=dtype,
            storage=StorageType.DB,
        )

        data = self._handle_data(universe=universe, data=data, dtype=dtype)

        if dtype == "indicator":
            iterator = (
                (sym, data[sym]) for sym in set(data.columns.get_level_values(0))
            )
        else:
            if dtype == "orderbook":
                data["Symbol_ID"] = data.index.get_level_values("ID") % 10000
            else:
                data["Symbol_ID"] = data["ID"] % 10000
            iterator = data.groupby("Symbol_ID")

        symbol_list = self._tanki[library].list_symbols()
        failed_syms = []
        for symbol_id, data_id in iterator:
            try:
                if not dtype == "indicator":
                    data_id = data_id.drop(columns=["Symbol_ID"])
                self._push_data_to_db(
                    library_name=library,
                    symbol=symbol_id,
                    data=data_id,
                    date=date,
                    symbol_list=symbol_list,
                )
            except Exception as exception:
                failed_syms.append(f"{symbol_id}: {repr(exception)}")

        if len(failed_syms):
            raise Exception(
                "DailyAppendsError: Following symbols failed to append:\n"
                + "\n".join(failed_syms)
            )

        return True

    def append_daily_data_1(
        self, universe: str, dtype: str = "trd", date: Optional[pd.Timestamp] = None
    ) -> bool:
        """Appends 1 min daily data for the given exchange (config)

        Args:
            universe (str): name of the universe

        Returns:
            bool: Returns True on successful daily appends
        """
        return self.append_daily_data(universe, dtype, date)

    def append_daily_data_5(
        self, universe: str, dtype: str = "trd", date: Optional[pd.Timestamp] = None
    ) -> bool:
        """Appends 5 min daily data for the given exchange (config)

        Args:
            universe (str): name of the universe

        Returns:
            bool: Returns True on successful daily appends
        """
        if date is None:
            date = self._config.DATE_TODAY

        base_library = get_library_name(
            exchange_name=self._config.EXCHANGE,
            frequency=1,
            universe_name=universe,
            dtype=dtype,
            storage=StorageType.DB,
        )
        library = get_library_name(
            exchange_name=self._config.EXCHANGE,
            frequency=5,
            universe_name=universe,
            dtype=dtype,
            storage=StorageType.DB,
        )

        operator = Operator(config=self._config)

        symbol_list = self._tanki[library].list_symbols()
        failed_syms = []
        for symbol in self._tanki[base_library].list_symbols():
            try:
                base_data = self._tanki[base_library].read(
                    symbol=symbol, start_date=date
                )
                if len(base_data) == 0:
                    continue

                data = operator.resample_data_1_min_to_5_min(
                    universe=universe, data=base_data
                )
                self._push_data_to_db(
                    library_name=library,
                    symbol=symbol,
                    data=data,
                    date=date,
                    symbol_list=symbol_list,
                )
            except Exception as exception:
                failed_syms.append(f"{symbol}: {exception}")

        if len(failed_syms):
            raise Exception(
                "DailyAppendsError: Following symbols failed to append:\n"
                + "\n".join(failed_syms)
            )

        return True

    def append_daily_data_column_bucket(
        self,
        universe: str,
        frequency: int,
        dtype: str = "trd",
        date: Optional[pd.Timestamp] = None,
    ) -> bool:
        """Appends daily data of column_bucket for the given exchange (config)

        Args:
            universe (str): name of the universe

        Returns:
            bool: Returns True on successful daily appends
        """
        if date is None:
            date = self._config.DATE_TODAY

        base_library = get_library_name(
            exchange_name=self._config.EXCHANGE,
            frequency=frequency,
            universe_name=universe,
            dtype=dtype,
            storage=StorageType.DB,
        )
        library = get_library_name(
            exchange_name=self._config.EXCHANGE,
            frequency=frequency,
            universe_name=universe,
            dtype="column_bucket",
            storage=StorageType.DB,
        )

        operator = Operator(config=self._config)

        symbol_wise_column_to_column_bucket_list: Dict[str, List[pd.DataFrame]] = {}

        for symbol in self._tanki[base_library].list_symbols():
            base_data = self._tanki[base_library].read(symbol=symbol, start_date=date)
            if len(base_data) == 0:
                continue

            column_to_column_bucket = operator.create_column_bucket(
                universe=universe, data=base_data
            )

            for column in column_to_column_bucket.keys():
                column_data = column_to_column_bucket[column]
                if column not in symbol_wise_column_to_column_bucket_list:
                    symbol_wise_column_to_column_bucket_list[column] = []
                symbol_wise_column_to_column_bucket_list[column].append(column_data)

        symbol_list = self._tanki[library].list_symbols()
        failed_syms = []
        for column in symbol_wise_column_to_column_bucket_list.keys():
            try:
                data = pd.concat(symbol_wise_column_to_column_bucket_list[column])
                data = self._handle_data(
                    universe=universe,
                    data=data,
                    dtype="column_bucket",
                    frequency=frequency,
                )
                data = data.sort_index()
                self._push_data_to_db(
                    library_name=library,
                    symbol=column,
                    data=data,
                    date=date,
                    symbol_list=symbol_list,
                )
            except Exception as exception:
                failed_syms.append(f"{column}: {exception}")

        if len(failed_syms):
            raise Exception(
                "DailyAppendsError: Following symbols failed to append:\n"
                + "\n".join(failed_syms)
            )

        return True

    @abstractmethod
    def append_raw_bhav_copy(self, symbol: str, date: Optional[pd.Timestamp] = None):
        pass

    @abstractmethod
    def append_daily_data_1440(self, universe, timing, symbol=None):
        pass
