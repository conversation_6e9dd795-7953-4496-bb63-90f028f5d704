from typing import Optional
import pandas as pd
from daily_appends.daily_appends_base import DailyAppendsBase
from main.data.utility import get_library_name
from main.enums import StorageType


class DailyAppendsUS(DailyAppendsBase):
    def __init__(self, exchange_type: str) -> None:
        super().__init__(exchange_type=exchange_type)
        # For faster getfresh calls when data is not present in sampler. Make sure getfresh full day
        # completes before sampler resets.
        self._toti_obj.config.MAX_GET_LATEST_TIMESTAMP_ATTEMPTS = 1

    def append_raw_bhav_copy(self, symbol: str, date: Optional[pd.Timestamp] = None):
        raise NotImplementedError

    def append_daily_data_1440(self, universe: str, date: pd.Timestamp):
        library_name = get_library_name(
            exchange_name=self._config.EXCHANGE,
            frequency=1,
            universe_name=universe,
            dtype="trd",
            storage=StorageType.DB,
        )
        df_list = []
        symbol_list = self._tanki[library_name].list_symbols()
        for symbol in symbol_list:
            data = self._tanki[library_name].read(
                symbol=symbol, start_date=self._config.DATE_TODAY
            )
            df_list.append(data)
        data = pd.concat(df_list).sort_index()
        if len(data) == 0:
            return
        symbol_name = (
            self._config.UNIVERSE_TO_BALTE_UNIVERSE_MAPPING.get(universe, universe)
            + "_bhav"
        )
        data = self.__preprocess_bhav_data(universe, data)

        aftermarket_library_name = get_library_name(
            exchange_name="international",
            frequency=1440,
            universe_name="after_market",
            dtype="trd",
            storage=StorageType.DB,
        )
        self._push_data_to_db(
            library_name=aftermarket_library_name,
            symbol=symbol_name,
            data=data,
            date=date,
        )

    def __preprocess_bhav_data(self, bhav_universe: str, df: pd.DataFrame):
        if bhav_universe == "futidx_fut_us_bhav":
            df["date"] = df.index.date
            df = df.drop_duplicates(subset=["ID"], keep="last")
            df.rename(columns={"ID": "balte_id", "Close": "eod_price"}, inplace=True)
            df = df.reset_index().set_index("date")
            df = df[["balte_id", "eod_price"]]
        elif bhav_universe == "opt_us_bhav":
            df = df.drop_duplicates(subset=["ID"], keep="last")
            df["date"] = df.index.date
            df.rename(
                columns={
                    "ID": "balte_id",
                    "Close": "close",
                    "Open": "open",
                    "High": "high",
                    "Low": "low",
                },
                inplace=True,
            )
            df["eod_price"] = df["close"]
            df = df.reset_index().set_index("date")
            df["expiry_dt"] = (
                ((df["balte_id"] % 1e13) / 1e5).astype("int").astype("str")
            )
            df["expiry_dt"] = pd.to_datetime(df["expiry_dt"], format="%Y%m%d")
            df["strike_pr"] = ((df["balte_id"] / int(1e13)) / 100).astype("int")
            df["option_type"] = ((df["balte_id"] / 1e4) % 10).astype("int")
            df["instrument"] = "OPTIDX_US"
            df = df[self._config.COLUMNS_DICT[bhav_universe]]
        elif bhav_universe in ["futidx_us_bhav", "futidx_extended_us_bhav"]:
            df["date"] = df.index.date
            df = df.drop_duplicates(subset=["ID"], keep="last")
            df.rename(
                columns={
                    "ID": "balte_id",
                    "Close": "close",
                    "Open": "open",
                    "High": "high",
                    "Low": "low",
                },
                inplace=True,
            )
            df["eod_price"] = df["close"]
            df = df.reset_index().set_index("date")
            df["instrument"] = bhav_universe[:-4].upper()
            df = df[self._config.COLUMNS_DICT[bhav_universe]]
        df.index = pd.to_datetime(df.index)
        return self._handle_data(bhav_universe, df)
