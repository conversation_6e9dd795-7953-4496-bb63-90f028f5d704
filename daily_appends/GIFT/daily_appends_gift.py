import datetime
from io import Bytes<PERSON>
from typing import Optional
import numpy as np
import pandas as pd
from main.config.config_GIFT import ConfigGift
from main.config.config_factory import ConfigFactory
from daily_appends.daily_appends_base import DailyAppendsBase

from main.data.utility import get_library_name, previous_date
from main.enums import StorageType


class DailyAppendsGIFT(DailyAppendsBase):
    def __init__(self, exchange_type: str) -> None:
        super().__init__(exchange_type=exchange_type)
        # For faster getfresh calls when data is not present in sampler. Make sure getfresh full day
        # completes before sampler resets.
        self._toti_obj.config.MAX_GET_LATEST_TIMESTAMP_ATTEMPTS = 1

    def __preprocess_bhav_data(self, bhav_universe, df, prev_date):
        if bhav_universe == "futidx_fut_gift_bhav":
            df.columns = [x.lower() for x in df.columns]
            df = self._extract_from_contract(symbol=bhav_universe, data=df)
            df["date"] = prev_date
            df = df[~df["close_pric"].isna()]
            df["balte_id"] = df.symbol.map(self._symbol_to_balte_id)
            df["expiry_rank"] = df.groupby("balte_id").expiry.rank(method="dense")
            df = df[df.expiry_rank == 1]
            df["eod_price"] = df["close_pric"]
            df = df.set_index("date")
            df = df[["balte_id", "eod_price"]]
            return df
        else:
            raise Exception(f"Bhavcopy handling not added for {bhav_universe}")

    def _extract_from_contract(self, symbol: str, data: pd.DataFrame):
        data["symbol"] = "GIFT_" + data["contract_d"].str[6:-11]
        data["series"] = data["contract_d"].str[:6]
        data["expiry"] = pd.to_datetime(data["contract_d"].str[-11:], format="%d-%b-%Y")

        return data

    def append_daily_data_1(
        self, universe: str, dtype: str = "trd", date: Optional[pd.Timestamp] = None
    ) -> bool:
        """Appends 1 min daily data for the given exchange (config)

        Args:
            universe (str): name of the universe

        Returns:
            bool: Returns True on successful daily appends
        """
        if date is None:
            date = self._config.DATE_TODAY
        balte_universe = self._config.UNIVERSE_TO_BALTE_UNIVERSE_MAPPING.get(
            universe, universe
        )
        data = self._toti_obj.get_fresh_1_full_day(
            balte_universe + "_onemin", date, dtype
        )

        library = get_library_name(
            exchange_name=self._config.EXCHANGE,
            frequency=1,
            universe_name=universe,
            dtype=dtype,
            storage=StorageType.DB,
        )

        if universe in ["futidx_fut", "futidx_raw"]:
            data["session_date"] = date
            # second session starts at 4:35 pm , its equivalent shifted is 13:35 pm
            mask = (data.index.time >= datetime.time(3, 30)) & (
                data.index.time <= datetime.time(13, 30)
            )
            data["session"] = 1
            data.loc[~mask, "session"] = 2

        data = self._handle_data(universe=universe, data=data, dtype=dtype)
        data["Symbol_ID"] = data["ID"] % 10000

        symbol_list = self._tanki[library].list_symbols()
        failed_syms = []
        for symbol_id, data_id in data.groupby("Symbol_ID"):
            try:
                data_id = data_id.drop(columns=["Symbol_ID"])
                self._push_data_to_db(
                    library_name=library,
                    symbol=symbol_id,
                    data=data_id,
                    date=date,
                    symbol_list=symbol_list,
                )
            except Exception as exception:
                failed_syms.append(f"{symbol_id}: {exception}")

        if len(failed_syms):
            raise Exception(
                "DailyAppendsError: Following symbols failed to append:\n"
                + "\n".join(failed_syms)
            )

        return True

    def append_raw_bhav_copy(self, symbol, date=None):
        if date is None:
            date = self._config.DATE_TODAY - pd.Timedelta(days=1)

        raw_symbol = f"raw_{symbol}"

        library_name = get_library_name(
            exchange_name=self._config.EXCHANGE,
            frequency=1440,
            universe_name=raw_symbol,
            dtype="trd",
            storage=StorageType.DB,
        )
        bhav_copy = pd.read_csv(
            BytesIO(
                self._get_from_minio(
                    file_location="commondata",
                    file_name=self._config.AFTER_MARKET_DICT_MINIO[symbol],
                ).data
            )
        )

        bhav_copy.columns = [col.lower() for col in bhav_copy.columns]

        bhav_copy["date"] = date
        bhav_copy = bhav_copy.set_index("date")

        bhav_copy = self._extract_from_contract(symbol=raw_symbol, data=bhav_copy)

        bhav_copy = self._handle_data(universe=raw_symbol, data=bhav_copy)

        symbol_list = self._tanki[library_name].list_symbols()

        failed_syms = []

        for symbol_bhav, symbol_bhav_df in bhav_copy.groupby("symbol"):
            try:
                self._push_data_to_db(
                    library_name=library_name,
                    symbol=symbol_bhav,
                    data=symbol_bhav_df,
                    date=date,
                    symbol_list=symbol_list,
                )
            except Exception as e:
                failed_syms.append(f"{symbol_bhav}: {e}")

        if len(failed_syms):
            raise Exception(
                f"DailyAppendsError: Following raw bhav symbols are failed to append:\n{failed_syms}"
            )

    def append_daily_data_1440(self, universe, timing, symbol=None):
        library_name = get_library_name(
            exchange_name=self._config.EXCHANGE,
            frequency=1440,
            universe_name=universe,
            dtype="trd",
            storage=StorageType.DB,
        )
        bhav_universe = symbol

        if timing == "morning":
            prev_date = previous_date(self._all_dates)

            bhav_copy = pd.read_csv(
                BytesIO(
                    self._get_from_minio(
                        file_location="commondata",
                        file_name=self._config.AFTER_MARKET_DICT_MINIO[bhav_universe],
                    ).data
                )
            )
            try:
                processed_copy = self.__preprocess_bhav_data(
                    bhav_universe,
                    bhav_copy,
                    prev_date,
                )

                processed_copy = self._handle_data(bhav_universe, processed_copy)

                self._push_data_to_db(
                    library_name=library_name,
                    symbol=symbol,
                    data=processed_copy,
                    date=prev_date,
                )
            except Exception as exception:
                raise Exception(
                    f"DailyAppendsError: {bhav_universe} failed to append due to {exception}\n"
                )

        elif timing == "evening":
            symbols_failed_to_append = []
            symbol_list = self._tanki[library_name].list_symbols()
            for symbol in self._config.AFTER_MARKET_DICT_GRPC:
                symbol_data = self._toti_obj.get_fresh_1440(
                    symbol, self._config.DATE_TODAY
                )
                if len(symbol_data) == 0:
                    continue
                try:
                    symbol_data = symbol_data.set_index("date")
                    symbol_data = self._handle_data(universe=symbol, data=symbol_data)
                    self._push_data_to_db(
                        library_name=library_name,
                        symbol=symbol,
                        data=symbol_data,
                        symbol_list=symbol_list,
                    )
                except Exception as exception:
                    symbols_failed_to_append.append(f"{symbol}: {exception}")

            if symbols_failed_to_append:
                raise Exception(
                    f"DailyAppendsError: Following symbols are failed to append:\n{symbols_failed_to_append}\n"
                )
        else:
            raise Exception("DailyAppendsError: Invalid market timing!")
