from multiprocessing import Pool
import os
import pandas as pd
import datetime
from arcticdb import Arctic
import pickle
from minio import Minio
from multiprocessing import Pool, Manager
from main.enums import StorageType
from main.storage.config import ARCTIC_CONFIG
from main.tanki import Tanki

tanki = Tanki(exchange_type="nse")
tanki.login(username="mantraraj", password="mantraraj")

storea = Arctic("s3://*************:9000:arctic-db?access=super&secret=doopersecret")
storek = Arctic(
    "s3://*************:9000:kivi-arcticdb?access=super&secret=doopersecret"
)

libraries_to_write = [
    # 'nse/1_min/cash/trd',
    # 'nse/5_min/cash/trd',
    # 'nse/1_min/fno/column_bucket',
    # 'nse/5_min/fno/column_bucket',
    # 'nse/1_min/eq/column_bucket',
    # 'nse/5_min/eq/column_bucket',
    # 'nse/1_min/fno/trd',
    # 'nse/5_min/fno/trd',
    # 'nse/1_min/eq/trd',
    # 'nse/5_min/eq/trd',
    # 'nse/1440_min/after_market/trd'
    # 'nse/1_min/fut_raw/trd',
    # 'nse/5_min/fut_raw/trd',
    # 'nse/1_min/fut_raw/column_bucket',
    # 'nse/5_min/fut_raw/column_bucket'
    "nse/1_min/futidx_fut/trd",
    "nse/5_min/futidx_fut/trd",
    "nse/1_min/futidx_fut/column_bucket",
    "nse/5_min/futidx_fut/column_bucket",
]

issues = {}


def write_to_kivi(xx):
    try:
        sym = xx[0]
        library = xx[1]

        lib1 = storea[library]
        lib2 = tanki[library]
        lib_delete = storek[library]

        lib_delete.delete(sym)

        df = lib1.read(sym).data
        lib2.write_metadata(sym, df)
        lib2.write(sym, df)

        print(f"Done for {sym} in {library}")

    except Exception as e:
        print(f"Problem with {sym} in {library}: {e}")
        if library not in issues:
            issues[library] = []
        issues[library].append(sym)


# write_to_kivi(('127','nse/1_min/fut/trd'))


# write_to_kivi(('408','nse/1_min/fut/trd'))
# write_to_kivi(('408','nse/5_min/fut/trd'))

# lib = tanki['nse/1_min/raw_fut/trd']

# for sym in lib.list_symbols():
#     if(lib.read(sym)['Cons_Volume'].isna().sum() > 0):
#         print(sym)

for library in libraries_to_write:
    lib1 = storea[library]
    lib2 = tanki[library]

    syms = lib1.list_symbols()
    syms_done = lib2.list_symbols()

    args = [(sym, library) for sym in syms]

    for arg in args:
        # sym = arg[0]
        # if(sym not in syms_done):
        #     write_to_kivi(arg)
        #     continue
        # elif(storek[library].read(sym).data is None):
        #     write_to_kivi(arg)
        # else:
        #     print(f"No Problem {sym} {library}")

        write_to_kivi(arg)

file_path = "/home/<USER>/data_auditing/moving_issues_fut_raw"

with open(file_path, "wb") as file:
    pickle.dump(issues, file)
