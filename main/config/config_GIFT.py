from typing import Dict, <PERSON>, Tuple, Union
from main.config.config_base import ConfigBase
from dataclasses import dataclass, field
from main.enums import ExchangeType, Check


@dataclass
class ConfigGift(ConfigBase):
    EXCHANGE = "gift"
    EXCHANGE_ENUM = ExchangeType.GIFT
    FILE_STORAGE = ""
    DB_STORAGE = ""
    FILE_DICT: Dict[str, Tuple[str, str]] = field(
        default_factory=lambda: {
            "ALL_DATES": ("commondata", "balte_uploads/ALL_DATES_GIFT.npy"),
            "MAPPING_DICT": ("commondata", "balte_uploads/mapping_dict"),
            "GIFT_EXPIRY_DICT": ("commondata", "balte_uploads/gift_expiry_dict"),
        }
    )

    MARKET_HOURS_DICT: Dict[str, Dict[str, str]] = field(
        default_factory=lambda: {
            "raw_futidx_fut": {"open": "3:30", "close": "23:45"},
            "futidx_fut": {"open": "3:30", "close": "23:45"},
            "futidx_raw": {"open": "3:30", "close": "23:45"},
            "fut_margin_gift": {"open": "3:30", "close": "23:45"},
            "raw_futidx_fut_gift_bhav": {"open": "3:30", "close": "23:45"},
            "futidx_fut_gift_bhav": {"open": "3:30", "close": "23:45"},
        }
    )

    PRE_DEPENDENCIES: Dict[str, List[str]] = field(
        default_factory=lambda: {
            "futidx_fut": ["raw_futidx_fut"],
            "raw_futidx_fut": [],
            "futidx_raw": ["raw_futidx_fut"],
            "futidx_fut_gift_bhav": ["raw_futidx_fut_gift_bhav"],
            "raw_futidx_fut_gift_bhav": [],
        }
    )

    POST_DEPENDENCIES: Dict[str, List[str]] = field(
        default_factory=lambda: {
            "raw_futidx_fut": ["futidx_fut", "futidx_raw"],
            "futidx_fut": [],
            "futidx_raw": [],
            "raw_futidx_fut_gift_bhav": ["futidx_fut_gift_bhav"],
            "futidx_fut_gift_bhav": [],
        }
    )

    COLUMNS_DICT: Dict[str, List[str]] = field(
        default_factory=lambda: {
            "raw_futidx_fut": [
                "timestamp",
                "ID",
                "expiry",
                "Open",
                "High",
                "Low",
                "Close",
                "Cons_Volume",
            ],
            "futidx_fut": [
                "timestamp",
                "ID",
                "Open",
                "High",
                "Low",
                "Close",
                "Cons_Volume",
                "session",
                "session_date",
            ],
            "futidx_raw": [
                "timestamp",
                "ID",
                "Open",
                "High",
                "Low",
                "Close",
                "Cons_Volume",
                "next_close",
                "session",
                "session_date",
            ],
            "fut_margin_gift": [
                "date",
                "ID",
                "span",
                "exposure",
            ],
            "raw_futidx_fut_gift_bhav": [
                "date",
                "symbol",
                "expiry",
                "series",
                "contract_d",
                "previous_s",
                "open_price",
                "high_price",
                "low_price",
                "close_pric",
                "settlement",
                "net_change",
                "oi_no_con",
                "traded_qua",
                "trd_no_con",
                "traded_val",
            ],
            "futidx_fut_gift_bhav": [
                "date",
                "balte_id",
                "eod_price",
            ],
        }
    )

    UNIVERSE_TO_DATA_TYPE_DICT_UPDATE_DICT: Dict[
        str, Dict[Union[str, int, Tuple[str, str]], str]
    ] = field(
        default_factory=lambda: {
            "raw_futidx_fut": {"ID": "object"},
        }
    )

    UNIVERSE_TO_AFTER_MARKET_SYMBOL_LIST: Dict[str, List[str]] = field(
        default_factory=lambda: {
            "raw_futidx_fut": [
                "symbol_change",
                "demerger_merger",
            ],
            "futidx_fut": ["repo_rate"],
        }
    )

    UNIVERSE_TO_MINIO_FILES_LIST: Dict[str, Dict[str, str]] = field(
        default_factory=lambda: {
            "futidx_fut_gift_bhav": {"expiry_dict": "GIFT_EXPIRY_DICT"},
        }
    )

    MODIFY_COLUMNS_OPERATIONS: Dict[str, List[str]] = field(
        default_factory=lambda: {
            "futidx_fut": [
                "apply_basis_adjustment",
            ],
        }
    )

    UNIVERSE_TO_IGNORE_CHECK_LIST: Dict[str, List[Check]] = field(
        default_factory=lambda: {
            "futidx_fut": [
                Check.CHECK_ALL_TIMESTAMPS,
                Check.CHECK_OVERNIGHT_SUDDEN_JUMPS,
            ],
            "raw_futidx_fut": [
                Check.CHECK_ALL_TIMESTAMPS,
            ],
        }
    )

    FILTER_DATA_OPERATIONS: Dict[str, List[str]] = field(
        default_factory=lambda: {
            "raw_futidx_fut": ["apply_symbol_change", "apply_demerger_merger"],
            "futidx_fut_gift_bhav": ["filter_near_contract"],
        }
    )

    FUTURE_LIST: List[str] = field(
        default_factory=lambda: [
            "raw_futidx_fut",
            "futidx_fut",
            "futidx_raw",
        ]
    )

    LOCAL_FILE_LOCATION = "gift/compilation"

    AFTER_MARKET_DICT_MINIO: Dict[str, str] = field(
        default_factory=lambda: {
            "futidx_fut_gift_bhav": "nse_ix_daily_downloads/bhavcopy_fo.csv",
            "raw_futidx_fut_gift_bhav": "nse_ix_daily_downloads/bhavcopy_fo.csv",
        }
    )

    AFTER_MARKET_DICT_GRPC: List[str] = field(
        default_factory=lambda: [
            "fut_margin_gift",
        ]
    )

    UNIVERSE_TO_BALTE_UNIVERSE_MAPPING: Dict[str, str] = field(
        default_factory=lambda: {
            "futidx_fut": "futidx_fut_gift",
            "futidx_raw": "futidx_raw_gift",
        }
    )
