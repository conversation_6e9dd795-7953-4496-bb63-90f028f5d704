## Factory function to get different types of exchange config file

from typing import Dict, Type
from main.config.config_GIFT import ConfigGift
from main.config.config_MCX import Config<PERSON><PERSON>
from main.config.config_base import ConfigBase
from main.config.config_NSE import Config<PERSON><PERSON>
from main.config.config_KRX import ConfigKRX
from main.config.config_BSE import ConfigBSE
from main.config.config_GIFT import ConfigGift
from main.config.config_US import ConfigUS

EXCHANGE_DICT: Dict[str, Type[ConfigBase]] = {
    "nse": ConfigNSE,
    "krx": ConfigKRX,
    "bse": ConfigBSE,
    "gift": ConfigGift,
    "us": ConfigUS,
    "mcx": ConfigMCX,
}


def ConfigFactory(exchange_type: str) -> ConfigBase:
    """
    Factory function to get different types of exchange level config

    Args:
        exchange_type (str): exchange

    Raises:
        Exception: Config class when not found for given exchange

    Returns:
        configBase: config class as per given exchange
    """
    try:
        config_class = EXCHANGE_DICT[exchange_type]()
    except Exception:
        raise Exception(f"ConfigError: Config class not found for {exchange_type}")

    return config_class
