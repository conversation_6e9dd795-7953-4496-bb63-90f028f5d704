## For exchange specific info

from main.config.config_base import ConfigBase
from dataclasses import dataclass, field
from typing import List, Dict, Tu<PERSON>, Union
from operator import mul, truediv
from main.enums import Check


@dataclass
class ConfigBSE(ConfigBase):
    EXCHANGE = "bse"
    EXCHANGE_ENUM = 7
    FILE_STORAGE = ""
    DB_STORAGE = ""
    FILE_DICT: Dict[str, Tuple[str, str]] = field(
        default_factory=lambda: {
            "ALL_DATES": ("commondata", "balte_uploads/ALL_DATES.npy"),
            "MAPPING_DICT": ("commondata", "balte_uploads/mapping_dict"),
        }
    )

    MARKET_HOURS_DICT: Dict[str, Dict[str, str]] = field(
        default_factory=lambda: {
            "raw_futidx": {"open": "9:15", "close": "15:30"},
            "raw_opt": {"open": "9:15", "close": "15:30"},
            "futidx": {"open": "9:15", "close": "15:30"},
            "opt": {"open": "9:15", "close": "15:30"},
            "raw_opt_oi": {"open": "9:15", "close": "15:30"},
        }
    )

    PRE_DEPENDENCIES: Dict[str, List[str]] = field(
        default_factory=lambda: {
            "raw_futidx": [],
            "futidx": ["raw_futidx", "raw_opt_oi"],
            "opt": ["raw_opt", "raw_futidx", "raw_opt_oi"],
            "raw_opt": [],
            "raw_opt_oi": [],
        }
    )
    POST_DEPENDENCIES: Dict[str, List[str]] = field(
        default_factory=lambda: {
            "raw_futidx": ["opt", "futidx"],
            "raw_opt": ["opt"],
            "raw_opt_oi": ["futidx", "opt"],
            "futidx": [],
            "opt": [],
        }
    )
    COLUMNS_DICT: Dict[str, List[str]] = field(
        default_factory=lambda: {
            "opt": [
                "timestamp",
                "ID",
                "Open_int",
                "High",
                "Low",
                "Close",
                "Cons_Volume",
                "iv",
                "delta",
                "gamma",
                "theta",
                "vega",
            ],
            "futidx": [
                "timestamp",
                "ID",
                "Open",
                "High",
                "Low",
                "Close",
                # "Vwap",
                "Cons_Volume",
                "pcr",
            ],
            "raw_opt": [
                "timestamp",
                "ID",
                "Open",
                "High",
                "Low",
                "Close",
                "Vwap",
                "Cons_Volume",
            ],
            "raw_futidx": [
                "timestamp",
                "ID",
                "Open",
                "High",
                "Low",
                "Close",
                "Vwap",
                "Cons_Volume",
            ],
            "issensex": ["date", "ID", 0],  # type: ignore
            "lotsize_index_bse": [
                "date",
                "ID",
                "near_month",
                "next_month",
                "near_week",
                "next_week",
            ],
            "lotsize_stk_bse": ["date", "ID", "near_month", "next_month"],
            "fut_margin_bse": ["date", "ID", "span", "exposure"],
            "opt_margin_bse": [
                "date",
                "ID",
                "contract_price",
                "R1",
                "R2",
                "R3",
                "R4",
                "R5",
                "R6",
                "R7",
                "R8",
                "R9",
                "R10",
                "R11",
                "R12",
                "R13",
                "R14",
                "R15",
                "R16",
                "delta",
            ],
            "optstk_margin_bse": [
                "date",
                "ID",
                "contract_price",
                "R1",
                "R2",
                "R3",
                "R4",
                "R5",
                "R6",
                "R7",
                "R8",
                "R9",
                "R10",
                "R11",
                "R12",
                "R13",
                "R14",
                "R15",
                "R16",
                "delta",
            ],
            "raw_fo_bhav": [
                "date",
                "instrument",
                "symbol",
                "expiry_dt",
                "strike_pr",
                "option_typ",
                "open",
                "high",
                "low",
                "close",
                "settle_pr",
                "contracts",
                "val_inlakh",
                "open_int",
                "chg_in_oi",
            ],
            "raw_cm_bhav": [
                "date",
                "symbol",
                "series",
                "open",
                "high",
                "low",
                "close",
                "last",
                "prevclose",
                "tottrdqty",
                "tottrdval",
                "isin",
            ],
            "cm_bhav": [
                "date",
                "symbol",
                "series",
                "open",
                "high",
                "low",
                "close",
                "last",
                "prevclose",
                "tottrdqty",
                "tottrdval",
                "isin",
                "balte_id",
                "close_raw",
                "eod_price",
                "adj_factor",
            ],
            "futidx_bhav": [
                "date",
                "instrument",
                "symbol",
                "expiry_dt",
                "strike_pr",
                "option_typ",
                "open",
                "high",
                "low",
                "close",
                "settle_pr",
                "contracts",
                "val_inlakh",
                "open_int",
                "chg_in_oi",
                "balte_id",
                "expiry_rank",
                "close_raw",
                "adj_factor",
                "eod_price",
            ],
            "futstk_bhav": [
                "date",
                "instrument",
                "symbol",
                "expiry_dt",
                "strike_pr",
                "option_typ",
                "open",
                "high",
                "low",
                "close",
                "settle_pr",
                "contracts",
                "val_inlakh",
                "open_int",
                "chg_in_oi",
                "balte_id",
                "expiry_rank",
                "close_raw",
                "adj_factor",
                "eod_price",
            ],
            "optidx_bhav": [
                "date",
                "instrument",
                "symbol",
                "expiry_dt",
                "strike_pr",
                "option_typ",
                "open",
                "high",
                "low",
                "close",
                "settle_pr",
                "contracts",
                "val_inlakh",
                "open_int",
                "chg_in_oi",
                "balte_id",
                "close_raw",
                "adj_factor",
                "expiry_rank",
                "eod_price",
            ],
            "optstk_bhav": [
                "date",
                "instrument",
                "symbol",
                "expiry_dt",
                "strike_pr",
                "option_typ",
                "open",
                "high",
                "low",
                "close",
                "settle_pr",
                "contracts",
                "val_inlakh",
                "open_int",
                "chg_in_oi",
                "balte_id",
                "close_raw",
                "adj_factor",
                "expiry_rank",
                "eod_price",
            ],
            "raw_opt_oi": ["timestamp", "ID", "Open_int"],
        }
    )

    UNIVERSE_TO_RENAME_COLUMN_DICT: Dict[str, Dict[Union[int, str], str]] = field(
        default_factory=lambda: {"lotsize_stk_bse": {0: "near_month", 1: "next_month"}}
    )

    UNIVERSE_TO_IGNORE_CHECK_LIST: Dict[str, List[Check]] = field(
        default_factory=lambda: {"opt": [Check.CHECK_ALL_TIMESTAMPS]}
    )

    OPTION_LIST: List[str] = field(
        default_factory=lambda: [
            "opt",
            "raw_opt",
            "raw_opt_oi",
        ]
    )

    SPOT_LIST: List[str] = field(
        default_factory=lambda: [
            "raw_futidx",
            "futidx",
        ]
    )

    OPTION_TO_UNDERLYING_MAP: Dict[str, str] = field(
        default_factory=lambda: {
            "opt": "futidx",
            "raw_opt": "raw_futidx",
        }
    )

    FUTURE_TO_UNDERLYING_MAP: Dict[str, str] = field(default_factory=lambda: {})

    UNDERLYING_TO_OPTION_MAP: Dict[str, str] = field(
        default_factory=lambda: {
            "futidx": "opt",
            "raw_futidx": "raw_opt",
        }
    )

    UNIVERSE_TO_BALTE_UNIVERSE_MAPPING: Dict[str, str] = field(
        default_factory=lambda: {
            "opt": "opt_bse",
            "futidx": "futidx_bse",
            "opt_oi": "opt_oi_bse",
            "cm_bhav": "cm_bhav_bse",
            "futidx_bhav": "futidx_bhav_bse",
            "optidx_bhav": "optidx_bhav_bse",
            "futstk_bhav": "futstk_bhav_bse",
            "optstk_bhav": "optstk_bhav_bse",
            "fo_bhav": "fo_bhav_bse",
        }
    )

    LOCAL_FILE_LOCATION = "bse/compilation"

    INTEREST_RATE = 0.1
    EXPIRY_TIME = 55800  ## 15:30 in seconds

    AFTER_MARKET_DICT_MINIO: Dict[str, str] = field(
        default_factory=lambda: {
            "optidx_bhav_bse": "bse_daily_downloads/bse_fo_bhav.csv",
            "optstk_bhav_bse": "bse_daily_downloads/bse_fo_bhav.csv",
            "futidx_bhav_bse": "bse_daily_downloads/bse_fo_bhav.csv",
            "futstk_bhav_bse": "bse_daily_downloads/bse_fo_bhav.csv",
            "cm_bhav_bse": "bse_daily_downloads/bse_cm_bhav.csv",
            "fo_bhav_bse": "bse_daily_downloads/bse_fo_bhav.csv",
        }
    )

    AFTER_MARKET_DICT_GRPC: List[str] = field(
        default_factory=lambda: [
            "fut_margin_bse",
            "issensex",
            "lotsize_index_bse",
            "lotsize_stk_bse",
            "opt_margin_bse",
            # "optstk_margin_bse",
        ]
    )
