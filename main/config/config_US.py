## For exchange specific info
import pytz
from main.config.config_base import ConfigBase
from dataclasses import dataclass, field
from typing import List, Dict, Tuple, Union

from main.data.utility import find_timezone_offset
from main.enums import ExchangeType, Check


@dataclass
class ConfigUS(ConfigBase):
    EXCHANGE = "us"
    EXCHANGE_ENUM = ExchangeType.INTERNATIONAL
    FILE_STORAGE = ""
    DB_STORAGE = ""
    FILE_DICT: Dict[str, Tuple[str, str]] = field(
        default_factory=lambda: {
            "ALL_DATES": ("international", "all_dates/ALL_DATES_US.npy"),
            "MAPPING_DICT": ("commondata", "balte_uploads/mapping_dict"),
            "SPXW_EXPIRY_DICT": ("international", "expiry_dict/spxw_expiry_dict"),
            "US_FUTURES_EXPIRY_DICT": (
                "international",
                "expiry_dict/us_futures_expiry_dict",
            ),
        }
    )

    MARKET_HOURS_DICT: Dict[str, Dict[str, str]] = field(
        default_factory=lambda: {
            "raw_opt": {"open": "2:15", "close": "23:00"},
            "raw_futidx_fut": {"open": "0:00", "close": "23:00"},
            "raw_futidx": {"open": "15:30", "close": "22:00"},
            "raw_futidx_extended": {"open": "0:10", "close": "23:00"},
            "opt": {"open": "2:15", "close": "23:00"},
            "futidx": {"open": "15:30", "close": "22:00"},
            "futidx_fut": {"open": "0:00", "close": "23:00"},
            "futidx_extended": {"open": "0:00", "close": "23:00"},
            "raw_futidx_ob": {"open": "0:00", "close": "23:59"},
            "futidx_ob": {"open": "0:00", "close": "23:59"},
        }
    )

    PRE_DEPENDENCIES: Dict[str, List[str]] = field(
        default_factory=lambda: {
            "opt": ["raw_opt", "raw_futidx"],
            "futidx": ["raw_futidx"],
            "futidx_fut": ["raw_futidx_fut"],
            "futidx_extended": ["raw_futidx_extended"],
            "raw_futidx_extended": [],
            "raw_futidx": [],
            "raw_opt": [],
            "raw_futidx_fut": [],
            "raw_futidx_ob": [],
            "futidx_ob": ["raw_futidx_ob"],
        }
    )

    POST_DEPENDENCIES: Dict[str, List[str]] = field(
        default_factory=lambda: {
            "opt": [],
            "futidx": [],
            "raw_futidx": ["opt", "futidx"],
            "raw_opt": ["opt"],
            "raw_futidx_fut": ["futidx_fut"],
            "futidx_fut": [],
            "raw_futidx_extended": ["futidx_extended"],
            "futidx_extended": [],
            "raw_futidx_ob": ["futidx_ob"],
            "futidx_ob": [],
        }
    )

    COLUMNS_DICT: Dict[str, List[str]] = field(
        default_factory=lambda: {
            "raw_opt": [
                "timestamp",
                "ID",
                "expiry",
                "option_type",
                "strike",
                "Open",
                "High",
                "Low",
                "Close",
                "Cons_Volume",
            ],
            "raw_futidx": [
                "timestamp",
                "ID",
                "Open",
                "High",
                "Low",
                "Close",
                "Cons_Volume",
            ],
            "raw_futidx_extended": [
                "timestamp",
                "ID",
                "Open",
                "High",
                "Low",
                "Close",
                "Cons_Volume",
            ],
            "raw_futidx_fut": [
                "timestamp",
                "ID",
                "symbol",
                "expiry",
                "Open",
                "High",
                "Low",
                "Close",
                "Cons_Volume",
            ],
            "opt": [
                "timestamp",
                "ID",
                "Open",
                "High",
                "Low",
                "Close",
                "Cons_Volume",
                "iv",
                "delta",
                "gamma",
                "theta",
                "vega",
            ],
            "futidx": [
                "timestamp",
                "ID",
                "Open",
                "High",
                "Low",
                "Close",
                "Cons_Volume",
            ],
            "futidx_extended": [
                "timestamp",
                "ID",
                "Open",
                "High",
                "Low",
                "Close",
                "Cons_Volume",
            ],
            "futidx_fut": [
                "timestamp",
                "ID",
                "Open",
                "High",
                "Low",
                "Close",
                "Cons_Volume",
            ],
            "futidx_us_bhav": [
                "instrument",
                "balte_id",
                "open",
                "high",
                "low",
                "close",
                "eod_price",
            ],
            "futidx_extended_us_bhav": [
                "instrument",
                "balte_id",
                "open",
                "high",
                "low",
                "close",
                "eod_price",
            ],
            "opt_us_bhav": [
                "instrument",
                "balte_id",
                "expiry_dt",
                "strike_pr",
                "option_type",
                "open",
                "high",
                "low",
                "close",
                "eod_price",
            ],
            "raw_futidx_ob": [
                "timestamp",
                "ID",
                "symbol",
                "expiry",
                "side",
                "level",
                "price",
                "quantity",
                "queue_depth",
            ],
            "futidx_ob": [
                "timestamp",
                "ID",
                "side",
                "level",
                "price",
                "quantity",
                "queue_depth",
            ],
        }
    )
    UNIVERSE_TO_DATA_TYPE_DICT_UPDATE_DICT: Dict[
        str, Dict[Union[str, int, Tuple[str, str]], str]
    ] = field(
        default_factory=lambda: {
            "raw_futidx": {"ID": "object", "expiry": "uint64"},
            "raw_futidx_extended": {"ID": "object", "expiry": "uint64"},
            "raw_opt": {"ID": "object", "expiry": "uint64"},
        }
    )
    UNIVERSE_TO_MINIO_FILES_LIST: Dict[str, Dict[str, str]] = field(
        default_factory=lambda: {
            "futidx_fut": {"expiry_dict": "SPXW_EXPIRY_DICT"},
            "raw_futidx_fut": {"expiry_dict": "SPXW_EXPIRY_DICT"},
            "raw_opt": {"expiry_dict": "US_FUTURES_EXPIRY_DICT"},
            "opt": {"expiry_dict": "US_FUTURES_EXPIRY_DICT"},
        }
    )
    UNIVERSE_TO_AFTER_MARKET_SYMBOL_LIST: Dict[str, List[str]] = field(
        default_factory=lambda: {
            "futidx_fut": [
                "repo_rate",
            ],
        }
    )

    MODIFY_COLUMNS_OPERATIONS: Dict[str, List[str]] = field(
        default_factory=lambda: {
            "futidx_fut": ["apply_basis_adjustment"],
        }
    )
    FILTER_DATA_OPERATIONS: Dict[str, List[str]] = field(
        default_factory=lambda: {
            "raw_futidx": ["apply_symbol_change", "apply_demerger_merger"],
            "raw_futidx_extended": ["apply_symbol_change", "apply_demerger_merger"],
            "futidx_fut": ["filter_near_contract"],
            "raw_opt": ["apply_symbol_change", "apply_demerger_merger"],
            "raw_futidx_fut": ["apply_symbol_change", "apply_demerger_merger"],
        }
    )

    OPTION_LIST: List[str] = field(
        default_factory=lambda: [
            "raw_opt",
            "opt",
        ]
    )

    FUTURE_LIST: List[str] = field(
        default_factory=lambda: [
            "raw_futidx_fut",
            "futidx_fut",
            "raw_futidx_ob",
            "futidx_ob",
        ]
    )

    SPOT_LIST: List[str] = field(
        default_factory=lambda: [
            "raw_futidx",
            "futidx",
            "futidx_extended",
            "raw_futidx_extended",
        ]
    )

    OPTION_TO_UNDERLYING_MAP: Dict[str, str] = field(
        default_factory=lambda: {"opt": "futidx", "raw_opt": "raw_futidx"}
    )

    UNDERLYING_TO_OPTION_MAP: Dict[str, str] = field(
        default_factory=lambda: {
            "futidx": "opt",
            "raw_futidx": "raw_opt",
        }
    )

    UNIVERSE_TO_BALTE_UNIVERSE_MAPPING: Dict[str, str] = field(
        default_factory=lambda: {
            "opt": "opt_us",
            "futidx": "futidx_us",
            "futidx_fut": "futidx_fut_us",
            "futidx_extended": "futidx_extended_us",
            "futidx_ob": "futidx_ob_us",
        }
    )

    UNIVERSE_TO_IGNORE_CHECK_LIST: Dict[str, List[Check]] = field(
        default_factory=lambda: {
            "futidx": [
                Check.CHECK_MONOTONIC_NATURE,
                Check.CHECK_NAN_ENTRIES,
                Check.CHECK_INTRADAY_SUDDEN_JUMPS,
                Check.CHECK_OVERNIGHT_SUDDEN_JUMPS,
                Check.CHECK_ALL_TIMESTAMPS,
            ],
            "futidx_extended": [
                Check.CHECK_NAN_ENTRIES,
                Check.CHECK_INTRADAY_SUDDEN_JUMPS,
                Check.CHECK_OVERNIGHT_SUDDEN_JUMPS,
                Check.CHECK_ALL_TIMESTAMPS,
            ],
            "opt": [
                Check.CHECK_MONOTONIC_NATURE,
                Check.CHECK_NAN_ENTRIES,
                Check.CHECK_ALL_TIMESTAMPS,
                Check.CHECK_INTRADAY_SUDDEN_JUMPS,
                Check.CHECK_OVERNIGHT_SUDDEN_JUMPS,
            ],
            "futidx_fut": [
                Check.CHECK_ALL_TIMESTAMPS,
                Check.CHECK_NAN_ENTRIES,
                Check.CHECK_INTRADAY_SUDDEN_JUMPS,
                Check.CHECK_OVERNIGHT_SUDDEN_JUMPS,
            ],
            "raw_futidx_fut": [
                Check.CHECK_ALL_DATES,
                Check.CHECK_ALL_TIMESTAMPS,
                Check.CHECK_NAN_ENTRIES,
                Check.CHECK_INTRADAY_SUDDEN_JUMPS,
                Check.CHECK_OVERNIGHT_SUDDEN_JUMPS,
            ],
        }
    )

    EXTRA_SYMBOL_TO_ID_DICT: Dict[str, int] = field(
        default_factory=lambda: {"EMININASDAQ": 5007}
    )

    IGNORE_COLUMNS_COLUMN_BUCKET_LIST: List[str] = field(
        default_factory=lambda: ["time", "ID", "date", "timestamp", "symbol", "expiry"]
    )

    LOCAL_FILE_LOCATION = "us/compilation"

    INTEREST_RATE = 0.0533
    EXPIRY_TIME = 79200  ## 22:00 in seconds
    TIMEZONE = find_timezone_offset(
        base_timezone=pytz.timezone("US/Eastern"), offset=360  # type: ignore
    )
