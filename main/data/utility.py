import io
import re
import pandas as pd
import pytz
import numpy as np
import datetime
from typing import Dict, List, Optional, Any, Union, Tuple, Callable
from main.enums import StorageType
import py_vollib.black_scholes.implied_volatility as impv  # type:ignore
import py_vollib.black.implied_volatility as impv_black  # type:ignore
from py_vollib.black_scholes.greeks.analytical import (  # type:ignore
    delta,
    gamma,
    theta,
    vega,
)
from py_vollib.ref_python.black_scholes import d1, d2  # type:ignore
from scipy import stats  # type:ignore
from py_vollib.black.greeks.analytical import (  # type:ignore
    delta as delta_black,
    gamma as gamma_black,
    theta as theta_black,
    vega as vega_black,
)
from py_vollib.ref_python.black import (  # type:ignore
    d1 as d1_black,
    d2 as d2_black,
)


@np.vectorize
def get_balte_id(
    sym_id: int,
    universe: str,
    expiry: int = 0,
    option_type: int = 2,
    strike: int = 0,
) -> int:
    """Creates balte_id for the option contract

    Args:
        sym_id (int): symbol id of the option contract
        universe (str): universe in which the option contract belongs
        expiry (int): expiry of the option contract
        option_type (int): type of the option contract
        strike (int): strike price of the option contract

    Returns:
        int: balte_id for the option contract
    """
    universe = universe.lower()
    balte_id = sym_id
    if universe in [
        "opt",
        "optidx",
        "opt_oi",
        "raw_opt",
        "raw_optidx",
        "raw_opt_oi",
        "optidx_bhav",
    ]:
        balte_id = int(
            strike * int(1e13) + expiry * int(1e5) + option_type * int(1e4) + sym_id
        )
    elif universe in [
        "optstk",
        "optstk_oi",
        "raw_optstk",
        "raw_optstk_oi",
        "optstk_bhav",
    ]:
        balte_id = int(
            strike * int(1e11)
            + (expiry % int(1e6)) * int(1e5)
            + option_type * int(1e4)
            + sym_id
        )
    elif universe in [
        "fut",
        "raw_fut",
        "futidx_fut",
        "raw_futidx_fut",
        "futidx_ob",
        "futstk_ob",
        "raw_futidx_ob",
        "raw_futstk_ob",
    ]:
        balte_id = int(expiry * int(1e5) + 2 * int(1e4) + sym_id)
    return balte_id


@np.vectorize
def handle_strike(balte_id: int, universe: str) -> float:
    """extract strike in Rs(or exchange specific currency) from options balte_id

    Args:
        balte_id (int): balte_id for the option contract
        universe (str): universe in which the balte_id belongs

    Returns:
        float: strike in Rs(or exchange specific currency)
    """

    universe = universe.lower()
    strike = 0.0
    if universe in ["opt", "opt_onemin"]:
        strike = float(balte_id // int(1e13))
    elif universe in ["optcom", "optidx_krx", "optidx_krx_onemin"]:
        strike = int(balte_id / int(1e13)) / 10
    elif universe == "optcur":
        strike = int(balte_id / int(1e13)) / 100
    elif universe in ["optstk", "optstk_onemin"]:
        strike = int(balte_id / int(1e11)) / 100

    return strike


@np.vectorize
def handle_expiry(balte_id: int, universe: str) -> int:
    """Extract expiry from options balte_id

    Args:
        balte_id (int): balte_id for the option contract
        universe (str): universe in which the balte_id belongs

    Returns:
        int: expiry of the contract
    """

    if universe.lower() in ["optstk", "optstk_onemin"]:
        expiry = int(2e7) + (int(balte_id % int(1e11)) // int(1e5))
    else:
        expiry = int(balte_id % int(1e13)) // int(1e5)

    return expiry


@np.vectorize
def get_iv(
    price: float,
    s: float,
    k: float,
    t: float,
    r: float,
    f: str,
    black: Optional[bool] = False,
) -> Union[float, Any]:
    """Calculates iv of an option

    Args:
        price (`float`): option price
        s (`float`): underlying price
        k (`float`): strike price
        t (`float`): time to expiration in years
        r (`float`): risk-free interest rate
        f (`str`):  'c' or 'p' for call or put.
        black (`bool`): if true use black 76 instead of black scholes
    Returns:
        Union[float,Any]: iv value
    """
    try:
        if black:
            return impv_black.implied_volatility(price, s, k, r, t, f)
        else:
            return impv.implied_volatility(price, s, k, t, r, f)
    except Exception as e:
        return np.nan


@np.vectorize
def get_delta(
    flag: str,
    s: float,
    k: float,
    t: float,
    r: float,
    sigma: float,
    black: Optional[bool] = False,
) -> Union[float, Any]:
    """Calculates delta value of option

    Args:
        flag (`str`):  'c' or 'p' for call or put.
        s (`float`): underlying asset price
        k (`float`): strike price
        t (`float`): time to expiration in years
        r (`float`): risk-free interest rate
        sigma (`float`): annualized standard deviation, or volatility
        black (`bool`): if true use black 76 instead of black scholes
    Returns:
        Union[float,Any]: Delta of option
    """
    try:
        if black:
            return delta_black(flag, s, k, t, r, sigma)
        else:
            return delta(flag, s, k, t, r, sigma)
    except Exception as e:
        return np.nan


@np.vectorize
def get_gamma(
    flag: str,
    s: float,
    k: float,
    t: float,
    r: float,
    sigma: float,
    black: Optional[bool] = False,
) -> Union[float, Any]:
    """Calculates gamma value of option

    Args:
        flag (`str`):  'c' or 'p' for call or put.
        s (`float`): underlying asset price
        k (`float`): strike price
        t (`float`): time to expiration in years
        r (`float`): risk-free interest rate
        sigma (`float`): annualized standard deviation, or volatility
        black (`bool`): if true use black 76 instead of black scholes
    Returns:
        Union[float,Any]: Gamma of option
    """
    try:
        if black:
            return gamma_black(flag, s, k, t, r, sigma)
        else:
            return gamma(flag, s, k, t, r, sigma)
    except Exception as e:
        return np.nan


@np.vectorize
def get_theta(
    flag: str,
    s: float,
    k: float,
    t: float,
    r: float,
    sigma: float,
    black: Optional[bool] = False,
) -> Union[float, Any]:
    """Calculates theta value of option

    Args:
        flag (`str`):  'c' or 'p' for call or put.
        s (`float`): underlying asset price
        k (`float`): strike price
        t (`float`): time to expiration in years
        r (`float`): risk-free interest rate
        sigma (`float`): annualized standard deviation, or volatility
        black (`bool`): if true use black 76 instead of black scholes
    Returns:
        Union[float,Any]: Theta of option
    """
    try:
        if black:
            return theta_black(flag, s, k, t, r, sigma)
        else:
            theta_greek = theta(flag, s, k, t, r, sigma)
            if theta_greek is not None:
                return theta_greek
            return np.nan
    except Exception as e:
        return np.nan


@np.vectorize
def get_vega(
    flag: str,
    s: float,
    k: float,
    t: float,
    r: float,
    sigma: float,
    black: Optional[bool] = False,
) -> Union[float, Any]:
    """Calculates vega value of option

    Args:
        flag (`str`):  'c' or 'p' for call or put.
        s (`float`): underlying asset price
        k (`float`): strike price
        t (`float`): time to expiration in years
        r (`float`): risk-free interest rate
        sigma (`float`): annualized standard deviation, or volatility
        black (`bool`): if true use black 76 instead of black scholes
    Returns:
        Union[float,Any]: Vega of option
    """
    try:
        if black:
            return vega_black(flag, s, k, t, r, sigma)
        else:
            return vega(flag, s, k, t, r, sigma)
    except Exception as e:
        return np.nan


@np.vectorize
def get_d1(
    s: float,
    k: float,
    t: float,
    r: float,
    sigma: float,
    black: Optional[bool] = False,
) -> Union[float, Any]:
    """Calculates d1 value of option

    Args:
        s (`float`): underlying asset price
        k (`float`): strike price
        t (`float`): time to expiration in years
        r (`float`): risk-free interest rate
        sigma (`float`): annualized standard deviation, or volatility
        black (`bool`): if true use black 76 instead of black scholes
    Returns:
        Union[float,Any]: d1 value of option
    """
    try:
        if black:
            return d1_black(s, k, t, r, sigma)
        else:
            return d1(s, k, t, r, sigma)
    except Exception as e:
        return np.nan


@np.vectorize
def get_d2(
    s: float,
    k: float,
    t: float,
    r: float,
    sigma: float,
    black: Optional[bool] = False,
) -> Union[float, Any]:
    """Calculates d2 value of option

    Args:
        s (`float`): underlying asset price
        k (`float`): strike price
        t (`float`): time to expiration in years
        r (`float`): risk-free interest rate
        sigma (`float`): annualized standard deviation, or volatility
        black (`bool`): if true use black 76 instead of black scholes
    Returns:
        Union[float,Any]: d2 value of option
    """
    try:
        if black:
            return d2_black(s, k, t, r, sigma)
        else:
            return d2(s, k, t, r, sigma)
    except Exception as e:
        return np.nan


@np.vectorize
def get_volga(
    vega: float,
    d1: float,
    d2: float,
    sigma: float,
) -> Union[float, Any]:
    """Calculates volga value of option

    Args:
        vega (`float`): vega value of option
        d1 (`float`): d1 value of option
        d2 (`float`): d2 value of option
        sigma (`float`): annualized standard deviation, or volatility
    Returns:
        Union[float,Any]: volga value of option
    """
    try:
        return (vega * 100 * d1 * d2) / sigma
    except Exception as e:
        return np.nan


@np.vectorize
def get_vanna(
    s: float,
    vega: float,
    d2: float,
    sigma: float,
    t: float,
) -> Union[float, Any]:
    """Calculates vanna value of option

    Args:
        s (`float`): underlying asset price
        vega (`float`): vega value of option
        d2 (`float`): d2 value of option
        sigma (`float`): annualized standard deviation, or volatility
        t (`float`): time to expiration in years
    Returns:
        Union[float,Any]: vanna value of option
    """
    try:
        return -1 * (vega * 100 / s) * (d2 / (sigma * (t**0.5)))
    except Exception as e:
        return np.nan


@np.vectorize
def get_charm(
    d1: float,
    d2: float,
    sigma: float,
    r: float,
    t: float,
) -> Union[float, Any]:
    """Calculates charm value of option

    Args:
        d1 (`float`): d1 value of option
        d2 (`float`): d2 value of option
        sigma (`float`): annualized standard deviation, or volatility
        r (`float`): risk-free interest rate
        t (`float`): time to expiration in years
    Returns:
        Union[float,Any]: charm value of option
    """
    try:
        return stats.norm.pdf(d1) * (
            (d2 * sigma - 2 * r * (t**0.5)) / (2 * sigma * t)
        )
    except Exception as e:
        return np.nan


def get_library_name(
    exchange_name: str,
    frequency: int,
    universe_name: str,
    dtype: str,
    storage: StorageType,
) -> str:
    """returns the library name depending on the type of storage

    Args:
        exchange_name (str): name of exchange
        frequency (int): frequency i.e. 1 or 5
        universe_name (str): name of universe
        dtype (str): type of data i.e. ord, trd, column_bucket
        storage (StorageType): StorageType.DB or StorageType.LOCAL

    Raises:
        Exception: raise Exception when storage type is not StorageType.DB or StorageType.LOCAL

    Returns:
        str: library name
    """

    if storage == StorageType.LOCAL:
        return f"{exchange_name}_{str(frequency)}min_{universe_name}_{dtype}"
    elif storage == StorageType.DB:
        return f"{exchange_name}/{str(frequency)}_min/{universe_name}/{dtype}"
    else:
        raise Exception(
            "UtilityError: Invalid storage type in get_library_name method!"
        )


def compare_floats(a: float, b: float) -> bool:
    """checks whether two float values are equal or not using epsilon approach

    Args:
        a (float): first number to compare
        b (float): second number to compare

    Returns:
        bool: True if equal otherwise False
    """

    epsilon = 1e-5
    return abs(a - b) < epsilon


def column_bucket_casting(
    universe: str,
    column_name: str,
    data: pd.DataFrame,
    data_types_dict: Dict[Union[str, int], str],
) -> pd.DataFrame:
    """used to update the data type of the column bucket as per data_types_dict

    Args:
        universe (str): name of universe
        column_name (str): name of column
        data (pd.DataFrame): column_bucket of the column_name having no columns in index
        data_types_dict (Dict[Union[str, int], str]): dictionary having key as column name and value as data type of key

    Raises:
        Exception: raise Exception if column_name is not present in data_types_dict

    Returns:
        pd.DataFrame: data with the updated data type having no columns in index
    """
    set_index = False
    if ("date" in data.columns) and ("ID" in data.columns):
        data["ID"] = data["ID"].astype(dtype="uint64")
        data["date"] = data["date"].astype(dtype="datetime64[ns]")
        data = data.set_index(["date", "ID"])
        set_index = True

    if column_name in data_types_dict:
        # checking the data type of only first column, as in column bucket all columns are of same attribute
        if data.dtypes.iloc[0] != data_types_dict[column_name]:
            data = data.astype(
                dtype=np.dtype(data_types_dict[column_name]),
                copy=True,
            )
    else:
        raise Exception(
            f"UtilityError: Invalid column_name: {column_name} in column_bucket_casting method!"
        )

    if set_index:
        return data.reset_index()
    return data


def _parse_report(report: str) -> Dict[str, Dict[str, float]]:
    """
    Parses a report string to extract information about intraday jumps, overnight jumps, and NaN values.

    Args:
        report (str): A string containing the report data.

    Returns:
        dict[str, dict[str, float]]: A dictionary containing four keys:
            - 'intraday_jump_dict': A dictionary of columns with their highest intraday percentage jumps.
            - 'overnight_jump_open_dict': A dictionary of columns with their highest overnight open percentage jumps.
            - 'overnight_jump_close_dict': A dictionary of columns with their highest overnight close percentage jumps.
            - 'nan_value_dict': A dictionary of columns with their highest NaN value percentages.
    """
    intraday_jump_dict: Dict[str, float] = {}
    overnight_jump_open_dict: Dict[str, float] = {}
    overnight_jump_close_dict: Dict[str, float] = {}
    nan_value_dict: Dict[str, float] = {}

    with io.StringIO(report) as file:
        current_segment: Optional[str] = None
        for line in file:
            line = line.strip()
            if line.startswith("check_nan_entries"):
                current_segment = "nan"
            elif line.startswith("check_intraday_sudden_jumps"):
                current_segment = "intraday"
            elif line.startswith("check_overnight_sudden_jumps"):
                current_segment = "overnight"
            elif current_segment == "intraday":
                match = re.match(
                    r"(.+?) data for which (.+?) percentage change is greater than allowed (.+?) with highest jump: (.+?) on (.+?)",
                    line,
                )
                if match:
                    _, column, _, highest, _ = match.groups()
                    intraday_jump_dict[column] = abs(float(highest))
            elif current_segment == "overnight":
                match_close = re.match(
                    r"(.+?) data for which (.+?) percentage change in current day last price to previous day last price is greater than allowed (.+?) with highest jump: (.+?) on (.+?)",
                    line,
                )
                match_open = re.match(
                    r"(.+?) data for which (.+?) percentage change in current day first price to previous day last price is greater than allowed (.+?) with highest jump: (.+?) on (.+?)",
                    line,
                )
                if match_close:
                    _, column, _, highest, _ = match_close.groups()
                    overnight_jump_close_dict[column] = abs(float(highest))
                elif match_open:
                    _, column, _, highest, _ = match_open.groups()
                    overnight_jump_open_dict[column] = abs(float(highest))
            elif current_segment == "nan":
                match = re.match(
                    r"Found NaN values > (.+?)% for (.+?) dates for the column: (.+?), with highest NaN count of (.+?)% on (.+?)",
                    line,
                )
                if match:
                    _, _, column, value, _ = match.groups()
                    nan_value_dict[column] = float(value)

    return {
        "intraday_jump_dict": intraday_jump_dict,
        "overnight_jump_open_dict": overnight_jump_open_dict,
        "overnight_jump_close_dict": overnight_jump_close_dict,
        "nan_value_dict": nan_value_dict,
    }


# This will create metadata from the checked_messages
# 3 attributes, length, start_timestamp and last_timestamp should be handled separately
def create_metadata_from_report(
    column_list: List[str], checked_messages: str
) -> Dict[str, Union[str, Dict[str, float]]]:
    """Creates metadata from the checked messages.

    Args:
        column_list: A list of column names to process.
        checked_messages: A string containing the checked messages.

    Returns:
        A Dictionary containing metadata for each column, including nan_ratio,
        intraday_jump, overnight_open_jump, and overnight_close_jump. Also includes
        length, start_timestamp, and last_timestamp. If 'corpact_applied' is in
        checked_messages, it adds the last_corpact_applied date.
    """
    metadata_dict: Dict[str, Union[str, Dict[str, float]]] = {}
    parsed_data = _parse_report(report=checked_messages)

    for column in column_list:
        intraday_jump = parsed_data["intraday_jump_dict"].get(column, 0) / 100
        overnight_open_jump = (
            parsed_data["overnight_jump_open_dict"].get(column, 0) / 100
        )
        overnight_close_jump = (
            parsed_data["overnight_jump_close_dict"].get(column, 0) / 100
        )
        nan_ratio = parsed_data["nan_value_dict"].get(column, 0) / 100

        metadata_dict[column] = {
            "nan_ratio": nan_ratio,
            "intraday_jump": intraday_jump,
            "overnight_open_jump": overnight_open_jump,
            "overnight_close_jump": overnight_close_jump,
        }

    metadata_dict["length"] = "0"
    metadata_dict["start_timestamp"] = metadata_dict["last_timestamp"] = ""

    if "corpact_applied" in checked_messages:
        metadata_dict["last_corpact_applied"] = str(pd.Timestamp.today().date())

    return metadata_dict


def previous_date(
    all_dates: List[pd.Timestamp], day: Optional[pd.Timestamp] = None, lookback: int = 1
) -> pd.Timestamp:
    """Finds the previous working date from a list of dates.

    Args:
        all_dates: A list of pandas Timestamp objects representing all available dates.
        day: A pandas Timestamp object representing the day from which to find the previous date.
             If None, the current date is used.
        lookback: An integer indicating how many previous working days to look back.

    Returns:
        A pandas Timestamp object representing the previous working date.

    Raises:
        IndexError: If there are not enough previous dates in the list to satisfy the lookback.
    """
    if day is None:
        day = pd.Timestamp.now().normalize()

    previous_working_dates = [x for x in all_dates if day > x]
    if len(previous_working_dates) < lookback:
        raise IndexError(
            f"Previous working date not found for {day} with lookback {lookback}"
        )
    previous_working_date = previous_working_dates[-lookback]
    return previous_working_date


def parse_to_date(date: str) -> pd.Timestamp:
    """
    Parses a string to a pandas Timestamp.

    This function attempts to parse a date string into a pandas Timestamp using the specified formats.
    If parsing is successful, it returns the parsed Timestamp.

    Args:
        date (str): The date string to be parsed.

    Returns:
        pd.Timestamp: The parsed date as a pandas Timestamp, or pd.NaT if parsing fails.
    """
    attempt_formats = ["%d-%m-%Y", "%d-%b-%Y"]

    ## If exception arises - today's date will be returned
    parsed_date = pd.Timestamp.now().normalize()

    for format in attempt_formats:
        try:
            parsed_date = pd.Timestamp(datetime.datetime.strptime(str(date), format))
            return parsed_date
        except Exception as e:
            print(f"Failed to parse date : {date}, format : {format}")
    return parsed_date


def calculate_corpact_adj_factor(
    df: pd.DataFrame,
    df_bhav: pd.DataFrame,
    date: pd.Timestamp,
    all_dates: List[pd.Timestamp],
) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Main function to calculate adjustment factor based on corporate actions.

    Args:
        df (pd.DataFrame): Corporate Action DataFrame with columns:
            ["symbol", "face_value", "purpose", "exdate"].
        df_bhav (pd.DataFrame): Bhavcopy DataFrame with columns:
            ["symbol", "timestamp", "close"].
        date (pd.Timestamp): Date for which corporate action is to be calculated.
        all_dates (List[pd.Timestamp]): List of all dates.

    Returns:
        Tuple[pd.DataFrame, pd.DataFrame]: A tuple containing:
            - Corporate Action DataFrame with columns:
                ["exdate", "type", "adj_factor", "symbol"].
            - DataFrame of bad corporate actions with columns:
                ["Bad Corpact"].
    """
    yesterday = previous_date(all_dates, date)
    df_bhav["timestamp"] = pd.to_datetime(df_bhav.timestamp)
    df_bhav = df_bhav.set_index(["timestamp", "symbol"])
    df_bhav_series = df_bhav.xs(yesterday).close

    exdates_1 = pd.to_datetime(df["exdate"], format="%d-%m-%Y", errors="coerce")
    exdates_2 = pd.to_datetime(df["exdate"], format="%d-%b-%Y", errors="coerce")
    df["exdate"] = exdates_1.fillna(exdates_2)

    df = df[~pd.isna(df.exdate)]
    df["prev_close"] = df.symbol.map(df_bhav_series)
    df["face_value"] = df["face_value"].astype(int)
    df["adj_factor"] = 1
    df["corp_act_type"] = ""

    ## No Corporate action for debentures, hence excluding all debenture purposes:
    df = df[~df.purpose.str.lower().str.contains("debenture")]

    ## Find adjustment factor
    pat1 = re.compile(r"div(\.|-| |ided|ded|dend|idend)|special")
    pat2 = re.compile(r"sp\w*t")
    pat3 = re.compile("bonus")
    pat4 = re.compile("right")
    corpact_texts = [pat1, pat2, pat3, pat4]
    row_list = []
    bad_corpact_list = []

    for _, row in df.iterrows():
        cppos = []
        cptype = []
        corpacts_str = row.purpose.lower()
        for c in range(len(corpact_texts)):
            for m in corpact_texts[c].finditer(corpacts_str):
                cppos.append(m.start())
                cptype.append(c)
        if not cppos:
            continue
        sorted_pairs = sorted(zip(cppos, cptype))
        cppos = [pos for pos, _ in sorted_pairs]
        cptype = [ptype for _, ptype in sorted_pairs]

        rights_info = []
        for cr in range(len(cppos)):
            if cr == len(cppos) - 1:
                posstart = cppos[cr]
                posend = len(corpacts_str)
            else:
                posstart = cppos[cr]
                posend = cppos[cr + 1]
            num_array = []
            for m in re.finditer(r"\d+\.\d+|\d+", corpacts_str[posstart:posend]):
                num_array.append(float(m.group()))
                temp_str = corpacts_str[posstart:posend][m.end() :].strip()
                if temp_str and temp_str[0] == "%":
                    num_array[-1] = (num_array[-1] / 100) * row.face_value
            if not num_array:
                continue
            if cptype[cr] == 0:
                if len(num_array) != 1:
                    bad_corpact_list.append(
                        f"Bad dividend for corporate action:{row.symbol} {row.purpose}"
                    )
                    continue
                row["adj_factor"] = (row["prev_close"] - num_array[0]) / row[
                    "prev_close"
                ]
                row["corp_act_type"] = "DIVIDEND"
            elif cptype[cr] == 1:
                if len(num_array) != 2:
                    bad_corpact_list.append(
                        f"Bad split for corporate action:{row.symbol} {row.purpose}"
                    )
                    continue
                row["adj_factor"] = num_array[1] / num_array[0]
                row["corp_act_type"] = "SPLIT"
            elif cptype[cr] == 2:
                if len(num_array) != 2:
                    bad_corpact_list.append(
                        f"Bad bonus for corporate action:{row.symbol} {row.purpose}"
                    )
                    continue
                row["adj_factor"] = num_array[1] / (num_array[0] + num_array[1])
                row["corp_act_type"] = "BONUS"
            elif cptype[cr] == 3:
                if len(num_array) == 3 or len(num_array) == 2:
                    if len(num_array) == 3:
                        rights_info.append(
                            [
                                num_array[0],
                                num_array[1],
                                (num_array[2] + row.face_value),
                                row.prev_close,
                            ]
                        )
                    elif len(num_array) == 2:
                        rights_info.append(
                            [num_array[0], num_array[1], row.face_value, row.prev_close]
                        )
                else:
                    bad_corpact_list.append(
                        f"Bad rights for corporate action:{row.symbol} {row.purpose}"
                    )
            if cptype[cr] != 3:
                row_list.append(row.copy())
        if rights_info:
            lcm_ratio = 1
            for rrow in range(len(rights_info)):
                lcm_ratio = np.lcm(int(lcm_ratio), int(rights_info[rrow][1]))
            additional_equity = []
            exitsting_equity = []
            row_1 = []
            for rrow in range(len(rights_info)):
                rights_info[rrow][0] = (rights_info[rrow][0] * lcm_ratio) / rights_info[
                    rrow
                ][1]
                rights_info[rrow][1] = lcm_ratio
                additional_equity.append(rights_info[rrow][0] * rights_info[rrow][2])
                exitsting_equity.append(rights_info[rrow][1] * rights_info[rrow][3])
                row_1.append(rights_info[rrow][0])
            aeq: float = np.nansum(additional_equity)
            eeq = rights_info[0][1] * rights_info[0][3]
            r1: float = np.nansum(row_1)
            row["adj_factor"] = ((eeq + aeq) / (r1 + rights_info[0][1])) / row[
                "prev_close"
            ]
            row["corp_act_type"] = "RIGHTS"
            row_list.append(row.copy())
    df = pd.DataFrame(row_list)
    bad_corpact_df = pd.DataFrame(bad_corpact_list, columns=["Bad Corpact"])

    if df.empty:
        print(f"No corpact for {date}, returning")
        return pd.DataFrame(
            columns=["exdate", "type", "adj_factor", "symbol"]
        ), pd.DataFrame(columns=["Bad Corpact"])
    # Skim for future dates
    df = df[df["exdate"] >= date]

    # drop nans and negative adj_factor
    df = df[~df.adj_factor.isna()]
    df = df[df.adj_factor > 0]

    # Eliminate all rights and bonus issue with adj_factor >= 1
    df = df[~(df.corp_act_type.isin(["RIGHTS", "BONUS"]) & (df.adj_factor >= 1))]

    df["type"] = df.corp_act_type
    df["inst_date"] = date
    df = df.drop_duplicates(["symbol", "adj_factor", "corp_act_type"])

    # skim to keep only required columns
    df = df[["exdate", "type", "adj_factor", "symbol"]]

    return df, bad_corpact_df


def get_merger_demerger(corpact_df: pd.DataFrame) -> pd.DataFrame:
    """Parses the corpact DataFrame to extract records related to mergers and demergers.

    This function filters the input DataFrame to include only those rows where the 'purpose'
    column contains keywords indicative of merger or demerger activities, such as 'erge',
    'arrang', or 'arngment'. The 'purpose' column is converted to lowercase to ensure
    case-insensitive matching.

    Args:
        corpact_df (pd.DataFrame): A DataFrame containing corporate action records.

    Returns:
        pd.DataFrame: A filtered DataFrame containing only merger and demerger records.
    """
    corpact_df["purpose"] = corpact_df["purpose"].str.lower()
    merger_demerger_df = corpact_df[
        (corpact_df.purpose.str.contains("erge"))
        | (corpact_df.purpose.str.contains("arrang"))
        | (corpact_df.purpose.str.contains("arngment"))
    ]
    return merger_demerger_df


def stack_column_bucket_data(df: pd.DataFrame, name: str) -> pd.DataFrame:
    """Stacks the column bucket data containing date and ID as index and time string
    as columns to create a dataframe with timestamp as index and ID, passed name as columns.

    Args:
        df (pd.DataFrame): The column bucket dataframe with date and ID as index and time string as columns.
        name (str): The name of the final column in the resulting dataframe.

    Returns:
        pd.DataFrame: A dataframe with timestamp as index, ID, and the passed name as columns.
    """
    df_series: pd.Series[Any] = pd.Series(df.stack(dropna=False))
    df_series.name = name
    df = df_series.reset_index()
    df["level_2"] += ":00"
    df["level_2"] = pd.to_timedelta(df.level_2)
    df["date"] = pd.to_datetime(df["date"])
    df["timestamp"] = df.date + df.level_2
    df = df.drop(columns=["level_2", "date"])
    df = df.sort_values(["timestamp", "ID"]).set_index("timestamp")
    return df


def round_to_nearest_lotsize(df: pd.DataFrame, lotsize: pd.DataFrame) -> pd.DataFrame:
    """
    Rounds the specified columns in the DataFrame to the nearest lot size.

    Args:
        df (pd.DataFrame): The input DataFrame containing the data to be rounded.
        lotsize (pd.DataFrame): The DataFrame containing the lot sizes for each date.

    Returns:
        pd.DataFrame: The DataFrame with the specified columns rounded to the nearest lot size.
    """
    if not isinstance(df.index, pd.DatetimeIndex):
        df.index = pd.to_datetime(df.index)
    df["date"] = pd.to_datetime(df.index.date)
    lotsize = lotsize[lotsize.index.isin(df["date"])]
    symbol_data = df.reset_index().set_index("date")

    for col in ["near_month", "next_month", "far_month"]:
        symbol_data[col] = np.round(symbol_data[col].div(lotsize[col], axis=0))
        symbol_data[col] = np.maximum(symbol_data[col], 1).mul(lotsize[col], axis=0)

    symbol_data = symbol_data.reset_index().set_index("timestamp").drop(columns="date")
    df.drop(columns="date", inplace=True)
    mask = (df == 0) | (symbol_data.isna())
    df.where(mask, symbol_data, inplace=True)
    # Round to nearest integer if lotsize is not present
    df = df.round()
    return df


def round_to_nearest_lotsize_column_bucket(
    df: pd.DataFrame, lotsize_df: pd.DataFrame, col: str
) -> pd.DataFrame:
    """
    Rounds the values in a specified column of column bucket to the nearest lot size,
    based on a provided lot size DataFrame.

    Args:
        df (pd.DataFrame): The input DataFrame containing the values to be rounded.
        lotsize_df (pd.DataFrame): The DataFrame containing the lot sizes.
        col (str): The column name in the lotsize_df to use for rounding.

    Returns:
        pd.DataFrame: The DataFrame with values rounded to the nearest lot size.
    """
    lotsize_df = lotsize_df[lotsize_df.index.isin(df.index.get_level_values("date"))]
    lotsize_df = lotsize_df.reset_index().set_index(["date", "ID"])
    symbol_data = np.round(df.div(lotsize_df[col], axis=0))
    symbol_data = np.maximum(symbol_data, 1).mul(lotsize_df[col], axis=0)
    # This is needed so that 0 OI values are not rounded to 1 lotsize
    # and OI is not lost if lotsize is not present
    mask = (df == 0) | (np.isnan(symbol_data))
    df.where(mask, symbol_data, inplace=True)
    # Round to nearest integer if lotsize is not present
    df = df.round()
    return df


def find_timezone_offset(
    base_timezone: Callable[[str], pytz.BaseTzInfo], offset: int
) -> Any:
    """Finds the timezone offset between the base timezone and IST, accounting for daylight saving time and additional offset.

    This function calculates the timezone offset dynamically, taking into account daylight saving time adjustments
    for the base timezone and an additional offset in minutes. It is particularly useful for time zones that
    observe daylight saving time, such as US time zones.

    Args:
        base_timezone (pytz.timezone): The base timezone for which the offset is calculated.
        offset (int): An additional offset in minutes to be applied to the calculated offset.

    Returns:
        pytz.FixedOffset: A timezone offset object representing the calculated offset in minutes.

    Raises:
        ValueError: If the timezone offset calculation results in None.
    """
    ist: pytz.BaseTzInfo = pytz.timezone("Asia/Kolkata")
    now: datetime.datetime = datetime.datetime.now()
    ist_now: datetime.datetime = ist.localize(now)
    base_timezone_now: datetime.datetime = base_timezone.localize(now)  # type: ignore
    ist_offset: Optional[datetime.timedelta] = ist_now.utcoffset()
    base_offset: Optional[datetime.timedelta] = base_timezone_now.utcoffset()
    if ist_offset is None or base_offset is None:
        raise ValueError("Timezone offset calculation resulted in None")
    current_offset_minutes: int = int((ist_offset - base_offset).total_seconds() / 60)
    current_offset_minutes -= offset
    current_offset_minutes = 330 - current_offset_minutes
    return pytz.FixedOffset(current_offset_minutes)


def split_dates_by_gap(
    dates: List[pd.Timestamp], lookback: int, all_dates: List[pd.Timestamp]
) -> List[List[pd.Timestamp]]:
    """
    Breaks the given date list into [start, end] date periods if there's a
    continuous gap of more than the specified lookback number of days.

    Args:
        dates (List[pd.Timestamp]): A list of dates.
        lookback (int): The lookback period in days.
        all_dates (List[pd.Timestamp]): A list of all available dates for reference.

    Returns:
        List[List[pd.Timestamp]]: A list of [start, end] date periods.
    """

    res_list: List[List[pd.Timestamp]] = []
    temp_list: List[pd.Timestamp] = []  # Contains only two values -- start and end time

    if not dates:
        return res_list

    prev_date: pd.Timestamp = previous_date(all_dates, dates[0], lookback)
    temp_list.append(prev_date)

    for i in range(len(dates)):
        timediff: pd.Timedelta = dates[i] - prev_date
        if timediff.days > lookback:
            lookback_date: pd.Timestamp = previous_date(all_dates, dates[i], lookback)
            if (prev_date is not None) and (
                previous_date(all_dates, prev_date, -1) < lookback_date
            ):
                temp_list.append(prev_date)
                res_list.append(temp_list)
                temp_list = [lookback_date]
        if i == len(dates) - 1:
            temp_list.append(dates[i])
            res_list.append(temp_list)
            temp_list = []
        prev_date = dates[i]
    return res_list
