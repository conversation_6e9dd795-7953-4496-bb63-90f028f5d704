## For validation checks on data stored in local storage

import pandas as pd
import numpy as np
from typing import Any, Callable, List, Optional, Dict, Sequence, Union, cast
from main.data.auditor import Auditor
from main.enums import StorageType, Check
from main.decorators import load_data
from main.config.config_base import ConfigBase
from main.enums import StorageType
from main.data.utility import previous_date


class Checker:
    """Class for performing validation checks on data stored across different storage types.

    This class uses an Auditor instance to access data and includes a comprehensive series
    of checks to validate the integrity and quality of the data. Each check is mapped to a
    corresponding method that performs the validation.

    Attributes:
        _config (ConfigBase): Configuration object containing settings for data validation.
        __auditor (Auditor): Auditor instance for reading data and metadata from different storage types.
        checks (List[Check]): List of available validation check enums.
        checks_to_fxn_mapping (Dict[Check, Callable]): Mapping of validation check enums to their corresponding methods.
    """

    def __init__(self, config: ConfigBase, auditor: Optional[Auditor] = None):
        """Initializes the Checker with a configuration object.

        Args:
            config (ConfigBase): Configuration object containing settings for data validation.
            auditor (Optional[Auditor]): An optional Auditor instance. If not provided, a new Auditor object will be created.
                Defaults to None.
        """
        self._config: ConfigBase = config
        if auditor is None:
            auditor = Auditor(config=config)
        self.__auditor = auditor
        # list of checks
        self.checks: List[Check] = [check for check in Check]

        # checks mapped functions
        self.checks_to_fxn_mapping = {
            check: getattr(self, check.value) for check in self.checks
        }

    def check_data_operations(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
    ) -> str:
        """
        Validates the data operations by performing a series of checks. Raises an exception if any check fails.

        Args:
            storage_type (StorageType): The type of storage for the file.
            file_location (str): The location of the Arctic library.
            file_name (str): The name of the symbol.
            data (pd.DataFrame): The user-provided DataFrame to write, append, or update.

        Raises:
            Exception: If any data check fails.

        Returns:
            str: A message indicating that all checks have passed.
        """

        collective_check_messages = self._check(
            storage_type=storage_type,
            file_location=file_location,
            file_name=file_name,
            data=data,
        )
        universe = file_location.split("/")[2]
        check_list: List[Check] = self._config.UNIVERSE_TO_CHECK_LIST.get(
            universe, self.checks
        )
        ignore_checks: List[Check] = self._config.UNIVERSE_TO_IGNORE_CHECK_LIST.get(
            universe, []
        )
        checks_to_perform = list(set(check_list) - set(ignore_checks))

        for checks in checks_to_perform:
            pattern: str = f"{checks.value}:\nPassed\n"
            if pattern not in collective_check_messages:
                raise Exception(
                    f"DataChecksFailed: Data inconsistencies found for the symbol: {file_name}.\n{collective_check_messages}"
                )

        return collective_check_messages

    def check_data(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        data: Optional[pd.DataFrame] = None,
        check_list: Optional[Sequence[Union[str, Check]]] = None,
        ignore_checks: Optional[Sequence[Union[str, Check]]] = None,
    ) -> str:
        """Checks the data for specified conditions.

        This method loads the data from the specified storage location and
        performs a series of checks on it. The checks to be performed and ignored
        can be specified via the `check_list` and `ignore_checks`
        parameters, respectively.

        Args:
            storage_type (StorageType): The type of storage where the file is located.
            file_location (str): The location of the Arctic library.
            file_name (str): The name of the file or symbol.
            data (Optional[pd.DataFrame], optional): The data to be checked. If None,
                the dataset will be loaded from the specified storage location.
                Defaults to None.
            check_list (Optional[List[Union[str, Check]]], optional): A list of checks to be
                performed on the data. If None, all available checks will be performed.
                Defaults to None.
            ignore_checks (Optional[List[Union[str, Check]]], optional): A list of checks to be
                ignored during the data validation process. Defaults to None.

        Returns:
            str: A concatenated string of messages from all performed checks.
        """

        if data is None:
            data = self.__auditor.read(
                storage_type=storage_type,
                file_location=file_location,
                file_name=file_name,
            )

        return self._check(
            storage_type=storage_type,
            file_location=file_location,
            file_name=file_name,
            data=data,
            check_list=check_list,
            ignore_checks=ignore_checks,
        )

    def _check(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
        check_list: Optional[Sequence[Union[str, Check]]] = None,
        ignore_checks: Optional[Sequence[Union[str, Check]]] = None,
    ) -> str:
        """Executes specified data checks.

        This method performs a series of checks on the provided data based on the specified
        check list and ignores any checks specified in the ignore list. If no checks are
        specified, it defaults to performing all available checks.

        Args:
            storage_type (StorageType): The type of storage for the file.
            file_location (str): The location of the Arctic library.
            file_name (str): The name of the file or symbol.
            data (pd.DataFrame): The data corresponding to the symbol in the library.
            check_list (Optional[List[Union[str, Check]]]): A list of checks to be performed on the data.
                If 'all' is specified, all checks are performed. Argument defaults to None but all the checks
                are performed in that case too.
            ignore_checks (Optional[List[Union[str, Check]]]): A list of checks to be ignored. If 'all' is
                specified, all checks are ignored. Defaults to None.

        Returns:
            str: A concatenated string of messages from all performed checks.
        """
        check_list = ["all"] if check_list is None else check_list
        check_list_final: List[Check] = []
        if check_list == ["all"]:
            check_list_final = self.checks
        elif check_list is not None:
            for item in check_list:
                if isinstance(item, Check):
                    check_list_final.append(item)
                elif isinstance(item, str):
                    try:
                        check_list_final.append(Check(item))
                    except ValueError:
                        raise ValueError(f"Invalid check value: {item}")

        ignore_checks_final: List[Check] = []
        if ignore_checks == ["all"]:
            ignore_checks_final = self.checks
        elif ignore_checks is not None:
            for item in ignore_checks:
                if isinstance(item, Check):
                    ignore_checks_final.append(item)
                elif isinstance(item, str):
                    try:
                        ignore_checks_final.append(Check(item))
                    except ValueError:
                        raise ValueError(f"Invalid Ignore check value: {item}")

        checks_to_perform = [
            check for check in check_list_final if check in self.checks
        ]
        checks_to_perform = [
            check for check in checks_to_perform if check not in ignore_checks_final
        ]

        if not checks_to_perform:
            return "No checks done!\n"

        collective_all_checks_message = ""

        for check in checks_to_perform:
            selected_check_function = self.checks_to_fxn_mapping[check]
            collective_all_checks_message += selected_check_function(
                storage_type=storage_type,
                file_location=file_location,
                file_name=file_name,
                data=data,
            )

        return collective_all_checks_message

    def _compare_ordered_column_lists(
        self, data_column_list: List[str], expected_column_list: List[str]
    ) -> str:
        """Compares two ordered lists of column names and returns a message detailing any differences.

        This method checks if the actual column list matches the expected column list in terms of
        both order and content. It identifies any extra columns in the actual list, any missing
        columns, and any discrepancies in the number of columns.

        Args:
            data_column_list (List[str]): The list of column names present in the data.
            expected_column_list (List[str]): The list of column names expected by the library.

        Returns:
            str: A message detailing any differences found between the actual and expected column lists.
        """

        if data_column_list == expected_column_list:
            return ""

        data_column_set = set(data_column_list)
        expected_column_set = set(expected_column_list)
        extra_column_in_data = list(data_column_set - expected_column_set)
        missing_column_in_data = list(expected_column_set - data_column_set)

        checked_messages = ""

        if len(data_column_list) != len(expected_column_list):
            checked_messages += f"Should have {len(expected_column_list)} but found {len(data_column_list)} columns\n"

        if extra_column_in_data:
            checked_messages += f"Found following extra column(s) in data: {sorted(extra_column_in_data)}\n"

        if missing_column_in_data:
            checked_messages += f"Found following missing column(s) in data: {sorted(missing_column_in_data)}\n"

        if not checked_messages:
            checked_messages += "Unexpected order of the columns found\n"

        return checked_messages

    @cast(Callable[..., Callable[..., str]], load_data)
    def check_columns_set(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
    ) -> str:
        """Checks if all columns are present in the correct order in the data and returns messages accordingly.

        Args:
            storage_type (StorageType): The type of storage for the file.
            file_location (str): The Arctic library location.
            file_name (str): The symbol name.
            data (pd.DataFrame): The data corresponding to the file_name in file_location.

        Returns:
            str: Collective messages after columns-set checks.
        """

        checked_messages = "check_columns_set:\n"

        # file_location: "exchange/frequency/universe/dtype", so getting universe from 2nd index
        file_location_split = file_location.split("/")
        universe = file_location_split[2]
        dtype = file_location_split[-1]
        universe_dtype = f"{universe}_{dtype}"
        col_list = data.columns.values.tolist()
        index_list = list(data.index.names)
        # including index column names as well
        data_column_list = index_list + col_list
        expected_column_list = []

        if (
            universe in self._config.OPTION_LIST
            or universe in self._config.FUTURE_LIST
            or universe in self._config.SPOT_LIST
        ):
            col_list = self._config.UNIVERSE_DTYPE_TO_COLUMN_DICT.get(
                universe_dtype, self._config.COLUMNS_DICT[universe]
            )

            expected_column_list = col_list
            checked_messages += self._compare_ordered_column_lists(
                data_column_list=data_column_list,
                expected_column_list=expected_column_list,
            )
        else:
            checked_messages += (
                f"Unexpected universe: {universe}, so columns can't be checked\n"
            )

        if checked_messages.count("\n") == 1:
            checked_messages += "Passed\n"

        checked_messages += "\n"
        return checked_messages

    @cast(Callable[..., Callable[..., str]], load_data)
    def check_columns_dtype(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
    ) -> str:
        """Checks if the DataFrame has the expected column data types and returns a summary of the results.

        This method verifies that each column in the DataFrame, including index columns, matches the expected data types
        defined in the configuration. It generates a message indicating whether each column's data type is as expected or
        if there are any discrepancies.

        Args:
            storage_type (StorageType): The type of storage for the file.
            file_location (str): The location of the Arctic library.
            file_name (str): The name of the file or symbol.
            data (pd.DataFrame): The DataFrame containing the data for the specified file.

        Returns:
            str: A summary message indicating the results of the data type checks.
        """

        checked_messages = "check_columns_dtype:\n"

        universe = file_location.split("/")[2]

        col_types_dict = data.dtypes
        # including index columns too
        for index_name in data.index.names:
            col_types_dict[index_name] = data.index.get_level_values(index_name).dtype

        data_types_dict = self._config.DATA_TYPES_DICT.copy()
        if universe in self._config.UNIVERSE_TO_DATA_TYPE_DICT_UPDATE_DICT:
            for (
                column,
                data_type,
            ) in self._config.UNIVERSE_TO_DATA_TYPE_DICT_UPDATE_DICT[universe].items():
                data_types_dict[column] = data_type

        for key, value in col_types_dict.items():
            if str(key) not in data_types_dict:
                checked_messages += (
                    f"Unexpected column: {key}, so dtype can't be checked\n"
                )

            elif value != data_types_dict[str(key)]:
                checked_messages += f"Unexpected dtype for column: {key}, since expected {data_types_dict[str(key)]} but got {value}\n"

        if checked_messages.count("\n") == 1:
            checked_messages += "Passed\n"

        checked_messages += "\n"
        return checked_messages

    @cast(Callable[..., str], load_data)
    def check_intraday_sudden_jumps(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
    ) -> str:
        """Check for intraday sudden jumps in OHLC (Open, High, Low, Close) data.

        This method calculates the percentage change for each OHLC column and compares it against a threshold.
        The threshold is determined by either a default value or a value specified in the metadata, multiplied by a factor.
        It also checks if the percentage change exceeds a certain percentage of the average percentage change for the specific ID and date combination.

        Args:
            storage_type (StorageType): The type of storage for the file.
            file_location (str): The location of the Arctic library.
            file_name (str): The symbol name of the file.
            data (pd.DataFrame): The data corresponding to the file_name and file_location.

        Returns:
            str: A collective message summarizing the results of the sudden jumps check.
        """

        checked_messages = "check_intraday_sudden_jumps:\n"
        messages_to_be_added = ""

        data = data.reset_index()

        for col in self._config.COLUMN_JUMPS_CHECK:
            if col not in data.columns:
                checked_messages += "Passed\n"
                return checked_messages

        metadata: Optional[
            Dict[str, Union[str, Dict[str, float]]]
        ] = self.__auditor.read_metadata(
            storage_type=storage_type, file_location=file_location, file_name=file_name
        )

        if metadata is None:
            checked_messages += f"MetadataReadError: Found error during reading metadata_dict from {file_name} located at {file_location} inside store: {storage_type}\n"

        for col in self._config.COLUMN_JUMPS_CHECK:
            data[f"{col}_pct_change"] = (
                data.groupby([data["timestamp"].dt.date, "ID"])[col].pct_change().abs()
                * 100
            )

            allowed_intraday_sudden_jump = self._config.ALLOWED_SUDDEN_JUMP

            if metadata is not None and col not in metadata:
                checked_messages += f"Column: {col} not in metadata for {file_location} with symbol = {file_name}\n"
            elif metadata is not None:
                col_data = metadata[col]
                if isinstance(col_data, dict):
                    allowed_intraday_sudden_jump = col_data.get(
                        "intraday_jump", self._config.ALLOWED_SUDDEN_JUMP
                    )

            allowed_intraday_sudden_jump *= 100

            allowed_sudden_jumps_exceeded_data_percent = (
                (
                    data[f"{col}_pct_change"] - allowed_intraday_sudden_jump
                    > self._config.ALLOWED_JUMP_INCREASE * 100
                ).sum()
                * 100
            ) / len(data)

            if allowed_sudden_jumps_exceeded_data_percent > 0:
                highest_jump_index = data[f"{col}_pct_change"].idxmax()
                checked_messages += f"{allowed_sudden_jumps_exceeded_data_percent}% data for which {col} percentage change is greater than allowed {allowed_intraday_sudden_jump}% with highest jump: {data.loc[highest_jump_index][f'{col}_pct_change']} on {data.loc[highest_jump_index]['timestamp']}\n"

        threshold_all: pd.DataFrame = (
            data.groupby([data["timestamp"].dt.date, "ID"])[
                [
                    "Open_pct_change",
                    "High_pct_change",
                    "Low_pct_change",
                    "Close_pct_change",
                ]
            ].transform("mean")
            * self._config.ALLOWED_OHLC_INTRADAY_AVERAGE_MULTIPLIER
        )

        threshold_o_exceeded_bool_arr = (
            data["Open_pct_change"] > threshold_all["Open_pct_change"]
        )
        threshold_o_exceeded_percent = (
            threshold_o_exceeded_bool_arr.sum() * 100 / len(data)
        )
        threshold_h_exceeded_bool_arr = (
            data["High_pct_change"] > threshold_all["High_pct_change"]
        )
        threshold_h_exceeded_percent = (
            threshold_h_exceeded_bool_arr.sum() * 100 / len(data)
        )
        threshold_l_exceeded_bool_arr = (
            data["Low_pct_change"] > threshold_all["Low_pct_change"]
        )
        threshold_l_exceeded_percent = (
            threshold_l_exceeded_bool_arr.sum() * 100 / len(data)
        )
        threshold_c_exceeded_bool_arr = (
            data["Close_pct_change"] > threshold_all["Close_pct_change"]
        )
        threshold_c_exceeded_percent = (
            threshold_c_exceeded_bool_arr.sum() * 100 / len(data)
        )

        values = [
            threshold_o_exceeded_percent,
            threshold_h_exceeded_percent,
            threshold_l_exceeded_percent,
            threshold_c_exceeded_percent,
        ]

        newline_count = 0
        if any(value > self._config.DATA_JUMPS_THRESHOLD for value in values):
            newline_count += 4
            messages_to_be_added += f"{threshold_o_exceeded_percent}% data for which open percentage change is {self._config.ALLOWED_OHLC_INTRADAY_AVERAGE_MULTIPLIER} times greater than average open percentage change for the specific ID and Date combination\n"
            messages_to_be_added += f"{threshold_h_exceeded_percent}% data for which high percentage change is {self._config.ALLOWED_OHLC_INTRADAY_AVERAGE_MULTIPLIER} times greater than average high percentage change for the specific ID and Date combination\n"
            messages_to_be_added += f"{threshold_l_exceeded_percent}% data for which low percentage change is {self._config.ALLOWED_OHLC_INTRADAY_AVERAGE_MULTIPLIER} times greater than average low percentage change for the specific ID and Date combination\n"
            messages_to_be_added += f"{threshold_c_exceeded_percent}% data for which close percentage change is {self._config.ALLOWED_OHLC_INTRADAY_AVERAGE_MULTIPLIER} times greater than average close percentage change for the specific ID and Date combination\n"

        if messages_to_be_added.count("\n") == newline_count:
            checked_messages += "Passed\n"

        checked_messages += messages_to_be_added
        checked_messages += "\n"
        return checked_messages

    @cast(Callable[..., Callable[..., str]], load_data)
    def check_duplicate_entries(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
    ) -> str:
        """Checks for duplicate entries in the DataFrame, both with and without considering the index.

        This function checks for exact duplicate rows, including the index, and also checks for rows
        that have the same 'timestamp' and 'ID' (and optionally 'expiry') but differ in other columns.

        Args:
            storage_type (StorageType): The type of storage for the file.
            file_location (str): The location of the Arctic library.
            file_name (str): The name of the file or symbol.
            data (pd.DataFrame): The DataFrame containing the data for the specified file.

        Returns:
            A string containing messages summarizing the results of the duplicate checks.
        """
        checked_messages = "check_duplicate_entries:\n"

        data = data.reset_index()
        duplicated_data_count_exact_with_time = data.duplicated().sum()

        if duplicated_data_count_exact_with_time > 0:
            checked_messages += f"{duplicated_data_count_exact_with_time} duplicate entries with timestamp + row\n"

        diff_data_on_same_timestamp_id_bool_arr = data.duplicated(
            subset=["timestamp", "ID"]
            + (["expiry"] if "expiry" in data.columns else []),
            keep="first",
        )
        diff_data_on_same_timestamp_id_count = (
            diff_data_on_same_timestamp_id_bool_arr.sum()
        )

        if diff_data_on_same_timestamp_id_count > 0:
            checked_messages += f"{diff_data_on_same_timestamp_id_count} different entries for same timestamp + ID\n"

        data = data.set_index("timestamp")

        if checked_messages.count("\n") == 1:
            checked_messages += "Passed\n"

        checked_messages += "\n"
        return checked_messages

    def _nan_after_non_null(self, series: "pd.Series[Any]") -> bool:
        """
        Checks if there are any NaN values in the series after the first non-null value.

        Args:
            series (pandas Series): A pandas Series object to be checked.

        Returns:
            bool: True if there is at least one NaN value after the first non-null value, False otherwise.
        """
        first_non_null_pos = series.first_valid_index()

        # Handle both cases: all NaN or no valid index
        if first_non_null_pos is None:
            return False  # type: ignore[unreachable]

        # Get position and check for NaNs after
        pos = series.index.get_loc(first_non_null_pos)
        return series.iloc[pos + 1 :].isna().any()

    @cast(Callable[..., Callable[..., str]], load_data)
    def check_forward_fill(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
    ) -> str:
        """Checks forward filling in specified columns of a DataFrame.
        Handles different columns with specific logic.

        Args:
            storage_type (StorageType): The type of storage for the file.
            file_location (str): The location of the Arctic library.
            file_name (str): The name of the symbol.
            data (pd.DataFrame): The DataFrame containing data for the specified file.

        Returns:
            str: A collective message indicating the results of the forward fill check.
        """
        checked_messages = "check_forward_fill:\n"

        data = data.reset_index()
        for column in self._config.COLUMN_FFILL_CHECK:
            if column in data:
                if column == "OI" or column == "Open_int":
                    ffill = (
                        data.groupby("ID")[column].apply(self._nan_after_non_null).sum()
                    )
                    if ffill > 0:
                        checked_messages += f"Column: {column} is not forward filled\n"

        if checked_messages.count("\n") == 1:
            checked_messages += "Passed\n"

        checked_messages += "\n"
        return checked_messages

    @cast(Callable[..., Callable[..., str]], load_data)
    def check_monotonic_nature(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
    ) -> str:
        """Checks the monotonic nature of specified columns in the DataFrame.

        This method checks if the specified columns in the DataFrame are monotonically increasing.
        Special handling is applied for the 'Cons_Volume' column, which is checked for monotonicity
        within groups defined by 'ID' and optionally 'expiry', with a daily frequency.

        Args:
            storage_type (StorageType): The type of storage for the file.
            file_location (str): The location of the Arctic library.
            file_name (str): The name of the symbol.
            data (pd.DataFrame): The DataFrame containing data for the specified file.

        Returns:
            str: A message indicating the results of the monotonicity checks.
        """

        checked_messages = "check_monotonic_nature:\n"

        for column in self._config.COLUMN_MONOTONIC_CHECK:
            if column in data:
                if column == "Cons_Volume":
                    data_without_nan = data.dropna(subset=["Cons_Volume"])
                    if "expiry" in data.columns:
                        result = data_without_nan.groupby(
                            ["ID", "expiry", pd.Grouper(freq="D")]
                        )["Cons_Volume"].is_monotonic_increasing
                    else:
                        result = data_without_nan.groupby(["ID", pd.Grouper(freq="D")])[
                            "Cons_Volume"
                        ].is_monotonic_increasing

                    if np.any(~result):
                        checked_messages += (
                            f"Column: {column} is not monotonically increasing\n"
                        )

        if checked_messages.count("\n") == 1:
            checked_messages += "Passed\n"

        checked_messages += "\n"
        return checked_messages

    @cast(Callable[..., Callable[..., str]], load_data)
    def check_nan_entries(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
    ) -> str:
        """Checks the percentage of NaN entries for each column in the DataFrame.
        The function compares the NaN ratio against the `nan_ratio` specified in the metadata
        or defaults to `DEFAULT_NAN_RATIO` if the metadata is not available.

        Args:
            storage_type (StorageType): The type of storage for the file.
            file_location (str): The location of the Arctic library.
            file_name (str): The name of the symbol.
            data (pd.DataFrame): The data corresponding to the symbol in the library.

        Returns:
            str: A summary message detailing the results of the NaN entry checks.
        """
        checked_messages = "check_nan_entries:\n"

        data = data.reset_index()
        date_wise_data_list = [
            (date, group) for date, group in data.groupby(data["timestamp"].dt.date)
        ]
        metadata = self.__auditor.read_metadata(
            storage_type=storage_type, file_location=file_location, file_name=file_name
        )

        if metadata is None:
            checked_messages += f"MetadataReadError: Found error during reading metadata_dict from {file_name} located at {file_location} inside store: {storage_type}\n"

        for column in data.columns:
            allowed_nan_ratio = self._config.DEFAULT_NAN_RATIO

            if metadata is not None and column not in metadata:
                checked_messages += f"Column: {column} not in metadata for {file_location} with symbol = {file_name}\n"
            elif metadata is not None:
                col_data = metadata[column]
                if isinstance(col_data, dict):
                    allowed_nan_ratio = col_data.get("nan_ratio", allowed_nan_ratio)

            exceed_nan_date_count = 0
            date_wise_max_nan_count_percent: float = 0
            max_nan_count_date = None
            for date, date_wise_data in date_wise_data_list:
                total_count: int = len(date_wise_data)
                nan_count = cast(int, date_wise_data[column].isna().sum())
                allowed_nan_count = (
                    allowed_nan_ratio + self._config.ALLOWED_NAN_INCREASE
                ) * total_count

                if nan_count > allowed_nan_count:
                    exceed_nan_date_count += 1
                    date_wise_max_nan_count_percent = max(
                        date_wise_max_nan_count_percent, (nan_count / total_count) * 100
                    )
                    max_nan_count_date = date

            if exceed_nan_date_count > 0:
                checked_messages += f"Found NaN values > {(allowed_nan_ratio + self._config.ALLOWED_NAN_INCREASE) * 100}% for {exceed_nan_date_count} dates for the column: {column}, with highest NaN count of {date_wise_max_nan_count_percent}% on {max_nan_count_date}\n"

        if checked_messages.count("\n") == 1:
            checked_messages += "Passed\n"

        checked_messages += "\n"
        return checked_messages

    # TODO: Needs to handle for updating the data
    @cast(Callable[..., str], load_data)
    def check_overnight_sudden_jumps(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
    ) -> str:
        """
        Evaluates overnight data for specified columns to detect sudden price jumps between the last close of the previous day and the first open of the current day, as well as between consecutive closes.

        The function performs the following steps:
        - Computes the percentage change between the last close of the previous day and the first open of the current day, and between consecutive closes.
        - Compares these percentage changes against allowed thresholds, which can be specified in the metadata or default to a base configuration.
        - Calculates the mean percentage change for each column and compares individual data points against these means, scaled by a multiplier..

        Args:
            storage_type (StorageType): The type of storage for the file.
            file_location (str): The location of the file.
            file_name (str): The name of the file.
            data (pd.DataFrame): The new data to be appended corresponding to the file_name of file_location.

        Returns:
            str: A detailed message summarizing the results of the sudden jump checks.
        """
        checked_messages = "check_overnight_sudden_jumps:\n"
        messages_to_be_added = ""

        data = data.reset_index()

        for col in self._config.COLUMN_JUMPS_CHECK:
            if col not in data.columns:
                checked_messages += "Passed\n"
                return checked_messages

        metadata = self.__auditor.read_metadata(
            storage_type=StorageType.DB,
            file_location=file_location,
            file_name=file_name,
        )

        df_previous_date_data: Optional[pd.DataFrame] = None

        if metadata is None or "last_timestamp" not in metadata:
            checked_messages += f"Metadata is none or doesn't have last_timestamp for {file_location} with symbol = {file_name}\n"
        else:
            try:
                all_dates_object = self.__auditor.read(
                    storage_type=StorageType.FILE,
                    file_location=self._config.FILE_DICT["ALL_DATES"][0],
                    file_name=self._config.FILE_DICT["ALL_DATES"][1],
                )
                date = data["timestamp"].iloc[0].normalize()
                previous_date_of_data = previous_date(
                    all_dates=all_dates_object,
                    day=date,
                )
                if (
                    metadata["last_timestamp"] != ""
                    and pd.Timestamp(cast(str, metadata["last_timestamp"]))
                    >= previous_date_of_data
                ):
                    df_previous_date_data = self.__auditor.read(
                        storage_type=storage_type,
                        file_location=file_location,
                        file_name=file_name,
                        start_date=previous_date_of_data,
                        end_date=date,
                    ).reset_index()
            except IndexError as ie:
                pass
            except Exception:
                raise Exception(
                    f"ReadError: Found error during reading {file_name} at {file_location} from store {storage_type} in check_overnight_sudden_jumps"
                )

        if df_previous_date_data is not None:
            data = pd.concat([df_previous_date_data, data])
        data = data[["timestamp", "ID"] + self._config.COLUMN_JUMPS_CHECK]
        new_data = pd.DataFrame()

        for col in self._config.COLUMN_JUMPS_CHECK:
            new_data[f"{col}_first"] = data.groupby(
                [data["timestamp"].dt.date, "ID"]
            ).agg({col: "first"})
            new_data[f"{col}_last"] = data.groupby(
                [data["timestamp"].dt.date, "ID"]
            ).agg({col: "last"})

        new_data = new_data.reset_index()

        for col in self._config.COLUMN_JUMPS_CHECK:
            new_data[f"{col}_first"] = (
                new_data.groupby("ID")[f"{col}_first"]
                .shift(-1)
                .fillna(new_data[f"{col}_first"])
            )
            new_data[f"{col}_last_shift"] = (
                new_data.groupby("ID")[f"{col}_last"]
                .shift(-1)
                .fillna(new_data[f"{col}_last"])
            )

            allowed_overnight_open_jump = self._config.ALLOWED_SUDDEN_JUMP
            allowed_overnight_close_jump = self._config.ALLOWED_SUDDEN_JUMP

            if metadata is not None and col not in metadata:
                checked_messages += f"Column: {col} not in metadata for {file_location} with symbol = {file_name}\n"
            elif metadata is not None:
                col_data = metadata[col]
                if isinstance(col_data, dict):
                    allowed_overnight_open_jump = col_data.get(
                        "overnight_open_jump", self._config.ALLOWED_SUDDEN_JUMP
                    )
                    allowed_overnight_close_jump = col_data.get(
                        "overnight_close_jump", self._config.ALLOWED_SUDDEN_JUMP
                    )

            allowed_overnight_open_jump *= 100
            allowed_overnight_close_jump *= 100

            new_data[f"{col}_interday_diff_percent"] = (
                abs(pd.Series(new_data[f"{col}_last"] - new_data[f"{col}_first"]))
                / pd.Series(new_data[[f"{col}_last", f"{col}_first"]].min(axis=1))
            ) * 100
            allowed_overnight_open_jump_exceeded_data_percent = (
                (
                    new_data[f"{col}_interday_diff_percent"]
                    - allowed_overnight_open_jump
                    > self._config.ALLOWED_JUMP_INCREASE * 100
                ).sum()
                * 100
            ) / len(new_data)
            if allowed_overnight_open_jump_exceeded_data_percent > 0:
                highest_open_jump_index = new_data[
                    f"{col}_interday_diff_percent"
                ].idxmax()
                checked_messages += f"{allowed_overnight_open_jump_exceeded_data_percent}% data for which {col} percentage change in current day first price to previous day last price is greater than allowed {allowed_overnight_open_jump}% with highest jump: {new_data.loc[highest_open_jump_index][f'{col}_interday_diff_percent']} on {data.loc[highest_open_jump_index]['timestamp']}\n"
            new_data[f"{col}_interday_last_last_diff_percent"] = (
                pd.Series(abs(new_data[f"{col}_last"] - new_data[f"{col}_last_shift"]))
                / pd.Series(new_data[[f"{col}_last", f"{col}_last_shift"]].min(axis=1))
            ) * 100
            allowed_overnight_close_jump_exceeded_data_percent = (
                (
                    new_data[f"{col}_interday_last_last_diff_percent"]
                    - allowed_overnight_close_jump
                    > self._config.ALLOWED_JUMP_INCREASE * 100
                ).sum()
                * 100
            ) / len(new_data)
            if allowed_overnight_close_jump_exceeded_data_percent > 0:
                highest_close_jump_index = new_data[
                    f"{col}_interday_last_last_diff_percent"
                ].idxmax()
                checked_messages += f"{allowed_overnight_close_jump_exceeded_data_percent}% data for which {col} percentage change in current day last price to previous day last price is greater than allowed {allowed_overnight_close_jump}% with highest jump: {new_data.loc[highest_close_jump_index][f'{col}_interday_last_last_diff_percent']} on {data.loc[highest_close_jump_index]['timestamp']}\n"

            new_data[f"{col}_interday_diff_percent_mean"] = (
                new_data.groupby("ID")[f"{col}_interday_diff_percent"].transform("mean")
                * self._config.ALLOWED_OHLC_INTERDAY_AVERAGE_MULTIPLIER
            )
            new_data[f"{col}_interday_last_last_diff_percent_mean"] = (
                new_data.groupby("ID")[
                    f"{col}_interday_last_last_diff_percent"
                ].transform("mean")
                * self._config.ALLOWED_OHLC_INTERDAY_AVERAGE_MULTIPLIER
            )

        o_interday_fail_bool_arr = (
            new_data["Open_interday_diff_percent"]
            > new_data["Open_interday_diff_percent_mean"]
        )
        o_interday_fail_percent = o_interday_fail_bool_arr.sum() * 100 / len(new_data)
        h_interday_fail_bool_arr = (
            new_data["High_interday_diff_percent"]
            > new_data["High_interday_diff_percent_mean"]
        )
        h_interday_fail_percent = h_interday_fail_bool_arr.sum() * 100 / len(new_data)
        l_interday_fail_bool_arr = (
            new_data["Low_interday_diff_percent"]
            > new_data["Low_interday_diff_percent_mean"]
        )
        l_interday_fail_percent = l_interday_fail_bool_arr.sum() * 100 / len(new_data)
        c_interday_fail_bool_arr = (
            new_data["Close_interday_diff_percent"]
            > new_data["Close_interday_diff_percent_mean"]
        )
        c_interday_fail_percent = c_interday_fail_bool_arr.sum() * 100 / len(new_data)

        o_interday_last_last_fail_bool_arr = (
            new_data["Open_interday_last_last_diff_percent"]
            > new_data["Open_interday_last_last_diff_percent_mean"]
        )
        o_interday_last_last_fail_percent = (
            o_interday_last_last_fail_bool_arr.sum() * 100 / len(new_data)
        )
        h_interday_last_last_fail_bool_arr = (
            new_data["High_interday_last_last_diff_percent"]
            > new_data["High_interday_last_last_diff_percent_mean"]
        )
        h_interday_last_last_fail_percent = (
            h_interday_last_last_fail_bool_arr.sum() * 100 / len(new_data)
        )
        l_interday_last_last_fail_bool_arr = (
            new_data["Low_interday_last_last_diff_percent"]
            > new_data["Low_interday_last_last_diff_percent_mean"]
        )
        l_interday_last_last_fail_percent = (
            l_interday_last_last_fail_bool_arr.sum() * 100 / len(new_data)
        )
        c_interday_last_last_fail_bool_arr = (
            new_data["Close_interday_last_last_diff_percent"]
            > new_data["Close_interday_last_last_diff_percent_mean"]
        )
        c_interday_last_last_fail_percent = (
            c_interday_last_last_fail_bool_arr.sum() * 100 / len(new_data)
        )

        values = [
            o_interday_fail_percent,
            o_interday_last_last_fail_percent,
            h_interday_fail_percent,
            l_interday_fail_percent,
            l_interday_last_last_fail_percent,
            c_interday_fail_percent,
            c_interday_last_last_fail_percent,
        ]

        exceed_jump_threshold = False
        if any(value > self._config.DATA_JUMPS_THRESHOLD for value in values):
            exceed_jump_threshold = True
            messages_to_be_added += f"{o_interday_fail_percent}% data for which open percentage change is {self._config.ALLOWED_OHLC_INTERDAY_AVERAGE_MULTIPLIER} times greater than average open percentage change current day last to previous day first\n"
            messages_to_be_added += f"{o_interday_last_last_fail_percent}% data for which open percentage change is {self._config.ALLOWED_OHLC_INTERDAY_AVERAGE_MULTIPLIER} times greater than average open percentage change current day last to previous day last\n"
            messages_to_be_added += f"{h_interday_fail_percent}% data for which high percentage change is {self._config.ALLOWED_OHLC_INTERDAY_AVERAGE_MULTIPLIER} times greater than average high percentage change current day last to previous day first\n"
            messages_to_be_added += f"{h_interday_last_last_fail_percent}% data for which high percentage change is {self._config.ALLOWED_OHLC_INTERDAY_AVERAGE_MULTIPLIER} times greater than average high percentage change current day last to previous day last\n"
            messages_to_be_added += f"{l_interday_fail_percent}% data for which low percentage change is {self._config.ALLOWED_OHLC_INTERDAY_AVERAGE_MULTIPLIER} times greater than average low percentage change current day last to previous day first\n"
            messages_to_be_added += f"{l_interday_last_last_fail_percent}% data for which low percentage change is {self._config.ALLOWED_OHLC_INTERDAY_AVERAGE_MULTIPLIER} times greater than average low percentage change current day last to previous day last\n"
            messages_to_be_added += f"{c_interday_fail_percent}% data for which close percentage change is {self._config.ALLOWED_OHLC_INTERDAY_AVERAGE_MULTIPLIER} times greater than average close percentage change current day last to previous day first\n"
            messages_to_be_added += f"{c_interday_last_last_fail_percent}% data for which close percentage change is {self._config.ALLOWED_OHLC_INTERDAY_AVERAGE_MULTIPLIER} times greater than average close percentage change current day last to previous day last\n"

        newline_count = 0
        if exceed_jump_threshold:
            newline_count += 8
        if messages_to_be_added.count("\n") == newline_count:
            checked_messages += "Passed\n"

        checked_messages += messages_to_be_added
        checked_messages += "\n"
        return checked_messages

    @cast(Callable[..., Callable[..., str]], load_data)
    def check_all_dates(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
    ) -> str:
        """Check for missing and extra dates in the given data compared to the ALL_DATES.

        Args:
            storage_type (StorageType): The type of storage for the file.
            file_location (str): The location of the Arctic library.
            file_name (str): The symbol name of the file.
            data (pd.DataFrame): The data corresponding to the file_name in file_location.

        Returns:
            str: A collective message indicating the results of the date check.
        """

        checked_messages = "check_all_dates:\n"

        metadata = self.__auditor.read_metadata(
            storage_type=StorageType.DB,
            file_location=file_location,
            file_name=file_name,
        )

        if (metadata is None) or ("last_timestamp" not in metadata):
            checked_messages += f"Metadata is none or doesn't have last_timestamp for {file_location} with symbol = {file_name}\n"

        given_dates = set(cast(pd.DatetimeIndex, data.index).date)
        start_date = min(given_dates)
        if (
            (metadata is not None)
            and ("last_timestamp" in metadata)
            and (metadata.get("last_timestamp") != "")
            and (pd.to_datetime(str(metadata["last_timestamp"])).date() < start_date)
        ):
            # this start date is not present in the data, but for maintaining data continuity, we utilize the "last_timestamp" as the start_date and check and then check for entries falling between this start_date and the end_date specified
            start_date = pd.to_datetime(str(metadata["last_timestamp"])).date()
            given_dates.add(start_date)
        end_date = max(given_dates)

        all_dates_object = self.__auditor.read(
            storage_type=StorageType.FILE,
            file_location=self._config.FILE_DICT["ALL_DATES"][0],
            file_name=self._config.FILE_DICT["ALL_DATES"][1],
        )
        ALL_DATES = pd.DataFrame(all_dates_object, columns=["ALL_DATES"])

        allowed_dates = set(
            date
            for date in ALL_DATES["ALL_DATES"].dt.date.values
            if date >= start_date and date <= end_date
        )

        missing_dates = allowed_dates.difference(given_dates)
        extra_dates = given_dates.difference(allowed_dates)

        if len(missing_dates) > 0:
            checked_messages += f"Found {len(missing_dates)} missing dates\n"

        if len(extra_dates) > 0:
            checked_messages += f"Found {len(extra_dates)} extra dates\n"

        if checked_messages.count("\n") == 1:
            checked_messages += "Passed\n"

        checked_messages += "\n"
        return checked_messages

    @cast(Callable[..., Callable[..., str]], load_data)
    def check_all_timestamps(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
    ) -> str:
        """Check for missing and extra time entries for a particular ID and date.

        This method verifies the presence of all expected timestamps within the market hours
        for a given file, based on its frequency and universe. It compares the timestamps
        present in the data with the expected timestamps and reports any discrepancies.

        Args:
            storage_type (StorageType): The type of storage for the file.
            file_location (str): The Arctic library location of the file.
            file_name (str): The symbol name of the file.
            data (pd.DataFrame): The data corresponding to the file_name and file_location.

        Returns:
            str: A collective message indicating any missing or extra time entries.
        """

        checked_messages = "check_all_timestamps:\n"

        # file_location: "exchange/frequency/universe/dtype"
        file_location_split = file_location.split("/")
        # getting frequency from 1st index and it is represented as T_min, so getting time by index 0 of frequency (assuming 1 or 5)
        frequency = file_location_split[1][0]
        # getting universe from 2nd index
        universe = file_location_split[2]

        all_time_set = {
            stamp.time()
            for stamp in pd.date_range(
                start=self._config.MARKET_HOURS_DICT[universe]["open"],
                end=self._config.MARKET_HOURS_DICT[universe]["close"],
                freq=f"{frequency}T",
                inclusive="right",
            )
        }

        date_wise_data_list = [
            (date, group)
            for date, group in data.groupby(cast(pd.DatetimeIndex, data.index).date)
        ]

        missing_time_count = 0
        extra_time_count = 0

        for date, date_wise_data in date_wise_data_list:
            given_time_set = set(cast(pd.DatetimeIndex, date_wise_data.index).time)
            missing_time_count += len(all_time_set - given_time_set)
            extra_time_count += len(given_time_set - all_time_set)

        if missing_time_count > 0:
            checked_messages += f"Found {missing_time_count} missing times\n"

        if extra_time_count > 0:
            checked_messages += f"Found {extra_time_count} extra times\n"

        if checked_messages.count("\n") == 1:
            checked_messages += "Passed\n"

        checked_messages += "\n"
        return checked_messages

    @cast(Callable[..., Callable[..., str]], load_data)
    def check_OHLC(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
    ) -> str:
        """
        Validates the Open-High-Low-Close (OHLC) data for anomalies and data jumps.

        This function performs several checks on the OHLC data:
        1. Ensures that the 'High' value is not less than the 'Low', 'Close', or 'Open' values.
        2. Ensures that the 'Low' value is not greater than the 'Close' or 'Open' values.
        3. Calculates the percentage change between 'Open' and 'Close' and between 'High' and 'Low'.
        4. Compares these percentage changes against a threshold derived from the average percentage changes for each 'ID' and 'Date' combination, multiplied by a configurable multiplier.
        5. Identifies and reports any data points where these percentage changes exceed the threshold.

        Returns:
            str: str: A detailed message indicating the results of the OHLC validation checks, including any anomalies or data jumps detected.
        """
        checked_messages = "check_OHLC:\n"
        messages_to_be_added = ""

        data = data.reset_index()

        for col in self._config.COLUMN_JUMPS_CHECK:
            if col not in data.columns:
                checked_messages += "Passed\n\n"
                return checked_messages

        ohlc_basic_check_bool_arr = (
            (data["High"] < data["Low"])
            | (data["High"] < data["Close"])
            | (data["High"] < data["Open"])
        )

        ohlc_basic_check_bool_arr |= (data["Low"] > data["Close"]) | (
            data["Low"] > data["Open"]
        )
        ohlc_basic_check_percent = ohlc_basic_check_bool_arr.sum() * 100 / len(data)

        if ohlc_basic_check_percent > 0:
            checked_messages += (
                f"{ohlc_basic_check_percent}% data for which OHLC basic check failed\n"
            )

        data["ocp"] = (
            pd.Series(abs(data["Close"] - data["Open"]))
            / pd.Series(data[["Close", "Open"]].min(axis=1))
        ) * 100
        data["hlp"] = (
            pd.Series(abs(data["High"] - data["Low"]))
            / pd.Series(data[["High", "Low"]].min(axis=1))
        ) * 100

        threshold_ocp = (
            data.groupby([data["timestamp"].dt.date, "ID"])["ocp"].transform("mean")
            * self._config.ALLOWED_OHLC_ACROSS_ROW_AVERAGE_MULTIPLIER
        )
        threshold_hlp = (
            data.groupby([data["timestamp"].dt.date, "ID"])["hlp"].transform("mean")
            * self._config.ALLOWED_OHLC_ACROSS_ROW_AVERAGE_MULTIPLIER
        )

        threshold_ocp_exceeded_bool_arr = data["ocp"] > threshold_ocp
        threshold_hlp_exceeded_bool_arr = data["hlp"] > threshold_hlp

        threshold_ocp_exceeded_perent = (
            threshold_ocp_exceeded_bool_arr.sum() * 100 / len(data)
        )
        threshold_hlp_exceeded_perent = (
            threshold_hlp_exceeded_bool_arr.sum() * 100 / len(data)
        )

        values = [threshold_ocp_exceeded_perent, threshold_hlp_exceeded_perent]

        newline_count = 0
        if any(value > self._config.DATA_JUMPS_THRESHOLD for value in values):
            newline_count += 2
            messages_to_be_added += f"{threshold_ocp_exceeded_perent}% data for which open-close percentage change is {self._config.ALLOWED_OHLC_ACROSS_ROW_AVERAGE_MULTIPLIER} times greater than average open-close percentage for the specific ID and Date combination\n"
            messages_to_be_added += f"{threshold_hlp_exceeded_perent}% data for which low-high percentage change is {self._config.ALLOWED_OHLC_ACROSS_ROW_AVERAGE_MULTIPLIER} times greater than average low-high percentage for the specific ID and Date combination\n"

        if messages_to_be_added.count("\n") == newline_count:
            checked_messages += "Passed\n"

        checked_messages += messages_to_be_added
        checked_messages += "\n"
        return checked_messages
