## For fixes checks on data stored in local storage

import datetime
from io import BytesIO
import pandas as pd
import numpy as np
from typing import List, cast
from main.data.auditor import Auditor
from main.enums import StorageType
from main.config.config_base import ConfigBase
from main.enums import StorageType


class Checker_Fixer(Auditor):
    def __init__(self, config: ConfigBase):
        super().__init__(config)

        # list of checks_fixers
        self.checks_fixers = [
            "check_columns_set_fixer",
            "check_columns_dtype_fixer",
            "check_monotonic_nature_fixer",
            "check_nan_entries_fixer",
            "check_duplicate_entries_fixer",
            "check_forward_fill_fixer",
            "check_all_dates_fixer",
            "check_all_timestamps_fixer",
            "check_OHLC_fixer",
            "check_intraday_sudden_jumps_fixer",
            "check_overnight_sudden_jumps_fixer",
        ]

        # checks_fixers mapped functions
        self.checks_fixers_to_fxn_mapping = {
            item: getattr(self, item) for item in self.checks_fixers
        }

        self.missing_date_to_times = dict()
        self.missing_dates = set()
        self.failed_date_to_id = list()
        self.ohlc_basic_fail_data = pd.DataFrame()

    def check_data(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        checker_report: str,
        data: pd.DataFrame,
    ) -> str:
        """Calls all the checks

        Args:
            storage_type (StorageType): contains the type of storage of the file
            file_location (str): denotes arctic library location
            file_name (str): denotes symbol name
            data (pd.DataFrame): contains data corresponding to the symbol of a library
            check_list (Optional[List[str]], optional): List of checks to be performed on data. Defaults to None.
            ignore_checks (Optional[List[str]], optional): List of checks to be ignored on data. Defaults to None.

        Returns:
            str: Aggregated string of messages after all performed checks on data
        """

        collective_all_checks_message = ""

        file_location_split = file_location.split("/")
        universe = file_location_split[2]
        dtype = file_location_split[-1]

        check_wise_messages = checker_report.split("check_")[1:]
        check_wise_messages = [f"check_{msg}" for msg in check_wise_messages]

        for msg in check_wise_messages:
            check = msg.partition(":")[0]
            if "Passed" in msg:
                collective_all_checks_message += msg
            else:
                selected_check_function = self.checks_fixers_to_fxn_mapping[
                    f"{check}_fixer"
                ]

                msg, data = selected_check_function(
                    storage_type=storage_type,
                    file_location=file_location,
                    file_name=file_name,
                    data=data,
                )
                collective_all_checks_message += msg

        return (
            collective_all_checks_message,
            data,
            self.missing_dates,
            self.missing_date_to_times,
            self.failed_date_to_id,
            self.ohlc_basic_fail_data,
        )

    def _compare_ordered_column_lists_fixer(
        self,
        data_column_list: List[str],
        expected_column_list: List[str],
        universe: str,
        data: pd.DataFrame,
    ) -> str:
        """Compares the two ordered lists and returns the message related to the differences in the lists

        Args:
            data_column_list (List[str]): List containing column names of Data
            expected_column_list (List[str]): List containing expected column names for the library

        Returns:
            str: Collective messages related to the differences in the lists
        """

        if data_column_list == expected_column_list:
            return ""

        data_column_set = set(data_column_list)
        expected_column_set = set(expected_column_list)
        extra_column_in_data = list(data_column_set - expected_column_set)
        missing_column_in_data = list(expected_column_set - data_column_set)

        checked_messages = ""

        if len(data_column_list) != len(expected_column_list):
            checked_messages += f"Should have {len(expected_column_list)} but found {len(data_column_list)} columns\n"

        if extra_column_in_data:
            data = data.drop(columns=extra_column_in_data)

        if missing_column_in_data:
            checked_messages += f"Found following missing column(s) in data: {sorted(missing_column_in_data)}\n"

        if not checked_messages:
            data = data[self._config.COLUMNS_DICT[universe][1:]]

        return checked_messages, data

    def check_columns_set_fixer(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
    ) -> str:
        """Checks if all columns are present in correct order in data and returns messages accordingly

        Args:
            storage_type (StorageType): contains the type of storage of the file
            file_location (str): denotes arctic library location
            file_name (str): denotes symbol name
            data (pd.DataFrame): contains data corresponding to the file_name of file_location

        Returns:
            str: Collective messages after columns-set checks
        """

        checked_messages = "check_columns_set:\n"

        # file_location: "exchange/frequency/universe/dtype", so getting universe from 2nd index
        universe = file_location.split("/")[2]
        col_list = data.columns.values.tolist()
        index_list = list(data.index.names)
        # including index column names as well
        data_column_list = index_list + col_list
        expected_column_list = []

        if universe in self._config.OPTION_LIST or universe in self._config.SPOT_LIST:
            expected_column_list = self._config.COLUMNS_DICT[universe]
            msg, data = self._compare_ordered_column_lists_fixer(
                data_column_list=data_column_list,
                expected_column_list=expected_column_list,
                universe=universe,
                data=data,
            )
            checked_messages += msg
        else:
            checked_messages += (
                f"Unexpected universe: {universe}, so columns can't be checked\n"
            )

        if checked_messages.count("\n") == 1:
            checked_messages += "Passed\n"

        checked_messages += "\n"
        return checked_messages, data

    def check_columns_dtype_fixer(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
    ) -> str:
        """Checks if data has expected column dtypes and collectively returns messages accordingly

        Args:
            storage_type (StorageType): contains the type of storage of the file
            file_location (str): denotes arctic library location
            file_name (str): denotes symbol name
            data (pd.DataFrame): contains data corresponding to the file_name of file_location

        Returns:
            str: Collective messages after checking columns-dtypes
        """

        checked_messages = "check_columns_dtype:\n"

        col_types_dict = data.dtypes
        # including index columns too
        for index_name in data.index.names:
            col_types_dict[index_name] = data.index.get_level_values(index_name).dtype

        for key, value in col_types_dict.items():
            if key not in self._config.DATA_TYPES_DICT:
                checked_messages += (
                    f"Unexpected column: {key}, so dtype can't be checked\n"
                )

            elif value != self._config.DATA_TYPES_DICT[str(key)]:
                if np.can_cast(
                    np.dtype(data[str(key)].dtype),
                    np.dtype(self._config.DATA_TYPES_DICT[str(key)]),
                ):
                    data[str(key)] = data[str(key)].astype(
                        dtype=np.dtype(self._config.DATA_TYPES_DICT[str(key)])
                    )
                else:
                    checked_messages += f"Unexpected dtype for column: {key}, since expected {self._config.DATA_TYPES_DICT[str(key)]} but got {value}\n"

        if checked_messages.count("\n") == 1:
            checked_messages += "Passed\n"

        checked_messages += "\n"
        return checked_messages, data

    def check_intraday_sudden_jumps_fixer(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
    ) -> str:
        """Check intraday sudden jumps for OHLC (Open, High, Low, Close) with a threshold set as the mean of the specific ID and day multiplied by the across_row_factor

        Args:
            storage_type (StorageType): contains the type of storage of the file
            file_location (str): denotes arctic library location
            file_name (str): denotes symbol name
            data (pd.DataFrame): contains data corresponding to the file_name of file_location

        Returns:
            str: Collective messages after checking sudden jumps
        """

        checked_messages = "check_intraday_sudden_jumps:\n"

        datac = data.copy()
        data = data.reset_index()

        # metadata = self.read_metadata(
        #     storage_type=storage_type, file_location=file_location, file_name=file_name
        # )
        metadata = None

        if metadata is None:
            checked_messages += f"MetadataReadError: Found error during reading metadata_dict from {file_name} located at {file_location} inside store: {storage_type}\n"

        for col in self._config.COLUMN_JUMPS_CHECK:
            data[f"{col}_pct_change"] = (
                data.groupby([data["timestamp"].dt.date, "ID"])[col].pct_change().abs()
                * 100
            )

        threshold_all = (
            data.groupby([data["timestamp"].dt.date, "ID"])[
                [
                    "Open_pct_change",
                    "High_pct_change",
                    "Low_pct_change",
                    "Close_pct_change",
                ]
            ].transform("mean")
            * self._config.ALLOWED_OHLC_INTRADAY_AVERAGE_MULTIPLIER
        )

        threshold_o_se_bada_bool_arr = (
            data["Open_pct_change"] > threshold_all["Open_pct_change"]
        )
        threshold_h_se_bada_bool_arr = (
            data["High_pct_change"] > threshold_all["High_pct_change"]
        )
        threshold_l_se_bada_bool_arr = (
            data["Low_pct_change"] > threshold_all["Low_pct_change"]
        )
        threshold_c_se_bada_bool_arr = (
            data["Close_pct_change"] > threshold_all["Close_pct_change"]
        )

        data["To_take"] = (
            threshold_o_se_bada_bool_arr
            | threshold_h_se_bada_bool_arr
            | threshold_l_se_bada_bool_arr
            | threshold_c_se_bada_bool_arr
        )

        data["Shift_To_take"] = (
            data.groupby([data["timestamp"].dt.date, "ID"])["To_take"]
            .shift(-1)
            .fillna(data["To_take"])
        )
        data["Shift_To_take"] = data["Shift_To_take"] | data["To_take"]

        all_greater_than_threshold_data = data[data["Shift_To_take"]]

        all_greater_than_threshold_data["timestamp"] = all_greater_than_threshold_data[
            "timestamp"
        ].dt.date

        self.failed_date_to_id.extend(
            list(
                zip(
                    all_greater_than_threshold_data["timestamp"],
                    all_greater_than_threshold_data["ID"],
                )
            )
        )

        if checked_messages.count("\n") == 1:
            checked_messages += "Passed\n"

        checked_messages += "\n"
        return checked_messages, datac

    def check_duplicate_entries_fixer(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
    ) -> str:
        """checks duplicate entries row wise (with index as well as without index)

        Args:
            storage_type (StorageType): contains the type of storage of the file
            file_location (str): denotes arctic library location
            file_name (str): denotes symbol name
            data (pd.DataFrame): contains data corresponding to the file_name of file_location

        Returns:
            str: Collective messages after checking duplicate entries
        """
        checked_messages = "check_duplicate_entries:\n"
        data = data.reset_index()
        duplicated_data_count_exact_with_time = data.duplicated().sum()

        if duplicated_data_count_exact_with_time > 0:
            data = data.drop_duplicates()

        diff_data_on_same_timestamp_id_bool_arr = data.duplicated(
            subset=["timestamp", "ID"], keep="first"
        )
        diff_data_on_same_timestamp_id_count = (
            diff_data_on_same_timestamp_id_bool_arr.sum()
        )

        if diff_data_on_same_timestamp_id_count > 0:
            data = data[~diff_data_on_same_timestamp_id_bool_arr]

        data = data.set_index("timestamp")

        if checked_messages.count("\n") == 1:
            checked_messages += "Passed\n"

        checked_messages += "\n"
        return checked_messages, data

    def _nan_after_non_null(self, series):
        first_non_null_index = (
            series.isna().idxmin() if not series.isna().all() else series.index[-1]
        )
        nan_after_non_null = series.loc[first_non_null_index + 1 :].isna().any()
        return nan_after_non_null

    def check_forward_fill_fixer(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
    ) -> str:
        """checks forward filling in column.
        Column-wise different handling.

        Args:
            storage_type (StorageType): contains the type of storage of the file
            file_location (str): denotes arctic library location
            file_name (str): denotes symbol name
            data (pd.DataFrame): contains data corresponding to the file_name of file_location. Defaults to None.

        Returns:
            str: Collective messages after checking forward fill
        """
        checked_messages = "check_forward_fill:\n"

        data = data.reset_index()
        for column in self._config.COLUMN_FFILL_CHECK:
            if column in data:
                if column == "OI" or column == "Open_int":
                    ffill = (
                        data.groupby("ID")[column].apply(self._nan_after_non_null).sum()
                    )
                    if ffill > 0:
                        data = data.groupby("ID")[column].ffill()

        if checked_messages.count("\n") == 1:
            checked_messages += "Passed\n"

        checked_messages += "\n"
        return checked_messages, data

    def check_nan_entries_fixer(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
    ) -> str:
        """Check % nan entries for every column

        Args:
            storage_type (StorageType): contains the type of storage of the file
            file_location (str): denotes arctic library location
            file_name (str): denotes symbol name
            data (pd.DataFrame): contains data corresponding to the symbol of a library
        Returns:
            str: Collective messages after checking nan entries
        """

        checked_messages = "check_nan_entries:\n"

        ohlc_all_nan_bool_arr = (
            (data["Open"].isna())
            & (data["High"].isna())
            & (data["Low"].isna())
            & (data["Close"].isna())
        )

        if ohlc_all_nan_bool_arr.sum() > 0:
            self.failed_date_to_id.extend(
                list(
                    zip(
                        data[ohlc_all_nan_bool_arr].index.date,
                        data[ohlc_all_nan_bool_arr]["ID"],
                    )
                )
            )

        data = data[~ohlc_all_nan_bool_arr]

        if checked_messages.count("\n") == 1:
            checked_messages += "Passed\n"

        checked_messages += "\n"
        return checked_messages, data

    def check_overnight_sudden_jumps_fixer(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
    ) -> str:
        """Check overnight sudden jumps in column.
        Temporarily kept less than 50% jump (fixed) allowed.

        Args:
            storage_type (StorageType): contains the type of storage of the file
            file_location (str): denotes arctic library location
            file_name (str): denotes symbol name
            data (pd.DataFrame): contains (new) data to be appended corresponding to the file_name of file_location

        Returns:
            str: Collective messages after checking desired sudden jumps
        """

        checked_messages = "check_overnight_sudden_jumps:\n"

        datac = data.copy()
        data = data.reset_index()

        # metadata = self.read_metadata(
        #     storage_type=StorageType.DB,
        #     file_location=file_location,
        #     file_name=file_name,
        # )
        metadata = None

        df_previous_date_metadata = None

        if metadata is None or "last_timestamp" not in metadata:
            checked_messages += f"Metadata is none or doesn't have last_timestamp for {file_location} with symbol = {file_name}\n"
        else:
            try:
                previous_date = metadata.get("last_timestamp").date()
                df_previous_date_metadata = self.read(
                    storage_type=storage_type,
                    file_location=file_location,
                    file_name=file_name,
                    start_date=previous_date,
                    end_date=data["timestamp"].iloc[0].date(),
                ).reset_index()
            except Exception:
                raise Exception(
                    f"ReadError: Found error during reading {file_name} at {file_location} from store {storage_type} in check_overnight_sudden_jumps"
                )

        data = pd.concat([df_previous_date_metadata, data])
        data = data[["timestamp", "ID"] + self._config.COLUMN_JUMPS_CHECK]
        new_data = pd.DataFrame()

        for col in self._config.COLUMN_JUMPS_CHECK:
            new_data[f"{col}_first"] = data.groupby(
                [data["timestamp"].dt.date, "ID"]
            ).agg({col: "first"})
            new_data[f"{col}_last"] = data.groupby(
                [data["timestamp"].dt.date, "ID"]
            ).agg({col: "last"})

        new_data = new_data.reset_index()

        for col in self._config.COLUMN_JUMPS_CHECK:
            new_data[f"{col}_first"] = (
                new_data.groupby("ID")[f"{col}_first"]
                .shift(-1)
                .fillna(new_data[f"{col}_first"])
            )
            new_data[f"{col}_last_shift"] = (
                new_data.groupby("ID")[f"{col}_last"]
                .shift(-1)
                .fillna(new_data[f"{col}_last"])
            )

            new_data[f"{col}_interday_diff_percent"] = (
                abs(new_data[f"{col}_last"] - new_data[f"{col}_first"])
                / new_data[[f"{col}_last", f"{col}_first"]].min(axis=1)
            ) * 100
            new_data[f"{col}_interday_last_last_diff_percent"] = (
                abs(new_data[f"{col}_last"] - new_data[f"{col}_last_shift"])
                / new_data[[f"{col}_last", f"{col}_last_shift"]].min(axis=1)
            ) * 100

            new_data[f"{col}_interday_diff_percent_mean"] = (
                new_data.groupby("ID")[f"{col}_interday_diff_percent"].transform("mean")
                * self._config.ALLOWED_OHLC_INTERDAY_AVERAGE_MULTIPLIER
            )
            new_data[f"{col}_interday_last_last_diff_percent_mean"] = (
                new_data.groupby("ID")[
                    f"{col}_interday_last_last_diff_percent"
                ].transform("mean")
                * self._config.ALLOWED_OHLC_INTERDAY_AVERAGE_MULTIPLIER
            )

        o_interday_fail_bool_arr = (
            new_data["Open_interday_diff_percent"]
            > new_data["Open_interday_diff_percent_mean"]
        )
        h_interday_fail_bool_arr = (
            new_data["High_interday_diff_percent"]
            > new_data["High_interday_diff_percent_mean"]
        )
        l_interday_fail_bool_arr = (
            new_data["Low_interday_diff_percent"]
            > new_data["Low_interday_diff_percent_mean"]
        )
        c_interday_fail_bool_arr = (
            new_data["Close_interday_diff_percent"]
            > new_data["Close_interday_diff_percent_mean"]
        )

        o_interday_last_last_fail_bool_arr = (
            new_data["Open_interday_last_last_diff_percent"]
            > new_data["Open_interday_last_last_diff_percent_mean"]
        )
        h_interday_last_last_fail_bool_arr = (
            new_data["High_interday_last_last_diff_percent"]
            > new_data["High_interday_last_last_diff_percent_mean"]
        )
        l_interday_last_last_fail_bool_arr = (
            new_data["Low_interday_last_last_diff_percent"]
            > new_data["Low_interday_last_last_diff_percent_mean"]
        )
        c_interday_last_last_fail_bool_arr = (
            new_data["Close_interday_last_last_diff_percent"]
            > new_data["Close_interday_last_last_diff_percent_mean"]
        )

        new_data["To_take"] = (
            o_interday_fail_bool_arr
            | h_interday_fail_bool_arr
            | l_interday_fail_bool_arr
            | c_interday_fail_bool_arr
            | o_interday_last_last_fail_bool_arr
            | h_interday_last_last_fail_bool_arr
            | l_interday_last_last_fail_bool_arr
            | c_interday_last_last_fail_bool_arr
        )

        new_data["Shift_To_take"] = (
            new_data.groupby(["ID"])["To_take"].shift(1).fillna(new_data["To_take"])
        )
        new_data["Shift_To_take"] = new_data["Shift_To_take"] | new_data["To_take"]

        interday_fail_data = new_data[new_data["Shift_To_take"]]

        if len(interday_fail_data) > 0:
            self.failed_date_to_id.extend(
                list(
                    zip(
                        interday_fail_data["timestamp"],
                        interday_fail_data["ID"],
                    )
                )
            )

        if checked_messages.count("\n") == 1:
            checked_messages += "Passed\n"

        checked_messages += "\n"
        return checked_messages, datac

    def check_all_dates_fixer(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
    ) -> str:
        """check missing and extra dates for the given data from ALL_DATES

        Args:
            storage_type (StorageType): contains the type of storage of the file
            file_location (str): denotes arctic library location
            file_name (str): denotes symbol name
            data (pd.DataFrame): contains data corresponding to the file_name of file_location

        Returns:
            str: Collective messages after finding missing and extra dates
        """

        checked_messages = "check_all_dates:\n"

        # metadata = self.read_metadata(
        #     storage_type=StorageType.DB,
        #     file_location=file_location,
        #     file_name=file_name,
        # )
        metadata = None

        if (metadata is None) or ("last_timestamp" not in metadata):
            checked_messages += f"Metadata is none or doesn't have last_timestamp for {file_location} with symbol = {file_name}\n"

        given_dates = set(cast(pd.DatetimeIndex, data.index).date)
        start_date = min(given_dates)
        if (
            (metadata is not None)
            and ("last_timestamp" in metadata)
            and metadata["last_timestamp"].date() < start_date
        ):
            # this start date is not present in the data, but for maintaining data continuity, we utilize the "last_timestamp" as the start_date and check and then check for entries falling between this start_date and the end_date specified
            start_date = metadata["last_timestamp"].date()
            given_dates.add(start_date)
        end_date = max(given_dates)

        all_dates_object = self.read(
            storage_type=StorageType.FILE,
            file_location=self._config.FILE_DICT["ALL_DATES"][0],
            file_name=self._config.FILE_DICT["ALL_DATES"][1],
        )
        all_dates_list = np.load(BytesIO(all_dates_object.data), allow_pickle=True)
        ALL_DATES = pd.DataFrame(all_dates_list, columns=["ALL_DATES"])

        allowed_dates = set(
            date
            for date in ALL_DATES["ALL_DATES"].dt.date.values
            if date >= start_date and date <= end_date
        )

        missing_dates = allowed_dates.difference(given_dates)
        extra_dates = list(given_dates.difference(allowed_dates))

        if len(missing_dates) > 0:
            self.missing_dates.update(missing_dates)
            checked_messages += f"Found {len(missing_dates)} missing dates\n"

        if len(extra_dates) > 0:
            date_not_in_extra_dates = ~np.isin(
                cast(pd.DatetimeIndex, data.index).date, extra_dates
            )
            data = data[date_not_in_extra_dates]

        if checked_messages.count("\n") == 1:
            checked_messages += "Passed\n"

        checked_messages += "\n"
        return checked_messages, data

    def check_all_timestamps_fixer(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
    ) -> str:
        """check for missing and extra time entries for particular ID and date

        Args:
            storage_type (StorageType): contains the type of storage of the file
            file_location (str): denotes arctic library location
            file_name (str): denotes symbol name
            data (pd.DataFrame): contains data corresponding to the file_name of file_location

        Returns:
            str: Collective messages after finding missing and extra time entries
        """

        checked_messages = "check_all_timestamps:\n"

        # file_location: "exchange/frequency/universe/dtype"
        file_location_split = file_location.split("/")
        # getting frequency from 1st index and it is represented as T_min, so getting time by index 0 of frequency (assuming 1 or 5)
        frequency = file_location_split[1][0]
        # getting universe from 2nd index
        universe = file_location_split[2]

        all_time_set = {
            stamp.time()
            for stamp in pd.date_range(
                start=self._config.MARKET_HOURS_DICT[universe]["open"],
                end=self._config.MARKET_HOURS_DICT[universe]["close"],
                freq=f"{frequency}T",
                inclusive="right",
            )
        }

        date_wise_data_list = [
            (date, group)
            for date, group in data.groupby(cast(pd.DatetimeIndex, data.index).date)
        ]

        missing_time_count = 0
        extra_time_count = 0
        extra_time_set = set()

        for date, date_wise_data in date_wise_data_list:
            given_time_set = set(
                cast(datetime.time, cast(pd.DatetimeIndex, date_wise_data.index).time)
            )
            date_wise_missing_time_set = all_time_set - given_time_set
            date_wise_extra_time_set = given_time_set - all_time_set
            missing_time_count += len(date_wise_missing_time_set)
            extra_time_count += len(date_wise_extra_time_set)
            extra_time_set |= date_wise_extra_time_set

            if len(date_wise_missing_time_set) > 0:
                if date not in self.missing_date_to_times:
                    self.missing_date_to_times[date] = set()
                self.missing_date_to_times[date].update(date_wise_missing_time_set)

        if missing_time_count > 0:
            checked_messages += f"Found {missing_time_count} missing times\n"

        if extra_time_count > 0:
            time_to_take = ~np.isin(
                cast(pd.DatetimeIndex, data.index).time, list(extra_time_set)
            )
            data = data[time_to_take]

        if checked_messages.count("\n") == 1:
            checked_messages += "Passed\n"

        checked_messages += "\n"
        return checked_messages, data

    def check_monotonic_nature_fixer(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
    ) -> str:
        """check monotonic nature of column.
        Column-wise different handling.

        Args:
            storage_type (StorageType): contains the type of storage of the file
            file_location (str): denotes arctic library location
            file_name (str): denotes symbol name
            data (pd.DataFrame): contains data corresponding to the file_name of file_location. Defaults to None.

        Returns:
            str: Collective messages after checking monotonic nature
        """

        checked_messages = "check_monotonic_nature:\n"

        for column in self._config.COLUMN_MONOTONIC_CHECK:
            if column in data:
                if column == "Cons_Volume":
                    data["Date"] = data.index.date

                    data_without_nan = data.dropna(subset=["Cons_Volume"])
                    result = data_without_nan.groupby(["ID", pd.Grouper(freq="D")])[
                        "Cons_Volume"
                    ].is_monotonic_increasing

                    non_monotonic = result[~result].reset_index()
                    non_monotonic = non_monotonic.rename(columns={"timestamp": "Date"})
                    data = data.reset_index()
                    filtered_data = data[
                        ~data.set_index(["ID", "Date"]).index.isin(
                            non_monotonic.set_index(["ID", "Date"]).index
                        )
                    ]
                    non_monotonic["Date"] = non_monotonic["Date"].dt.date
                    self.failed_date_to_id.extend(
                        list(zip(non_monotonic["Date"], non_monotonic["ID"]))
                    )

                    filtered_data = filtered_data.drop("Date", axis=1)
                    filtered_data = filtered_data.set_index("timestamp")

        if checked_messages.count("\n") == 1:
            checked_messages += "Passed\n"

        checked_messages += "\n"
        return checked_messages, filtered_data

    def check_OHLC_fixer(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
    ):
        checked_messages = "check_OHLC:\n"

        ohlc_basic_check_bool_arr = (
            (data["High"] < data["Low"])
            | (data["High"] < data["Close"])
            | (data["High"] < data["Open"])
        )

        ohlc_basic_check_bool_arr |= (data["Low"] > data["Close"]) | (
            data["Low"] > data["Open"]
        )

        if ohlc_basic_check_bool_arr.sum() > 0:
            self.ohlc_basic_fail_data = data[ohlc_basic_check_bool_arr]
            self.failed_date_to_id.extend(
                list(
                    zip(
                        self.ohlc_basic_fail_data.index.date,
                        self.ohlc_basic_fail_data["ID"],
                    )
                )
            )
            data = data[~ohlc_basic_check_bool_arr]

        datac = data.copy()
        data["ocp"] = (
            (abs(data["Close"] - data["Open"])) / data[["Close", "Open"]].min(axis=1)
        ) * 100
        data["hlp"] = (
            abs(data["High"] - data["Low"]) / data[["High", "Low"]].min(axis=1)
        ) * 100

        threshold_ocp = (
            data.groupby([data.index.date, "ID"])["ocp"].transform("mean")
            * self._config.ALLOWED_OHLC_ACROSS_ROW_AVERAGE_MULTIPLIER
        )
        threshold_hlp = (
            data.groupby([data.index.date, "ID"])["hlp"].transform("mean")
            * self._config.ALLOWED_OHLC_ACROSS_ROW_AVERAGE_MULTIPLIER
        )

        threshold_ocp_se_bada_bool_arr = data["ocp"] > threshold_ocp
        threshold_hlp_se_bada_bool_arr = data["hlp"] > threshold_hlp

        threshold_se_bada_data = data[
            threshold_ocp_se_bada_bool_arr | threshold_hlp_se_bada_bool_arr
        ]

        if len(threshold_se_bada_data) > 0:
            self.failed_date_to_id.extend(
                list(
                    zip(
                        threshold_se_bada_data.index.date,
                        threshold_se_bada_data["ID"],
                    )
                )
            )

        if checked_messages.count("\n") == 1:
            checked_messages += "Passed\n"

        checked_messages += "\n"
        return checked_messages, datac
