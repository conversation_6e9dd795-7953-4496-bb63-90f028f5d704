from enum import Enum, IntEnum


class ExchangeType(IntEnum):
    NSE = 1
    FX = 2
    MCX = 3
    NCDEX = 4
    KRX = 5
    GIFT = 6
    BSE = 7
    INTERNATIONAL = 8


class StorageType(Enum):
    DB = "db"
    LOCAL = "local"
    FILE = "file"


class Operation(Enum):
    OI = "oi"
    PCR = "pcr"
    GREEKS = "greeks"
    FILTER = "filter"
    MODIFY = "modify"
    NEXT_CONS_VOLUME = "next_cons_volume"
    CONS_VOLUME = "cons_volume"


class Check(Enum):
    CHECK_COLUMNS_SET = "check_columns_set"
    CHECK_COLUMNS_DTYPE = "check_columns_dtype"
    CHECK_NAN_ENTRIES = "check_nan_entries"
    CHECK_DUPLICATE_ENTRIES = "check_duplicate_entries"
    CHECK_MONOTONIC_NATURE = "check_monotonic_nature"
    CHECK_ALL_DATES = "check_all_dates"
    CHECK_ALL_TIMESTAMPS = "check_all_timestamps"
    CHECK_FORWARD_FILL = "check_forward_fill"
    CHECK_OHLC = "check_OHLC"
    CHECK_INTRADAY_SUDDEN_JUMPS = "check_intraday_sudden_jumps"
    CHECK_OVERNIGHT_SUDDEN_JUMPS = "check_overnight_sudden_jumps"
