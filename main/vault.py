from dataclasses import dataclass, field
import logging
import os
import json
import hvac  # type: ignore
from typing import Optional, Dict, Any
from enum import Enum
from dotenv import load_dotenv

logger = logging.getLogger("tanki")
load_dotenv()


class VaultAccessMode(Enum):
    """Enumeration for Vault access modes."""

    READ_ONLY = "read_only"
    WRITE = "write"


class UnauthorizedVaultAccess(Exception):
    """Exception raised when Vault authentication fails or access is denied.

    This exception is raised when the client fails to authenticate with Vault
    or when attempting to access Vault resources without proper authentication.
    """

    pass


@dataclass
class VaultConfig:
    """Configuration for HashiCorp Vault connection.

    Attributes:
        url: The URL of the Vault server.
        token_path: Path to a file containing the Vault token.
        token: Direct Vault token value from environment variable.
        app_role_cred_path: Path to a file containing AppRole credentials (role_id and secret_id).
        role_id: AppRole role_id from environment variable.
        secret_id: AppRole secret_id from environment variable.
    """

    url: str = field(
        default_factory=lambda: os.getenv("VAULT_URL", "http://192.168.0.198:8300")
    )
    token_path: str = field(default_factory=lambda: os.getenv("VAULT_TOKEN_PATH", ""))
    token: Optional[str] = field(default_factory=lambda: os.getenv("VAULT_TOKEN"))
    app_role_cred_path: str = field(
        default_factory=lambda: os.getenv("VAULT_APP_ROLE_CREDENTIALS_PATH", "")
    )
    role_id: Optional[str] = field(default_factory=lambda: os.getenv("VAULT_ROLE_ID"))
    secret_id: Optional[str] = field(
        default_factory=lambda: os.getenv("VAULT_SECRET_ID")
    )


@dataclass
class Data:
    """Data configuration container for storage credentials and settings.

    This class holds configuration data for various storage systems including
    database stores, local stores, file stores, and security settings.

    Attributes:
        db_store_endpoint: Database storage endpoint URL.
        db_store_username: Username for database storage access.
        db_store_password: Password for database storage access.
        db_store_bucket: Bucket name for database storage.
        local_store_endpoint: Local storage endpoint URL.
        local_store_username: Username for local storage access.
        local_store_password: Password for local storage access.
        local_store_bucket: Bucket name for local storage.
        file_store_endpoint: File storage endpoint URL.
        file_store_username: Username for file storage access.
        file_store_password: Password for file storage access.
        hash_salt: Salt used for password hashing operations.
        metadata_library: library with user credentials for tanki
    """

    db_store_endpoint: str = "192.168.0.121:9000"
    db_store_username: str = "minioreader"
    db_store_password: str = "minioreader"
    db_store_bucket: str = "kivi-arcticdb"
    local_store_endpoint: str = "192.168.0.121:9000"
    local_store_username: str = "minioreader"
    local_store_password: str = "minioreader"
    local_store_bucket: str = "arctic-db"
    file_store_endpoint: str = "192.168.0.198:11009"
    file_store_username: str = "minioreader"
    file_store_password: str = "reasersecret"
    hash_salt: bytes = b"$2b$12$gpNnccSEVoQdOBaugrwYd."
    metadata_library: str = "metadata"


class Vault:
    """Singleton client for interacting with HashiCorp Vault.

    Provides methods to authenticate with Vault using either token or AppRole
    and retrieve secrets. Implements automatic fallback between authentication methods.

    This class is a singleton that maintains separate instances for read-only and write access.

    Example:
        ```python
        vault = Vault.get_instance(write=False)
        secrets = vault.get_secrets(path="kv/data/my-secrets")
        ```
    """

    _read_only_instance: Optional["Vault"] = None
    _write_instance: Optional["Vault"] = None

    def __init__(self, write: bool = False):
        """Initialize Vault client with the provided configuration.

        Note: This should not be called directly. Use get_instance() instead.

        Args:
            write: If True, allows writer access credentials loaded from Vault.
                  If False, uses default fallback credentials.

        Raises:
            UnauthorizedVaultAccess: If all authentication methods fail.
        """
        self.__data: Optional[Data] = None
        self.__config: VaultConfig = VaultConfig()
        self.__client: Optional[hvac.Client] = None
        self.login(write=write)
        self.load_data(write=write)

    @classmethod
    def get_instance(cls, write: bool = False) -> "Vault":
        """Get the singleton instance for the specified access mode.

        Args:
            write: If True, returns a write-enabled Vault instance.
                  If False, returns a read-only Vault instance.

        Returns:
            Vault: The appropriate Vault instance for the access mode.
        """
        if write:
            if cls._write_instance is None:
                logger.info("Creating write-enabled Vault instance")
                cls._write_instance = cls(write=True)
            return cls._write_instance
        else:
            if cls._read_only_instance is None:
                logger.info("Creating read-only Vault instance")
                cls._read_only_instance = cls(write=False)
            return cls._read_only_instance

    @classmethod
    def reset_instances(cls) -> None:
        """Reset all Vault instances. Useful for testing or re-authentication."""
        cls._read_only_instance = None
        cls._write_instance = None
        logger.info("Reset all Vault instances")

    def __getitem__(self, key: str) -> Any:
        """Get a value from the Vault data object using [] accessor.

        Args:
            key: The key to retrieve from the Vault data object.

        Returns:
            The value associated with the specified key in the Vault data object.

        Example:
            ```python
            vault = Vault()
            endpoint = vault["db_store_endpoint"]
            username = vault["db_store_username"]
            ```
        """
        return getattr(self.__data, key, None)

    def is_authenticated(self) -> bool:
        """Check if the client is authenticated to Vault.

        Returns:
            bool: True if the client is properly authenticated, False otherwise.
        """
        return self.__client is not None and self.__client.is_authenticated()

    def login(self, write: bool = False) -> None:
        """Authenticate to Vault using available authentication methods.

        Args:
            write: If True, loads credentials from Vault. If False, uses default values.
        """
        if not write:
            return  # Use default credentials if not writing
        assert self.__config.url is not None, "Vault URL not provided"
        try:
            self.__token_login()
        except Exception as e:
            logger.warning(f"Token login failed with error: {e}")

        # Try AppRole login if token login failed
        if not self.is_authenticated():
            try:
                self.__app_role_login()
            except Exception as e:
                logger.warning(f"AppRole login failed with error: {e}")

        if not self.is_authenticated():
            raise UnauthorizedVaultAccess(
                "No valid authentication method provided or all methods failed"
            )

    def __token_login(self) -> None:
        """Authenticate to Vault using a token.

        Attempts to read token from the file specified in config.token_path
        or falls back to the direct token value in config.token.

        Raises:
            Exception: If no valid token is available from any source.
        """
        token = None
        try:
            with open(self.__config.token_path, "r") as f:
                token = f.read().strip()
        except Exception as e:
            logger.warning(f"Token login failed with error: {e}")
        token = token or self.__config.token
        if not token:
            raise Exception("No Vault token provided")
        self.__client = hvac.Client(url=self.__config.url, token=token)

    def __app_role_login(self) -> None:
        """Authenticate to Vault using AppRole credentials.

        Attempts to read credentials from the file specified in config.app_role_cred_path
        or falls back to environment variables if file reading fails.

        Raises:
            Exception: If role_id or secret_id are missing or invalid.
        """
        app_role_data = {}
        try:
            with open(self.__config.app_role_cred_path, "r") as f:
                json_dict = json.load(f)
                app_role_data = json_dict.get("arctic-data-audit", {})
        except Exception as e:
            logger.warning(f"AppRole login failed with error: {e}")
        role_id = app_role_data.get("role", self.__config.role_id)
        secret_id = app_role_data.get("secret", self.__config.secret_id)

        if not role_id or not secret_id:
            raise Exception("Missing role_id or secret_id for AppRole authentication")
        self.__client = hvac.Client(url=self.__config.url)
        self.__client.auth.approle.login(role_id=role_id, secret_id=secret_id)

    def load_data(self, write: bool) -> None:
        """Load configuration data from Vault or use defaults.

        Args:
            write: If True, loads credentials from Vault. If False, uses default values.
        """
        if not write:
            self.__data = Data()
            return
        try:
            arctic_data = self.__get_secrets(path="kv/data/minio-root-121")
            minio_data = self.__get_secrets(path="kv/data/minioreader-minio-199")

            self.__data = Data(
                db_store_username=arctic_data.get("username", "minioreader"),
                db_store_password=arctic_data.get("password", "reasersecret"),
                local_store_username=arctic_data.get("username", "minioreader"),
                local_store_password=arctic_data.get("password", "reasersecret"),
                file_store_username=minio_data.get("username", "minioreader"),
                file_store_password=minio_data.get("password", "reasersecret"),
            )
        except Exception as e:
            logger.error(f"Failed to load data from Vault: {e}")
            raise UnauthorizedVaultAccess("Failed to load data from Vault") from e

    def __get_secrets(self, path: str) -> Dict[str, Any]:
        """Retrieve secrets from Vault.

        Args:
            path: The path to the secrets in Vault (e.g., "kv/data/my-secrets").

        Returns:
            Dict[str, Any]: Dictionary containing the secret key-value pairs.

        Raises:
            UnauthorizedVaultAccess: If the client is not authenticated.
            Exception: If the secrets cannot be retrieved or the path is invalid.

        Example:
            ```python
            # Get secrets from a specific path
            secrets = vault.get_secrets(path="kv/data/database-credentials")
            db_username = secrets["username"]
            db_password = secrets["password"]
            ```
        """
        if not self.is_authenticated():
            raise UnauthorizedVaultAccess("Not authenticated to Vault")
        assert self.__client is not None  # to make mypy happy
        secrets: Dict[str, Any] = self.__client.read(path=path)["data"]["data"]
        return secrets


def get_vault(write: bool = False) -> Vault:
    """Convenience function to get a Vault instance.

    This function provides a simple interface to get Vault instances
    using the singleton pattern.

    Args:
        write: If True, returns a write-enabled Vault instance.
              If False, returns a read-only Vault instance.

    Returns:
        Vault: The appropriate Vault instance for the access mode.

    Example:
        ```python
        # Get read-only vault
        vault_ro = get_vault(write=False)

        # Get write-enabled vault
        vault_w = get_vault(write=True)
        ```
    """
    return Vault.get_instance(write=write)
