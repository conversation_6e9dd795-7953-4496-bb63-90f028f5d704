import pandas as pd
from typing import List, Optional, Callable, Any, Union
from main.enums import StorageType


def load_data(
    check_func: Callable[[Any, StorageType, str, str, pd.DataFrame], str]
) -> Callable[[Any, StorageType, str, str, Optional[pd.DataFrame]], str]:
    """used to load data for checker functions if it is not passed in arguments

    Raises:
        Exception: In case data is emtpy
    """

    def inner(
        checker_obj: Any,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        data: Optional[pd.DataFrame] = None,
    ) -> str:
        if data is None:
            data = checker_obj._Checker__auditor.read(
                storage_type=storage_type,
                file_location=file_location,
                file_name=file_name,
            )
        elif data.empty:
            raise Exception(
                f"DataError: DataFrame contains no data for the symbol: {file_name}"
            )
        elif "timestamp" not in data.index.names:
            raise Exception("DataError: DataFrame must include timestamp in the index")

        return check_func(checker_obj, storage_type, file_location, file_name, data)

    return inner


def authorization_check(
    check_func: Callable[..., Union[pd.DataFrame, bool, List[str]]]
) -> Callable[..., Union[pd.DataFrame, bool, List[str]]]:
    """
    used to check whether user is authorized to read/write to specific library or not
    Note:
        1. if symbol is None, it indicates that the list_symbols method has invoked the decorator
        2. if data is None, it indicates that the read method has invoked the decorator
        3. otherwise append, update or write method has invoked the decorator
    """

    def inner(
        self: Any,
        symbol: Optional[str] = None,
        data: Optional[pd.DataFrame] = None,
        start_date: Optional[pd.Timestamp] = None,
        end_date: Optional[pd.Timestamp] = None,
        comment: str = "",
    ) -> Union[pd.DataFrame, bool, List[str]]:
        if not self._write:
            if check_func.__name__ not in [
                "list_symbols",
                "read",
                "read_metadata",
            ]:
                raise Exception(
                    "Permission denied: user not authorize to do write operation in read mode. Please login with write=True"
                )

        if symbol is None:
            if (
                self.user_data is not None
                and self.user_data["username"].iloc[0] == "airflow"
            ):
                return check_func(self)
            read_permissions = self.user_data["read_permissions"].values
            if self._Library__lib_name not in read_permissions:
                raise Exception(
                    f"Permission denied: user not authorize to access {self._Library__lib_name}"
                )
            return check_func(self)
        elif data is None:
            if (
                self.user_data is not None
                and self.user_data["username"].iloc[0] == "airflow"
            ):
                return check_func(
                    self, symbol=symbol, start_date=start_date, end_date=end_date
                )
            if self._Library__lib_name == self._Library__vault["metadata_library"]:
                return check_func(
                    self, symbol=symbol, start_date=start_date, end_date=end_date
                )
            read_permissions = self.user_data["read_permissions"].values
            if self._Library__lib_name not in read_permissions:
                raise Exception(
                    f"Permission denied: user not authorize to access {self._Library__lib_name}"
                )
            return check_func(
                self,
                symbol=symbol,
                start_date=start_date,
                end_date=end_date,
            )

        if (
            self.user_data is not None
            and self.user_data["username"].iloc[0] == "airflow"
        ):
            return check_func(self, symbol=symbol, data=data, comment=comment)
        write_permissions = self.user_data["write_permissions"].values
        if self._Library__lib_name not in write_permissions:
            raise Exception(
                f"Permission denied: user not authorize to write/update {self._Library__lib_name}"
            )
        return check_func(self, symbol=symbol, data=data, comment=comment)

    return inner
