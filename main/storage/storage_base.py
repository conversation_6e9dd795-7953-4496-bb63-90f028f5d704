"""
Base Storage abstract class that defines the interface for all storage implementations.

This class serves as a contract for different storage implementations (DB, File, Local)
and ensures consistent access patterns across the application. All storage classes must
implement these methods to provide a unified interface for data operations.

The storage system uses a two-level hierarchy:
- file_location: Represents a container (library, bucket, etc.) that holds multiple files
- file_name: Represents an individual file/object within a container

Examples:
    Creating a concrete implementation:
    ```python
    class MyStorage(StoreBase):
        def list_file_locations(self) -> List[str]:
            # Implementation
            return ["location1", "location2"]

        # Implement other required methods...
    ```
"""

from abc import ABC
from abc import abstractmethod
import pandas as pd
from typing import Dict, List, Union, Optional


class StoreBase(ABC):
    """
    Abstract base class defining the interface for all storage implementations.

    This class cannot be instantiated directly and must be subclassed with
    implementations for all abstract methods.
    """

    def __init__(self, write: bool = False) -> None:
        """Initialize the base storage class.

        Args:
            write (bool, optional): If True, enables write access with credentials from <PERSON>ault.
                If False, uses default read-only credentials. Defaults to False.
        """
        super().__init__()
        self.__write = write

    @abstractmethod
    def list_file_locations(self) -> List[str]:
        """
        List all available file locations/containers in the storage.

        Returns:
            List[str]: A list of all file locations (libraries, buckets, etc.)

        Raises:
            NotImplementedError: If the method is not implemented by a subclass
        """
        pass

    @abstractmethod
    def list_file_names(self, file_location: str) -> List[str]:
        """
        List all file names/objects within a specific file location.

        Args:
            file_location (str): The location/container to list files from

        Returns:
            List[str]: A list of all file names in the specified location

        Raises:
            NotImplementedError: If the method is not implemented by a subclass
        """
        pass

    @abstractmethod
    def read(
        self,
        file_location: str,
        file_name: str,
        start_date: Optional[pd.Timestamp] = None,
        end_date: Optional[pd.Timestamp] = None,
    ) -> pd.DataFrame:
        """
        Read data from storage at the specified location and file.

        Args:
            file_location (str): The location/container to read from
            file_name (str): The name of the file/object to read
            start_date (Optional[pd.Timestamp], optional): Start date for filtering data. Defaults to None.
            end_date (Optional[pd.Timestamp], optional): End date for filtering data. Defaults to None.

        Returns:
            pd.DataFrame: The data read from storage

        Raises:
            NotImplementedError: If the method is not implemented by a subclass
        """
        pass

    @abstractmethod
    def append(
        self,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
        metadata: Dict[str, Union[str, Dict[str, float]]],
    ) -> int:
        """
        Append data to an existing file in storage.

        Args:
            file_location (str): The location/container where the file exists
            file_name (str): The name of the file to append to
            data (pd.DataFrame): The data to append
            metadata (Dict[str, Union[str, Dict[str, float]]]): Metadata associated with the data

        Returns:
            int: Version number or identifier for the operation

        Raises:
            NotImplementedError: If the method is not implemented by a subclass
        """
        pass

    @abstractmethod
    def update(
        self,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
        metadata: Dict[str, Union[str, Dict[str, float]]],
    ) -> int:
        """
        Update existing data in storage.

        Args:
            file_location (str): The location/container where the file exists
            file_name (str): The name of the file to update
            data (pd.DataFrame): The new data to update with
            metadata (Dict[str, Union[str, Dict[str, float]]]): Metadata associated with the data

        Returns:
            int: Version number or identifier for the operation

        Raises:
            NotImplementedError: If the method is not implemented by a subclass
        """
        pass

    @abstractmethod
    def write(
        self,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
        metadata: Dict[str, Union[str, Dict[str, float]]],
    ) -> int:
        """
        Write data to storage (creates or overwrites).

        Args:
            file_location (str): The location/container to write to
            file_name (str): The name of the file to write
            data (pd.DataFrame): The data to write
            metadata (Dict[str, Union[str, Dict[str, float]]]): Metadata associated with the data

        Returns:
            int: Version number or identifier for the operation

        Raises:
            NotImplementedError: If the method is not implemented by a subclass
        """
        pass

    @abstractmethod
    def read_metadata(
        self, file_location: str, file_name: str
    ) -> Dict[str, Union[str, Dict[str, float]]]:
        """
        Read metadata for a specific file in storage.

        Args:
            file_location (str): The location/container where the file exists
            file_name (str): The name of the file to read metadata from

        Returns:
            Dict[str, Union[str, Dict[str, float]]]: The metadata associated with the file

        Raises:
            NotImplementedError: If the method is not implemented by a subclass
        """
        pass

    @abstractmethod
    def write_metadata(
        self,
        file_location: str,
        file_name: str,
        metadata: Dict[str, Union[str, Dict[str, float]]],
    ) -> bool:
        """
        Write metadata for a specific file in storage.

        Args:
            file_location (str): The location/container where the file exists
            file_name (str): The name of the file to write metadata for
            metadata (Dict[str, Union[str, Dict[str, float]]]): The metadata to write

        Returns:
            bool: True if the operation was successful, False otherwise

        Raises:
            NotImplementedError: If the method is not implemented by a subclass
        """
        pass
