"""
Storage class for storing and accessing database files required in the compilation process.

This module implements database storage using ArcticDB with credentials
managed through the Vault system for secure configuration management.
"""

from typing import Dict, List, Union, Optional
from main.storage.storage_base import StoreBase
from main.vault import get_vault
from arcticdb import Arctic  # type: ignore
import pandas as pd


class DbStore(StoreBase):
    """Database storage implementation using ArcticDB.

    This class provides methods to interact with ArcticDB for storing and
    retrieving market data.
    """

    def __init__(self, write: bool = False) -> None:
        """Initialize DbStore with ArcticDB credentials from Vault.

        Args:
            write (bool, optional): If True, enables write access with credentials from Vault.
                If False, uses default read-only credentials. Defaults to False.
        """
        super().__init__(write=write)
        vault = get_vault(write=write)
        arctic_config = f"s3://{vault['db_store_endpoint']}:{vault['db_store_bucket']}?access={vault['db_store_username']}&secret={vault['db_store_password']}"
        self.__store = Arctic(arctic_config)

    def list_file_locations(self) -> List[str]:
        """list all file locations/libraries

        Returns:
            List[str]: list of all file locations
        """

        libraries: List[str] = self.__store.list_libraries()
        return libraries

    def list_file_names(self, file_location: str) -> List[str]:
        """list all file names/symbols of file location/library

        Args:
            file_location (str): file location/library name

        Returns:
            List[str]: list of all file names
        """

        symbols: List[str] = self.__store[file_location].list_symbols()
        return symbols

    def read(
        self,
        file_location: str,
        file_name: str,
        start_date: Optional[pd.Timestamp] = None,
        end_date: Optional[pd.Timestamp] = None,
    ) -> pd.DataFrame:
        """
        Reads data from database at given file_location for file_name

        Args:
            file_location (str): file location/ library name
            file_name (str): file name/ symbol name
            start_date: Optional[pd.Timestamp] = None,
            end_date: Optional[pd.Timestamp] = None,

        Returns:
            pd.DataFrame: fetched data
        """

        data: pd.DataFrame = (
            self.__store[file_location]
            .read(symbol=file_name, date_range=(start_date, end_date))
            .data
        )
        return data

    def append(
        self,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
        metadata: Dict[str, Union[str, Dict[str, float]]],
    ) -> int:
        """Appends data in library(file_location) for given symbol(file_name)

        Args:
            file_location (str): library name
            file_name (str): symbol
            data (pd.DataFrame): data to be appended
            metadata (Dict[str, Union[str, Dict[str, float]]]): metadata dict

        Returns:
            int: version number of the data if call was successful
        """

        version_number: int = (
            self.__store[file_location]
            .append(
                symbol=file_name,
                data=data,
                metadata=metadata,
                prune_previous_versions=True,
            )
            .version
        )
        return version_number

    def update(
        self,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
        metadata: Dict[str, Union[str, Dict[str, float]]],
    ) -> int:
        """updates data in library(file_location) for given symbol(file_name)
        Data for timestamp index will get updated in db

        Args:
            file_location (str): library name
            file_name (str): symbol
            data (pd.DataFrame): data to be updated
            metadata (Dict[str, Union[str, Dict[str, float]]]): metadata dict

        Returns:
            int: version number of the data if call was successful
        """

        version_number: int = (
            self.__store[file_location]
            .update(
                symbol=file_name,
                data=data,
                metadata=metadata,
                prune_previous_versions=True,
            )
            .version
        )
        return version_number

    def write(
        self,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
        metadata: Dict[str, Union[str, Dict[str, float]]],
    ) -> int:
        """writes data in library(file_location) for given symbol(file_name)

        Args:
            file_location (str): library name
            file_name (str): symbol
            data (pd.DataFrame): data
            metadata (Dict[str, Union[str, Dict[str, float]]]): metadata dict

        Returns:
            int: version number of the data if call was successful
        """

        version_number: int = (
            self.__store[file_location]
            .write(
                symbol=file_name,
                data=data,
                metadata=metadata,
                prune_previous_versions=True,
            )
            .version
        )
        return version_number

    def read_metadata(
        self, file_location: str, file_name: str
    ) -> Dict[str, Union[str, Dict[str, float]]]:
        """reads metadata dict for symbol(file_name) in library(file_location)

        Args:
            file_location (str): library name
            file_name (str): symbol

        Returns:
            Dict[str,Union[str, Union[str, Dict[str, float]]]]: metadata dict consisting of symbol's structural information
        """

        metadata: Dict[str, Union[str, Dict[str, float]]] = (
            self.__store[file_location].read_metadata(symbol=file_name).metadata
        )
        return metadata

    def write_metadata(
        self,
        file_location: str,
        file_name: str,
        metadata: Dict[str, Union[str, Dict[str, float]]],
    ) -> bool:
        """writes metadata dict for symbol(file_name) in library(file_location)

        Args:
            file_location (str): library name
            file_name (str): symbol
            metadata (Dict[str, Union[str,Union[str, Dict[str, float]]]]): new metadata dict

        Returns:
            bool: True if call was successful
        """

        self.__store[file_location].write_metadata(symbol=file_name, metadata=metadata)
        return True
