"""
Factory module for creating different types of storage implementations.

This module provides a factory function to instantiate the appropriate storage
implementation based on the requested storage type. It abstracts the creation
logic and provides a unified interface for obtaining storage instances.

Available storage types:
- LOCAL: For temporary local storage using ArcticDB
- FILE: For file-based storage using Minio
- DB: For database storage using ArcticDB

Examples:
    ```python
    from main.enums import StorageType
    from main.storage.store_factory import StoreFactory

    # Get a database storage instance
    db_store = StoreFactory(StorageType.DB)

    # Get a file storage instance
    file_store = StoreFactory(StorageType.FILE)
    ```
"""

from main.storage.local_store import LocalStore
from main.storage.file_store import FileStore
from main.storage.db_store import DbStore
from main.enums import StorageType
from typing import Union, Dict, Type

from main.storage.storage_base import StoreBase

# Mapping of storage types to their implementation classes
STORAGE_DICT: Dict[StorageType, Type[StoreBase]] = {
    StorageType.LOCAL: LocalStore,
    StorageType.FILE: FileStore,
    StorageType.DB: DbStore,
}


def StoreFactory(storage_type: StorageType, write: bool = False) -> StoreBase:
    """
    Factory function to create and return the appropriate storage implementation.

    This function uses the storage_type parameter to determine which storage
    implementation to instantiate. It handles the creation logic and error
    handling, providing a clean interface for obtaining storage instances.

    Args:
        storage_type (StorageType): The type of storage implementation required
            (LOCAL, FILE, or DB)
        write (bool, optional): If True, enables write access with credentials from Vault.
            If False, uses default read-only credentials. Defaults to False.

    Raises:
        Exception: When the requested storage type is invalid or the storage
            implementation cannot be instantiated

    Returns:
        StoreBase: An instance of the requested storage implementation

    Examples:
        ```python
        # Get a database storage instance with write access
        db_store = StoreFactory(StorageType.DB, write=True)

        # Use the storage instance
        libraries = db_store.list_file_locations()
        ```
    """
    try:
        store_object = STORAGE_DICT[storage_type](write=write)
    except Exception:
        raise Exception(
            f"StorageTypeError: Could not get store object for {storage_type}"
        )
    return store_object
