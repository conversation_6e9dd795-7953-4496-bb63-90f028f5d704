# conftest.py
import pytest
import os
from unittest.mock import patch


# Global mock classes
class MockData:
    hash_salt = b"$2b$12$z6dRA5mg7i1iuHWAi6UzXu"
    metadata_library = "mock_metadata_library"
    db_store_endpoint = "mock-db-endpoint:9000"
    db_store_username = "mock_db_user"
    db_store_password = "mock_db_password"
    db_store_bucket = "mock-db-bucket"
    local_store_endpoint = "mock-local-endpoint:9000"
    local_store_username = "mock_local_user"
    local_store_password = "mock_local_password"
    local_store_bucket = "mock-local-bucket"
    file_store_endpoint = "mock-file-endpoint:11009"
    file_store_username = "mock_file_user"
    file_store_password = "mock_file_password"

    def __getattr__(self, name):
        # Return a default mock value for any attribute not explicitly defined
        if name.endswith("_endpoint"):
            return "mock-endpoint:9000"
        elif name.endswith("_username"):
            return "mock_username"
        elif name.endswith("_password"):
            return "mock_password"
        elif name.endswith("_bucket"):
            return "mock_bucket"
        else:
            return f"mock_{name}"


class MockVault:
    def __init__(self, write=False):
        self._Vault__data = MockData()

    def __getitem__(self, key):
        return getattr(self._Vault__data, key)

    def login(self, write=False):
        pass

    def load_data(self, write=False):
        pass


@pytest.fixture(scope="session", autouse=True)
def mock_vault_instance():
    """
    Mock the Vault instance globally for all tests in the session.
    """

    # Mock the get_vault function to return MockVault instances
    def mock_get_vault(write=False):
        return MockVault(write=write)

    patcher = patch("main.tanki.get_vault", mock_get_vault)
    library_patcher = patch("main.library.get_vault", mock_get_vault)
    storage_db_patcher = patch("main.storage.db_store.get_vault", mock_get_vault)
    storage_local_patcher = patch("main.storage.local_store.get_vault", mock_get_vault)
    storage_file_patcher = patch("main.storage.file_store.get_vault", mock_get_vault)

    patcher.start()
    library_patcher.start()
    storage_db_patcher.start()
    storage_local_patcher.start()
    storage_file_patcher.start()

    yield

    patcher.stop()
    library_patcher.stop()
    storage_db_patcher.stop()
    storage_local_patcher.stop()
    storage_file_patcher.stop()


def pytest_configure():
    os.environ["VAULT_URL"] = "http://fake-vault"
    os.environ["VAULT_TOKEN_PATH"] = "/fake/path/token"
