import json
import pytest
from unittest.mock import patch, mock_open, MagicMock
from main.vault import (
    Data,
    Vault,
    UnauthorizedVaultAccess,
    VaultConfig,
    get_vault,
    VaultAccessMode,
)

pytestmark = pytest.mark.no_global_setup


def test_default_vault_config():
    config = VaultConfig()
    assert config.url == "http://fake-vault"
    assert config.token_path == "/fake/path/token"
    assert config.token is None


def test_token_login_from_file(monkeypatch):
    token = "vault-token-from-file"
    monkeypatch.setenv("VAULT_TOKEN_PATH", "/fake/path/token")
    monkeypatch.setenv("VAULT_URL", "http://fake-vault")

    with patch("builtins.open", mock_open(read_data=token)):
        with patch("hvac.Client") as mock_client:
            client_instance = MagicMock()
            client_instance.is_authenticated.return_value = True
            mock_client.return_value = client_instance
            v = Vault(write=True)
            assert v.is_authenticated()


def test_token_login_from_env(monkeypatch):
    monkeypatch.setenv("VAULT_TOKEN", "env-token")
    monkeypatch.setenv("VAULT_URL", "http://fake-vault")
    with patch("builtins.open", side_effect=FileNotFoundError()):
        with patch("hvac.Client") as mock_client:
            instance = MagicMock()
            instance.is_authenticated.return_value = True
            mock_client.return_value = instance
            v = Vault(write=True)
            assert v.is_authenticated()


def test_token_login_failure(monkeypatch):
    monkeypatch.setenv("VAULT_URL", "http://fake-vault")
    monkeypatch.delenv("VAULT_TOKEN", raising=False)
    with pytest.raises(UnauthorizedVaultAccess):
        Vault(write=True)


def test_approle_login_success(monkeypatch):
    role_data = json.dumps({"role_id": "rid", "secret_id": "sid"})
    monkeypatch.setenv("VAULT_APP_ROLE_CREDENTIALS_PATH", "/fake/creds.json")
    monkeypatch.setenv("VAULT_URL", "http://fake-vault")

    with patch("builtins.open", mock_open(read_data=role_data)):
        with patch("hvac.Client") as mock_client:
            instance = MagicMock()
            instance.is_authenticated.return_value = True
            mock_client.return_value = instance
            v = Vault(write=True)
            assert v.is_authenticated()


def test_approle_login_env_fallback(monkeypatch):
    monkeypatch.setenv("VAULT_URL", "http://fake-vault")
    monkeypatch.setenv("VAULT_ROLE_ID", "env-role")
    monkeypatch.setenv("VAULT_SECRET_ID", "env-secret")
    with patch("builtins.open", side_effect=FileNotFoundError()):
        with patch("hvac.Client") as mock_client:
            instance = MagicMock()
            instance.is_authenticated.return_value = True
            mock_client.return_value = instance
            v = Vault(write=True)
            assert v.is_authenticated()


def test_get_secrets(monkeypatch):
    monkeypatch.setenv("VAULT_URL", "http://fake-vault")
    monkeypatch.setenv("VAULT_TOKEN", "env-token")
    expected_data = {"data": {"data": {"key": "value"}}}
    with patch("hvac.Client") as mock_client:
        instance = MagicMock()
        instance.is_authenticated.return_value = True
        instance.read.return_value = expected_data
        mock_client.return_value = instance
        v = Vault(write=True)
        secrets = v._Vault__get_secrets("kv/data/test")
        assert secrets == {"key": "value"}


def test_getitem():
    mock_data = Data(**{"db_store_endpoint": "****:9000", "db_store_username": "****"})
    v = Vault(write=False)
    with patch.object(v, "_Vault__data", mock_data):
        assert v["db_store_endpoint"] == "****:9000"
        assert v["db_store_username"] == "****"
        assert v["non_existent"] is None


def test_load_data_from_vault(monkeypatch):
    monkeypatch.setenv("VAULT_URL", "http://fake-vault")
    monkeypatch.setenv("VAULT_TOKEN", "env-token")

    arctic = {
        "data": {
            "data": {
                "endpoint": "arctic-endpoint",
                "username": "arctic-user",
                "password": "arctic-pass",
                "bucket": "arctic-bucket",
            }
        }
    }
    minio = {
        "data": {
            "data": {
                "endpoint": "minio-endpoint",
                "username": "minio-user",
                "password": "minio-pass",
                "bucket": "minio-bucket",
            }
        }
    }

    with patch("hvac.Client") as mock_client:
        instance = MagicMock()
        instance.is_authenticated.return_value = True
        instance.read.side_effect = [arctic, minio]
        mock_client.return_value = instance

        v = Vault(write=True)
        assert v["db_store_password"] == "arctic-pass"
        assert v["file_store_username"] == "minio-user"


def test_load_data_vault_failure(monkeypatch):
    monkeypatch.setenv("VAULT_URL", "http://fake-vault")
    monkeypatch.setenv("VAULT_TOKEN", "env-token")
    with patch("hvac.Client") as mock_client:
        instance = MagicMock()
        instance.is_authenticated.return_value = True
        instance.read.side_effect = Exception("Vault error")
        mock_client.return_value = instance
        with pytest.raises(UnauthorizedVaultAccess):
            Vault(write=True)


def test_vault_access_mode_enum():
    assert VaultAccessMode.READ_ONLY.value == "read_only"
    assert VaultAccessMode.WRITE.value == "write"


def test_vault_singleton(monkeypatch):
    Vault.reset_instances()
    vault1 = Vault.get_instance(write=False)
    vault2 = Vault.get_instance(write=False)
    assert vault1 is vault2

    # Mock environment for write vault
    monkeypatch.setenv("VAULT_URL", "http://fake-vault")
    monkeypatch.setenv("VAULT_TOKEN", "env-token")

    with patch("hvac.Client") as mock_client:
        instance = MagicMock()
        instance.is_authenticated.return_value = True
        instance.read.return_value = {
            "data": {"data": {"username": "test_user", "password": "test_pass"}}
        }
        mock_client.return_value = instance

        vault3 = Vault.get_instance(write=True)
        vault4 = Vault.get_instance(write=True)
        assert vault3 is vault4
        assert vault1 is not vault3


def test_get_vault_singleton_modes(monkeypatch):
    Vault.reset_instances()
    monkeypatch.setenv("VAULT_URL", "http://fake-vault")
    monkeypatch.setenv("VAULT_TOKEN", "env-token")

    with patch("hvac.Client") as mock_client:
        instance = MagicMock()
        instance.is_authenticated.return_value = True
        instance.read.return_value = {
            "data": {"data": {"username": "test_user", "password": "test_pass"}}
        }
        mock_client.return_value = instance

        vault_ro1 = get_vault(write=False)
        vault_ro2 = get_vault(write=False)
        vault_w1 = get_vault(write=True)
        vault_w2 = get_vault(write=True)

        assert vault_ro1 is vault_ro2
        assert vault_w1 is vault_w2
        assert vault_ro1 is not vault_w1


def test_vault_reset_instances(monkeypatch):
    Vault.reset_instances()
    monkeypatch.setenv("VAULT_URL", "http://fake-vault")
    monkeypatch.setenv("VAULT_TOKEN", "env-token")

    with patch("hvac.Client") as mock_client:
        instance = MagicMock()
        instance.is_authenticated.return_value = True
        instance.read.return_value = {
            "data": {"data": {"username": "test_user", "password": "test_pass"}}
        }
        mock_client.return_value = instance

        vault_ro_1 = get_vault(write=False)
        vault_w_1 = get_vault(write=True)

        Vault.reset_instances()
        vault_ro_2 = get_vault(write=False)
        vault_w_2 = get_vault(write=True)

        assert vault_ro_1 is not vault_ro_2
        assert vault_w_1 is not vault_w_2


def test_vault_lazy_initialization():
    Vault.reset_instances()
    assert Vault._read_only_instance is None
    assert Vault._write_instance is None

    vault_ro = get_vault(write=False)
    assert Vault._read_only_instance is not None
    assert Vault._write_instance is None
    assert vault_ro is Vault._read_only_instance


def test_vault_authentication_failure():
    Vault.reset_instances()
    vault = get_vault(write=False)
    assert vault is not None

    Vault.reset_instances()
    with pytest.raises(UnauthorizedVaultAccess):
        get_vault(write=True)


def test_get_vault_function_coverage():
    Vault.reset_instances()
    with patch("hvac.Client") as mock_client:
        instance = MagicMock()
        instance.is_authenticated.return_value = True
        mock_client.return_value = instance

        vault_default = get_vault()
        vault_explicit_false = get_vault(write=False)
        assert vault_default is vault_explicit_false


def test_vault_memory_efficiency(monkeypatch):
    Vault.reset_instances()
    monkeypatch.setenv("VAULT_URL", "http://fake-vault")
    monkeypatch.setenv("VAULT_TOKEN", "env-token")

    with patch("hvac.Client") as mock_client:
        instance = MagicMock()
        instance.is_authenticated.return_value = True
        instance.read.return_value = {
            "data": {"data": {"username": "test_user", "password": "test_pass"}}
        }
        mock_client.return_value = instance

        original_init = Vault.__init__
        init_call_count = {"count": 0}

        def counting_init(self, write=False):
            init_call_count["count"] += 1
            return original_init(self, write)

        with patch.object(Vault, "__init__", counting_init):
            for _ in range(10):
                get_vault(write=False)
            for _ in range(10):
                get_vault(write=True)
            assert init_call_count["count"] == 2
