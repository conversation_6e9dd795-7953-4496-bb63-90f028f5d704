from main.storage.store_factory import StoreFactory
from main.storage.db_store import DbStore
from main.storage.local_store import LocalStore
from main.enums import StorageType
import pytest


def test_storefactory_dbStore_success():
    """
    Testing db_store instance in storeFactory
    """
    db_store = StoreFactory(storage_type=StorageType.DB)
    assert isinstance(db_store, DbStore)


def test_configfactory_raise_exception():
    expected_message = "StorageTypeError: Could not get store object for invalid_store"
    with pytest.raises(Exception) as exception_info:
        StoreFactory(storage_type="invalid_store")
    assert str(exception_info.value) == expected_message


def test_storefactory_localStore_success():
    """
    Testing local_store instance in storeFactory
    """
    local_store = StoreFactory(storage_type=StorageType.LOCAL)
    assert isinstance(local_store, LocalStore)
