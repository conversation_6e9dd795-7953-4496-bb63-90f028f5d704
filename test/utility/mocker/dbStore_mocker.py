import pandas as pd
from typing import Dict, Union, Optional, Tuple


class DbStoreMocker:
    def list_file_locations(self):
        return ["nse/5_min/opt/trd", "nse/5_min/opt/ord"]

    def list_file_names(self, file_location: str):
        if file_location == "nse/5_min/opt/trd":
            return ["5001", "5002", "5003", "5008"]
        else:
            raise Exception(f"Error listing file names for {file_location}")

    def read(
        self,
        file_location: str,
        file_name: str,
        start_date: Optional[pd.Timestamp] = None,
        end_date: Optional[pd.Timestamp] = None,
    ):
        test_data = pd.read_parquet("./test/utility/test_data/arctic_test_data.parquet")

        if (file_location == "nse/5_min/opt/trd") and (file_name == "symbol"):
            return test_data
        else:
            raise Exception("Some error occured while reading from db store")

    def append(
        self,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
        metadata: Dict[str, Union[str, Dict[str, float]]],
    ):
        if (
            (
                (file_location == "nse/5_min/opt/trd")
                and (file_name == "symbol")
                and (data.empty)
            )
        ) or (
            (file_location == "operation_audit_log")
            and (file_name == "nse/5_min/opt/trd")
        ):
            return 1
        else:
            raise Exception("FileError: Invalid file location or file name")

    def update(
        self,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
        metadata: Dict[str, Union[str, Dict[str, float]]],
    ):
        if (
            (file_location == "nse/5_min/opt/trd")
            and (file_name == "symbol")
            and (data.empty)
        ):
            return 1
        else:
            raise Exception("FileError: Invalid file location or file name")

    def write(
        self,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
        metadata: Dict[str, Union[str, Dict[str, float]]],
    ):
        if (
            (file_location == "nse/5_min/opt/trd")
            and (file_name == "symbol")
            and (data.empty)
        ):
            return 1
        else:
            raise Exception("Error writing to db store")

    def read_metadata(self, file_location: str, file_name: str):
        if (file_location == "nse/5_min/opt/trd") and (file_name == "symbol"):
            metadata_dict = {
                "column": {
                    "nan_ratio": 0.2,
                    "intraday_jump": 0.0,
                    "overnight_open_jump": 0.0,
                    "overnight_close_jump": 0.0,
                }
            }
            return metadata_dict
        else:
            raise Exception("Error reading metadata from db store")

    def write_metadata(
        self,
        file_location: str,
        file_name: str,
        metadata: Dict[str, Union[str, Dict[str, float]]],
    ):
        if (
            (file_location == "nse/5_min/opt/trd")
            and (file_name == "symbol")
            and len(metadata) == 0
        ):
            return True
        else:
            raise Exception("Error writing metadata to db store")
