import pandas as pd
from typing import Dict, Union, Optional, Tuple


class VersionedObject:
    data = pd.DataFrame()
    version: int = 1
    metadata: Dict[str, Union[str, Dict[str, float]]] = {}


class ArcticLibraryMocker:
    def list_symbols(self):
        return ["5001", "5002", "5003", "5008"]

    def read(
        self,
        symbol: str,
        date_range: Optional[
            Tuple[Optional[pd.Timestamp], Optional[pd.Timestamp]]
        ] = None,
    ):
        test_data = VersionedObject()
        test_data.data = pd.read_parquet(
            "./test/utility/test_data/arctic_test_data.parquet"
        )

        if symbol == "symbol":
            return test_data
        raise Exception("Some error occured while reading from arctic")

    def append(
        self,
        symbol: str,
        data: pd.DataFrame,
        metadata: Dict[str, Union[str, Dict[str, float]]],
        prune_previous_versions: bool = False,
    ):
        if (symbol == "symbol") and (data.empty):
            return VersionedObject()

    def update(
        self,
        symbol: str,
        data: pd.DataFrame,
        metadata: Dict[str, Union[str, Dict[str, float]]],
        prune_previous_versions: bool = False,
    ):
        if (symbol == "symbol") and (data.empty):
            return VersionedObject()

    def write(
        self,
        symbol: str,
        data: pd.DataFrame,
        metadata: Dict[str, Union[str, Dict[str, float]]],
        prune_previous_versions: bool = False,
    ):
        if (symbol == "symbol") and (data.empty):
            return VersionedObject()

    def read_metadata(self, symbol: str):
        test_data = VersionedObject()
        metadata_dict: Dict[str, Union[str, Dict[str, float]]] = {
            "column": {
                "nan_ratio": 0.2,
                "intraday_jump": 0.0,
                "overnight_open_jump": 0.0,
                "overnight_close_jump": 0.0,
            }
        }
        test_data.metadata = metadata_dict

        if symbol == "symbol":
            return test_data

    def write_metadata(
        self, symbol: str, metadata: Dict[str, Union[str, Dict[str, float]]]
    ):
        if (symbol == "symbol") and (len(metadata) == 0):
            return True


class ArcticMocker:
    def list_libraries(self):
        return ["nse/5_min/opt/trd", "nse/5_min/opt/ord"]

    def __getitem__(self, lib_name):
        if lib_name == "nse/5_min/opt/trd":
            return ArcticLibraryMocker()
