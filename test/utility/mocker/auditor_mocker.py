from io import BytesIO
import numpy as np
import pandas as pd
from main.data.checker import Checker
from main.enums import StorageType
from main.config.config_base import ConfigBase
from typing import Optional


class AuditorMocker:
    def __init__(self, config: ConfigBase):
        self._config = config

    def list_file_locations(self, storage_type: StorageType):
        if storage_type == StorageType.DB or storage_type == StorageType.LOCAL:
            file_locations = ["nse/5_min/opt/trd", "nse/5_min/opt/ord"]
            return file_locations
        else:
            raise Exception("Error listing file locations")

    def list_file_names(self, storage_type: StorageType, file_location: str):
        if storage_type == StorageType.DB or storage_type == StorageType.LOCAL:
            file_names = ["5001", "5002", "5003", "5008"]
            return file_names
        else:
            raise Exception(f"Error listing file names at {file_location}")

    def read(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        start_date: Optional[pd.Timestamp] = None,
        end_date: Optional[pd.Timestamp] = None,
    ):
        test_data = pd.read_parquet("./test/utility/test_data/arctic_test_data.parquet")
        all_dates_bytes = b""
        try:
            with open(
                "./test/utility/test_data/minio_all_dates_test_data.npy", "rb"
            ) as file:
                all_dates_bytes = file.read()
                ALL_DATES = np.load(BytesIO(all_dates_bytes), allow_pickle=True)
        except Exception:
            raise Exception("Error in reading ALL_DATES")

        if (
            (storage_type == storage_type.DB)
            and (file_location == "nse/5_min/opt/trd")
            and (
                file_name
                in ["symbol", "invalid_append_symbol", "invalid_update_symbol"]
            )
        ):
            return test_data
        elif (
            (storage_type == storage_type.DB)
            and (file_location == "nse/5_min/opt/trd")
            and (file_name == "symbol_date_range_error")
        ):
            raise Exception("DateRangeError: start_date can't be more than end_date")
        elif (
            (storage_type == storage_type.FILE)
            and (file_location == "commondata")
            and (file_name == "balte_uploads/ALL_DATES.npy")
        ):
            return ALL_DATES
        else:
            raise Exception(f"Error reading {file_name} at {file_location}")

    def append(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
        metadata=None,
    ):
        if (
            (storage_type == StorageType.DB)
            and (file_location == "nse/5_min/opt/trd")
            and (file_name == "symbol")
            and (data is not None)
        ):
            return 1
        else:
            raise Exception(f"Error appending {file_name} at {file_location}")

    def update(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
        metadata=None,
    ):
        if (
            (storage_type == StorageType.DB)
            and (file_location == "nse/5_min/opt/trd")
            and (file_name == "symbol")
            and (data is not None)
        ):
            return 1
        else:
            raise Exception(f"Error updating {file_name} at {file_location}")

    def write(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
        metadata=None,
    ):
        if (
            (storage_type == StorageType.DB)
            and (file_location == "nse/5_min/opt/trd")
            and (file_name in ["symbol", "symbol_corpact_applied"])
            and (data is not None)
        ):
            return 1
        else:
            raise Exception(f"Error writing {file_name} at {file_location}")

    def update_operation_history(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        version_number: int,
        operation_type: str,
        username: str,
        comment: str,
    ):
        if (
            (storage_type == StorageType.DB)
            and (file_location == "nse/5_min/opt/trd")
            and (file_name in ["symbol", "symbol_corpact_applied"])
            and (version_number == 1)
        ):
            return
        else:
            raise Exception(
                f"Error updating operation history for {file_name} at {file_location}"
            )

    def read_metadata(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
    ):
        if (
            (storage_type == StorageType.DB)
            and (file_location == "nse/5_min/opt/trd")
            and (file_name == "symbol")
        ):
            metadata_dict = {
                "length": "300",
                "start_timestamp": "2023-01-01",
                "last_timestamp": "2023-01-31",
            }
            return metadata_dict
        raise Exception(f"Error reading metadata for {file_name} at {file_location}")

    def write_metadata(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        metadata: dict,
    ) -> bool:
        # Accept any file_location for testing purposes
        if (
            (storage_type == StorageType.DB)
            and (file_name == "symbol")
            and (metadata is not None)
        ):
            return True
        raise Exception(f"Error writing metadata for {file_name} at {file_location}")

    def update_metadata(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        data: pd.DataFrame,
        operation: str,
        checker_report: str,
    ):
        # Accept any file_location for testing purposes
        if (
            (storage_type == StorageType.DB)
            and (file_name == "symbol")
            and (data is not None)
        ):
            metadata_dict = {
                "length": str(len(data)),
                "start_timestamp": str(data.index.min())
                if hasattr(data.index, "min")
                else "2023-01-01",
                "last_timestamp": str(data.index.max())
                if hasattr(data.index, "max")
                else "2023-01-31",
            }
            return metadata_dict
        elif (
            (storage_type == StorageType.DB)
            and (file_name == "symbol_corpact_applied")
            and (data is not None)
        ):
            metadata_dict = {
                "length": "100",
                "start_timestamp": "2023-01-01",
                "last_timestamp": "2023-12-31",
            }
            return metadata_dict
        else:
            raise Exception(
                f"Error updating metadata for {file_name} at {file_location}"
            )
