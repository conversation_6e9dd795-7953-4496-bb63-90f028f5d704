import math
import pytest
import pandas as pd
import numpy as np
from main.data.utility import (
    get_balte_id,
    column_bucket_casting,
    compare_floats,
    get_charm,
    get_d1,
    get_d2,
    get_delta,
    get_gamma,
    get_iv,
    get_library_name,
    get_theta,
    get_vanna,
    get_vega,
    get_volga,
    handle_expiry,
    handle_strike,
    _parse_report,
    create_metadata_from_report,
    previous_date,
    parse_to_date,
    calculate_corpact_adj_factor,
    get_merger_demerger,
    stack_column_bucket_data,
    round_to_nearest_lotsize,
    round_to_nearest_lotsize_column_bucket,
    find_timezone_offset,
    split_dates_by_gap,
)
from main.enums import StorageType
import pytz
import time_machine


class TestUtility:
    def test_get_balte_id_optstk(self):
        result = get_balte_id(5001, "optstk", 20240514, 0, 24000)
        assert int(result) == 2400024051405001

    def test_get_balte_id_opt(self):
        result = get_balte_id(5001, "opt", 20240514, 0, 24000)
        assert int(result) == 240002024051405001

    def test_get_balte_id_fut(self):
        result = get_balte_id(5001, "fut", 20240514)
        assert int(result) == 2024051425001

    def test_handle_strike_opt_success(self):
        assert compare_floats(
            handle_strike(balte_id=210002021022505001, universe="opt"), 21000.0
        )

    def test_handle_strike_optcom_success(self):
        assert compare_floats(
            handle_strike(balte_id=360002021011517011, universe="optcom"), 3600.0
        )

    def test_handle_strike_optidx_krx_success(self):
        assert compare_floats(
            handle_strike(balte_id=8001997081418001, universe="optidx_krx"), 80.0
        )

    def test_handle_strike_optidx_krx_onemin_success(self):
        assert compare_floats(
            handle_strike(balte_id=36752024020818001, universe="optidx_krx_onemin"),
            367.5,
        )

    def test_handle_strike_optcur_uppercase_success(self):
        assert compare_floats(
            handle_strike(balte_id=73502021010916001, universe="OPTCUR"), 73.5
        )

    def test_handle_strike_optstk_success(self):
        assert compare_floats(
            handle_strike(balte_id=25000021123011013, universe="optstk"), 2500.0
        )

    def test_handle_expiry_optstk_success(self):
        expiry = handle_expiry(balte_id=25000021123011013, universe="optstk")
        assert int(expiry) == 20211230

    def test_handle_expiry_optcom_uppercase_success(self):
        assert (
            int(handle_expiry(balte_id=360002021011517011, universe="OPTCOM"))
            == 20210115
        )

    def test_get_iv_black_true_success(self):
        assert compare_floats(
            get_iv(price=5.0, s=100.0, k=100.0, t=0.5, r=0.05, f="c", black=True),
            0.18185759630143392,
        )

    def test_get_iv_black_false_success(self):
        assert compare_floats(
            get_iv(price=5.0, s=100.0, k=100.0, t=0.5, r=0.05, f="c", black=False),
            0.13044107729557658,
        )

    def test_get_iv_s_zero_exception(self):
        assert np.isnan(
            get_iv(price=5.0, s=0, k=100.0, t=0.3, r=0.05, f="c", black=True)
        )

    def test_get_iv_t_zero_exception(self):
        assert (
            get_iv(price=5.0, s=100.0, k=100.0, t=0.0, r=0.05, f="c", black=True)
            == math.inf
        )

    def test_get_delta_black_true_success(self):
        assert compare_floats(
            get_delta(flag="c", s=49, k=50, t=0.3846, r=0.05, sigma=0.2, black=True),
            0.4510701748220183,
        )

    def test_get_delta_black_false_success(self):
        assert compare_floats(
            get_delta(flag="c", s=49, k=50, t=0.3846, r=0.05, sigma=0.2, black=False),
            0.5216016339715761,
        )

    def test_get_delta_k_zero_exception(self):
        assert np.isnan(
            get_delta(flag="c", s=49, k=0, t=0.5, r=0.05, sigma=0.2, black=True)
        )

    def test_get_gamma_black_true_success(self):
        assert compare_floats(
            get_gamma(flag="c", s=49, k=50, t=0.3846, r=0.05, sigma=0.2, black=True),
            0.0640646705882491,
        )

    def test_get_gamma_black_false_success(self):
        assert compare_floats(
            get_gamma(flag="c", s=49, k=50, t=0.3846, r=0.05, sigma=0.2, black=False),
            0.06554537725247868,
        )

    def test_get_gamma_t_zero_exception(self):
        assert np.isnan(
            get_gamma(flag="c", s=49, k=50, t=0, r=0.05, sigma=0.2, black=True)
        )

    def test_get_theta_black_true_success(self):
        assert compare_floats(
            get_theta(flag="c", s=49, k=50, t=0.3846, r=0.05, sigma=0.2, black=True),
            -0.008162368774616737,
        )

    def test_get_theta_black_false_success(self):
        assert compare_floats(
            get_theta(flag="c", s=49, k=50, t=0.3846, r=0.05, sigma=0.2, black=False),
            -0.011795588943961929,
        )

    def test_get_theta_t_zero_exception(self):
        assert np.isnan(
            get_theta(flag="c", s=49, k=50, t=0.0, r=0.05, sigma=0.2, black=True)
        )

    def test_get_vega_black_true_success(self):
        assert compare_floats(
            get_vega(flag="c", s=49, k=50, t=0.3846, r=0.05, sigma=0.2, black=True),
            0.11831778562417139,
        )

    def test_get_vega_black_false_success(self):
        assert compare_floats(
            get_vega(flag="c", s=49, k=50, t=0.3846, r=0.05, sigma=0.2, black=False),
            0.12105242754243843,
        )

    def test_get_vega_k_zero_exception(self):
        assert np.isnan(
            get_vega(flag="c", s=49, k=0, t=0.25, r=0.05, sigma=0.2, black=True)
        )

    def test_get_d1_black_true_success(self):
        assert compare_floats(
            get_d1(s=20, k=20, t=4 / 12.0, r=0.09, sigma=0.25, black=True),
            0.07216878364870322,
        )

    def test_get_d1_black_false_success(self):
        assert compare_floats(
            get_d1(s=20, k=20, t=4 / 12.0, r=0.09, sigma=0.25, black=False),
            0.2800148805569685,
        )

    def test_get_d1_k_zero_exception(self):
        assert np.isnan(get_d1(s=49, k=0, t=0.25, r=0.05, sigma=0.2, black=True))

    def test_get_d1_t_zero_exception(self):
        assert get_d1(s=49, k=50, t=0.0, r=0.05, sigma=0.2, black=True) == -math.inf

    def test_get_d2_black_true_success(self):
        assert compare_floats(
            get_d2(s=20, k=20, t=4 / 12.0, r=0.09, sigma=0.25, black=True),
            -0.07216878364870322,
        )

    def test_get_d2_black_false_success(self):
        assert compare_floats(
            get_d2(s=20, k=20, t=4 / 12.0, r=0.09, sigma=0.25, black=False),
            0.13567731325956206,
        )

    def test_get_d2_k_zero_exception(self):
        assert np.isnan(get_d2(s=49, k=0, t=0.25, r=0.05, sigma=0.2, black=True))

    def test_get_d2_t_zero_exception(self):
        assert get_d2(s=49, k=50, t=0.0, r=0.05, sigma=0.2, black=True) == -math.inf

    def test_get_volga_success(self):
        assert compare_floats(
            get_volga(vega=60.0, d1=0.53, d2=0.76, sigma=0.25), 9667.2
        )

    def test_get_volga_sgima_zero_exception(self):
        assert np.isnan(get_volga(vega=60.0, d1=0.53, d2=0.76, sigma=0))

    def test_get_vanna_success(self):
        assert compare_floats(
            get_vanna(s=49, vega=60, d2=0.76, sigma=0.25, t=0.5), -526.4337832180664
        )

    def test_get_vanna_s_zero_exception(self):
        assert np.isnan(get_vanna(s=0, vega=60, d2=0.76, sigma=0.25, t=0.5))

    def test_get_charm_success(self):
        assert compare_floats(
            get_charm(d1=0.43, d2=0.87, sigma=0.25, r=0.05, t=0.5), 0.21355709103151996
        )

    def test_get_charm_sigma_zero_exception(self):
        assert np.isnan(get_charm(d1=0.43, d2=0.87, sigma=0.0, r=0.05, t=0.5))

    def test_get_library_name_storage_local_success(self):
        assert (
            get_library_name(
                exchange_name="nse",
                frequency=1,
                universe_name="opt",
                dtype="trd",
                storage=StorageType.LOCAL,
            )
            == "nse_1min_opt_trd"
        )

    def test_get_library_name_storage_db_success(self):
        assert (
            get_library_name(
                exchange_name="krx",
                frequency=5,
                universe_name="futidx",
                dtype="column_bucket",
                storage=StorageType.DB,
            )
            == "krx/5_min/futidx/column_bucket"
        )

    def test_get_library_name_storage_file_exception(self):
        with pytest.raises(Exception) as exception_info:
            get_library_name(
                exchange_name="nse",
                frequency=5,
                universe_name="optcur",
                dtype="ord",
                storage=StorageType.FILE,
            )
        assert (
            str(exception_info.value)
            == "UtilityError: Invalid storage type in get_library_name method!"
        )

    def test_column_bucket_casting_date_true_ID_true_success(self):
        data = pd.read_parquet(
            "./test/utility/test_data/column_bucket_casting_test_data.parquet"
        )
        data_types_dict = {"Open_int": "float32"}
        column_name = data.columns.name
        data.reset_index(inplace=True)
        data["09:30"] = data["09:30"].astype(dtype=np.dtype("float64"))
        data["ID"] = data["ID"].astype(dtype=np.dtype("uint32"))
        data = column_bucket_casting(
            universe="optcom",
            column_name=column_name,
            data=data,
            data_types_dict=data_types_dict,
        )
        assert data["ID"].dtype == np.dtype("uint64")
        assert data["09:30"].dtype == np.dtype("float32")

    def test_column_bucket_casting_date_false_ID_false_success(self):
        data = pd.read_parquet(
            "./test/utility/test_data/column_bucket_casting_test_data.parquet"
        )
        data_types_dict = {"Open_int": "float32"}
        column_name = data.columns.name
        data["09:30"] = data["09:30"].astype(dtype=np.dtype("float64"))
        data = column_bucket_casting(
            universe="optcom",
            column_name=column_name,
            data=data,
            data_types_dict=data_types_dict,
        )
        assert data["09:30"].dtype == np.dtype("float32")

    def test_column_bucket_casting_column_name_false_raises_exception(self):
        data = pd.read_parquet(
            "./test/utility/test_data/column_bucket_casting_test_data.parquet"
        )
        data_types_dict = {"Open_int": "float32"}
        expected_message = "UtilityError: Invalid column_name: invalid_column in column_bucket_casting method!"
        with pytest.raises(Exception) as exception_info:
            data = column_bucket_casting(
                universe="optcom",
                column_name="invalid_column",
                data=data,
                data_types_dict=data_types_dict,
            )
        assert str(exception_info.value) == expected_message

    def test_parse_report_intraday_jump(self):
        report = """
        check_intraday_sudden_jumps:\n80.0% data for which Close percentage change is greater than allowed 0.0% with highest jump: 24.8546478424672 on 2024-02-06 09:35:00\nPassed\n\n
        """
        result = _parse_report(report)
        assert result == {
            "intraday_jump_dict": {"Close": 24.8546478424672},
            "overnight_jump_open_dict": {},
            "overnight_jump_close_dict": {},
            "nan_value_dict": {},
        }

    def test_parse_report_overnight_jump_open_close(self):
        report = """
        check_overnight_sudden_jumps:\n85.71428571428571% data for which Open percentage change in current day first price to previous day last price is greater than allowed 0.0% with highest jump: 101.79640253506037 on 4   2024-02-06 09:30:00\n4   2024-02-07 09:40:00\nName: timestamp, dtype: datetime64[ns]\n42.857142857142854% data for which Open percentage change in current day last price to previous day last price is greater than allowed 0.0% with highest jump: 225.14968117584039 on 0   2024-02-06 09:20:00\n0   2024-02-07 09:20:00\nName: timestamp, dtype: datetime64[ns]\n100.0% data for which Low percentage change in current day first price to previous day last price is greater than allowed 0.0% with highest jump: 132.14285714285714 on 4   2024-02-06 09:30:00\n4   2024-02-07 09:40:00\nName: timestamp, dtype: datetime64[ns]\n42.857142857142854% data for which Low percentage change in current day last price to previous day last price is greater than allowed 0.0% with highest jump: 269.2857197352818 on 0   2024-02-06 09:20:00\n0   2024-02-07 09:20:00\nName: timestamp, dtype: datetime64[ns]\n71.42857142857143% data for which High percentage change in current day first price to previous day last price is greater than allowed 0.0% with highest jump: 230.53891162351908 on 4   2024-02-06 09:30:00\n4   2024-02-07 09:40:00\nName: timestamp, dtype: datetime64[ns]\n42.857142857142854% data for which High percentage change in current day last price to previous day last price is greater than allowed 0.0% with highest jump: 228.1436930151069 on 0   2024-02-06 09:20:00\n0   2024-02-07 09:20:00\nName: timestamp, dtype: datetime64[ns]\n100.0% data for which Close percentage change in current day first price to previous day last price is greater than allowed 0.0% with highest jump: 153.57142857142858 on 4   2024-02-06 09:30:00\n4   2024-02-07 09:40:00\nName: timestamp, dtype: datetime64[ns]\n42.857142857142854% data for which Close percentage change in current day last price to previous day last price is greater than allowed 0.0% with highest jump: 275.71427481515065 on 0   2024-02-06 09:20:00\n0   2024-02-07 09:20:00\nName: timestamp, dtype: datetime64[ns]\nPassed\n\n
        """
        result = _parse_report(report)
        assert result == {
            "intraday_jump_dict": {},
            "overnight_jump_open_dict": {
                "Open": 101.79640253506037,
                "Low": 132.14285714285714,
                "High": 230.53891162351908,
                "Close": 153.57142857142858,
            },
            "overnight_jump_close_dict": {
                "Open": 225.14968117584039,
                "Low": 269.2857197352818,
                "High": 228.1436930151069,
                "Close": 275.71427481515065,
            },
            "nan_value_dict": {},
        }

    def test_parse_report_nan_values(self):
        report = "check_nan_entries:\nFound NaN values > 30.000000000000004% for 1 dates for the column: Close, with highest NaN count of 100.0% on 2024-02-06\nFound NaN values > 30.000000000000004% for 1 dates for the column: iv, with highest NaN count of 100.0% on 2024-02-06\n\n"
        result = _parse_report(report)
        assert result == {
            "intraday_jump_dict": {},
            "overnight_jump_open_dict": {},
            "overnight_jump_close_dict": {},
            "nan_value_dict": {"Close": 100.0, "iv": 100.0},
        }

    def test_parse_report_no_entries(self):
        report = ""
        result = _parse_report(report)
        assert result == {
            "intraday_jump_dict": {},
            "overnight_jump_open_dict": {},
            "overnight_jump_close_dict": {},
            "nan_value_dict": {},
        }

    def test_create_metadata_from_report_with_corpact(self):
        column_list = ["Open"]
        checked_messages = """
        corpact_applied
        """
        result = create_metadata_from_report(column_list, checked_messages)
        assert result["Open"] == {
            "nan_ratio": 0.0,
            "intraday_jump": 0.0,
            "overnight_open_jump": 0.0,
            "overnight_close_jump": 0.0,
        }
        assert "last_corpact_applied" in result
        assert result["last_corpact_applied"] == str(pd.Timestamp.today().date())

    def test_create_metadata_from_report_multiple_columns(self):
        column_list = ["Open", "Close", "Low"]
        checked_messages = """
        check_intraday_sudden_jumps:\n80.0% data for which Close percentage change is greater than allowed 0.0% with highest jump: 24.8546478424672 on 2024-02-06 09:35:00\nPassed\n\n
        check_nan_entries:\nFound NaN values > 30.000000000000004% for 1 dates for the column: Close, with highest NaN count of 100.0% on 2024-02-06\nFound NaN values > 30.000000000000004% for 1 dates for the column: iv, with highest NaN count of 100.0% on 2024-02-06\n\n
        check_overnight_sudden_jumps:\n85.71428571428571% data for which Open percentage change in current day first price to previous day last price is greater than allowed 0.0% with highest jump: 101.79640253506037 on 4   2024-02-06 09:30:00\n4   2024-02-07 09:40:00\nName: timestamp, dtype: datetime64[ns]\n42.857142857142854% data for which Open percentage change in current day last price to previous day last price is greater than allowed 0.0% with highest jump: 225.14968117584039 on 0   2024-02-06 09:20:00\n0   2024-02-07 09:20:00\nName: timestamp, dtype: datetime64[ns]\n100.0% data for which Low percentage change in current day first price to previous day last price is greater than allowed 0.0% with highest jump: 132.14285714285714 on 4   2024-02-06 09:30:00\n4   2024-02-07 09:40:00\nName: timestamp, dtype: datetime64[ns]\n42.857142857142854% data for which Low percentage change in current day last price to previous day last price is greater than allowed 0.0% with highest jump: 269.2857197352818 on 0   2024-02-06 09:20:00\n0   2024-02-07 09:20:00\nName: timestamp, dtype: datetime64[ns]\n71.42857142857143% data for which High percentage change in current day first price to previous day last price is greater than allowed 0.0% with highest jump: 230.53891162351908 on 4   2024-02-06 09:30:00\n4   2024-02-07 09:40:00\nName: timestamp, dtype: datetime64[ns]\n42.857142857142854% data for which High percentage change in current day last price to previous day last price is greater than allowed 0.0% with highest jump: 228.1436930151069 on 0   2024-02-06 09:20:00\n0   2024-02-07 09:20:00\nName: timestamp, dtype: datetime64[ns]\n100.0% data for which Close percentage change in current day first price to previous day last price is greater than allowed 0.0% with highest jump: 153.57142857142858 on 4   2024-02-06 09:30:00\n4   2024-02-07 09:40:00\nName: timestamp, dtype: datetime64[ns]\n42.857142857142854% data for which Close percentage change in current day last price to previous day last price is greater than allowed 0.0% with highest jump: 275.71427481515065 on 0   2024-02-06 09:20:00\n0   2024-02-07 09:20:00\nName: timestamp, dtype: datetime64[ns]\nPassed\n\n
        """
        result = create_metadata_from_report(column_list, checked_messages)
        assert result == {
            "Open": {
                "nan_ratio": 0.0,
                "intraday_jump": 0.0,
                "overnight_open_jump": 1.0179640253506037,
                "overnight_close_jump": 2.251496811758404,
            },
            "Close": {
                "nan_ratio": 1.0,
                "intraday_jump": 0.248546478424672,
                "overnight_open_jump": 1.5357142857142858,
                "overnight_close_jump": 2.7571427481515065,
            },
            "Low": {
                "nan_ratio": 0.0,
                "intraday_jump": 0.0,
                "overnight_open_jump": 1.3214285714285714,
                "overnight_close_jump": 2.692857197352818,
            },
            "length": "0",
            "start_timestamp": "",
            "last_timestamp": "",
        }

    def test_previous_date(self):
        all_dates = [
            pd.Timestamp("2023-10-01"),
            pd.Timestamp("2023-10-02"),
            pd.Timestamp("2023-10-03"),
            pd.Timestamp("2023-10-04"),
            pd.Timestamp("2023-10-05"),
        ]
        day = pd.Timestamp("2023-10-05")
        lookback = 2

        result = previous_date(all_dates, day, lookback)
        assert result == pd.Timestamp("2023-10-03")

    def test_parse_to_date_success(self):
        date_str = "01-Oct-2023"
        expected_date = pd.Timestamp("2023-10-01")

        result = parse_to_date(date_str)
        assert result == expected_date

    def test_calculate_corpact_adj_factor(self):
        df = pd.DataFrame(
            {
                "symbol": ["AAPL", "GOOGL", "MSFT", "AMZN", "AMZN"],
                "face_value": [100, 150, 200, 300, 300],
                "purpose": [
                    "Dividend 2.50",
                    "Bonus 2 for 1",
                    "Rights 1 for 2 @ 150",
                    "Split 2 for 1",
                    "Dividend 2.50 3.5",
                ],
                "exdate": [
                    "03-Oct-2023",
                    "03-Oct-2023",
                    "03-Oct-2023",
                    "03-Oct-2023",
                    "03-Oct-2023",
                ],
            }
        )

        df_bhav = pd.DataFrame(
            {
                "symbol": ["AAPL", "GOOGL", "MSFT", "AMZN"],
                "timestamp": ["2023-10-02", "2023-10-02", "2023-10-02", "2023-10-02"],
                "close": [150.0, 2500.0, 300.0, 3500.0],
            }
        )
        date = pd.Timestamp("2023-10-03")
        all_dates = [
            pd.Timestamp("2023-10-01"),
            pd.Timestamp("2023-10-02"),
            pd.Timestamp("2023-10-03"),
        ]

        result_df, result_bad_corpact_df = calculate_corpact_adj_factor(
            df, df_bhav, date, all_dates
        )

        assert len(result_df) == 3
        assert compare_floats(result_df.iloc[0]["adj_factor"], 0.983333)
        assert compare_floats(result_df.iloc[1]["adj_factor"], 0.333333)
        assert compare_floats(result_df.iloc[2]["adj_factor"], 0.500000)
        assert len(result_bad_corpact_df) == 1
        assert (
            result_bad_corpact_df.iloc[0]["Bad Corpact"]
            == "Bad dividend for corporate action:AMZN Dividend 2.50 3.5"
        )

    def test_get_merger_demerger(self):
        # Sample data for corporate actions
        corpact_df = pd.DataFrame(
            {
                "symbol": ["AAPL", "GOOGL", "MSFT", "AMZN", "FB"],
                "face_value": [100, 150, 200, 300, 400],
                "purpose": [
                    "Dividend 2.50",
                    "Merger with XYZ",
                    "Bonus 2 for 1",
                    "Arrangement with ABC",
                    "Arngment with DEF",
                ],
                "exdate": [
                    "01-Oct-2023",
                    "02-Oct-2023",
                    "03-Oct-2023",
                    "04-Oct-2023",
                    "05-Oct-2023",
                ],
            }
        )

        # Expected results
        expected_df = pd.DataFrame(
            {
                "symbol": ["GOOGL", "AMZN", "FB"],
                "face_value": [150, 300, 400],
                "purpose": [
                    "Merger with XYZ",
                    "Arrangement with ABC",
                    "Arngment with DEF",
                ],
                "exdate": ["02-Oct-2023", "04-Oct-2023", "05-Oct-2023"],
            }
        ).reset_index(drop=True)

        # Call the function
        result_df = get_merger_demerger(corpact_df)

        assert len(result_df) == 3
        assert result_df.iloc[0].purpose == "merger with xyz"

    def test_stack_column_bucket_data(self):
        # Sample data for column bucket DataFrame
        data = {"09:00": [10, 20], "10:00": [15, 25], "11:00": [12, 22]}
        index = pd.MultiIndex.from_tuples(
            [("2023-10-01", 1), ("2023-10-01", 2)], names=["date", "ID"]
        )
        df = pd.DataFrame(data, index=index)

        # Expected results
        expected_data = {"ID": [1, 1, 1, 2, 2, 2], "value": [10, 15, 12, 20, 25, 22]}
        expected_index = pd.to_datetime(
            [
                "2023-10-01 09:00:00",
                "2023-10-01 10:00:00",
                "2023-10-01 11:00:00",
                "2023-10-01 09:00:00",
                "2023-10-01 10:00:00",
                "2023-10-01 11:00:00",
            ]
        )
        expected_df = pd.DataFrame(expected_data, index=expected_index)
        result_df = stack_column_bucket_data(df, "value")

        assert len(result_df) == len(expected_df)
        assert (result_df.iloc[0].index == expected_df.iloc[0].index).all() == True

    def test_round_to_nearest_lotsize(self):
        data = {
            "timestamp": pd.to_datetime(
                ["2023-10-01 09:00:00", "2023-10-01 10:00:00", "2023-10-01 11:00:00"]
            ),
            "near_month": [123.45, 234.56, 345.67],
            "next_month": [456.78, 567.89, 678.90],
            "far_month": [789.01, 890.12, 901.23],
        }
        df = pd.DataFrame(data).set_index("timestamp")

        lotsize_data = {
            "date": [
                pd.Timestamp("2023-10-01"),
                pd.Timestamp("2023-10-01"),
                pd.Timestamp("2023-10-01"),
            ],
            "ID": [1, 2, 3],
            "near_month": [10, 20, 30],
            "next_month": [40, 50, 60],
            "far_month": [70, 80, 90],
        }
        lotsize_df = pd.DataFrame(lotsize_data)
        lotsize_df = lotsize_df.set_index("date")

        # Expected results
        expected_data = {
            "near_month": [120, 240, 360],
            "next_month": [440, 550, 660],
            "far_month": [770, 880, 900],
        }
        expected_index = pd.to_datetime(
            ["2023-10-01 09:00:00", "2023-10-01 10:00:00", "2023-10-01 11:00:00"]
        )
        expected_df = pd.DataFrame(expected_data, index=expected_index)

        result_df = round_to_nearest_lotsize(df, lotsize_df)
        assert (result_df.iloc[0] == expected_df.iloc[0]).all()

    def test_round_to_nearest_lotsize_column_bucket(self):
        data = {
            "09:00": [123.45, 234.56, 345.67],
            "10:00": [456.78, 567.89, 678.90],
            "11:00": [789.01, 890.12, 901.23],
        }
        index = pd.MultiIndex.from_tuples(
            [
                (pd.Timestamp("2023-10-01"), 1),
                (pd.Timestamp("2023-10-01"), 2),
                (pd.Timestamp("2023-10-01"), 3),
            ],
            names=["date", "ID"],
        )
        df = pd.DataFrame(data, index=index)

        lotsize_data = {
            "date": [
                pd.Timestamp("2023-10-01"),
                pd.Timestamp("2023-10-01"),
                pd.Timestamp("2023-10-01"),
            ],
            "ID": [1, 2, 3],
            "near_month": [10, 20, 30],
            "next_month": [40, 50, 60],
            "far_month": [70, 80, 90],
        }
        lotsize_df = pd.DataFrame(lotsize_data)
        lotsize_df = lotsize_df.set_index("date")

        # Expected results
        expected_data = {
            "09:00": [
                120,
                240,
                360,
            ],  # 123.45 -> 12 / 10 * 10 = 120, 234.56 -> 11.728 * 20 = 240, 345.67 -> 11.5223 * 30 = 360
            "10:00": [
                460,
                560,
                690,
            ],  # 456.78 -> 11.4195 * 40 = 440, 567.89 -> 11.3578 * 50 = 560, 678.90 -> 11.315 * 60 = 680
            "11:00": [
                790,
                900,
                900,
            ],  # 789.01 -> 11.27157 * 70 = 770, 890.12 -> 11.1265 * 80 = 880, 901.23 -> 10.01755 * 90 = 900
        }
        expected_index = pd.MultiIndex.from_tuples(
            [("2023-10-01", 1), ("2023-10-01", 2), ("2023-10-01", 3)],
            names=["date", "ID"],
        )
        expected_df = pd.DataFrame(expected_data, index=expected_index)

        result_df = round_to_nearest_lotsize_column_bucket(df, lotsize_df, "near_month")
        assert (result_df.iloc[0] == expected_df.iloc[0]).all()

    @time_machine.travel(
        "2024-07-01 12:00:00", tick=False
    )  # Mid-year (DST active in US/Eastern)
    def test_find_timezone_offset_us_eastern_dst(self):
        base_tz = pytz.timezone("US/Eastern")
        offset = 0  # No internal shift

        result = find_timezone_offset(base_tz, offset)

        # IST is UTC+5:30, EDT is UTC-4 in July, so 5.5 - (-4) = 9.5 hours = 570 minutes
        # 330 - 570 = -240
        expected_minutes = -240
        assert result == pytz.FixedOffset(expected_minutes)

    @time_machine.travel(
        "2024-12-01 12:00:00", tick=False
    )  # Winter (Standard Time in US)
    def test_find_timezone_offset_us_eastern_standard(self):
        base_tz = pytz.timezone("US/Eastern")
        offset = 60  # 1 hour internal shift

        result = find_timezone_offset(base_tz, offset)
        expected_minutes = -240
        assert result == pytz.FixedOffset(expected_minutes)

    @time_machine.travel("2024-07-01 12:00:00", tick=False)
    def test_find_timezone_offset_half_hour_timezone(self):
        base_tz = pytz.timezone("Australia/Adelaide")  # UTC+9:30 or +10:30
        offset = 0

        result = find_timezone_offset(base_tz, offset)
        assert result == pytz.FixedOffset(570)

    @time_machine.travel(
        "2024-12-01 12:00:00", tick=False
    )  # Australian summer (DST active)
    def test_find_timezone_offset_half_hour_timezone_dst(self):
        base_tz = pytz.timezone("Australia/Adelaide")  # UTC+10:30 during DST
        offset = 0

        result = find_timezone_offset(base_tz, offset)
        assert result == pytz.FixedOffset(630)

    def test_split_dates_by_gap(self):
        # Sample data for the dates list
        dates = [
            pd.Timestamp("2023-10-01"),
            pd.Timestamp("2023-10-02"),
            pd.Timestamp("2023-10-05"),  # Gap of 2 days
            pd.Timestamp("2023-10-06"),
            pd.Timestamp("2023-10-10"),  # Gap of 3 days
        ]

        # Lookback period in days
        lookback = 2

        # Sample data for the all_dates list
        all_dates = [
            pd.Timestamp("2023-09-29"),
            pd.Timestamp("2023-09-30"),
            pd.Timestamp("2023-10-01"),
            pd.Timestamp("2023-10-02"),
            pd.Timestamp("2023-10-03"),
            pd.Timestamp("2023-10-04"),
            pd.Timestamp("2023-10-05"),
            pd.Timestamp("2023-10-06"),
            pd.Timestamp("2023-10-07"),
            pd.Timestamp("2023-10-08"),
            pd.Timestamp("2023-10-09"),
            pd.Timestamp("2023-10-10"),
        ]

        # Expected results
        expected_result = [
            [pd.Timestamp("2023-09-29"), pd.Timestamp("2023-10-02")],
            [pd.Timestamp("2023-10-03"), pd.Timestamp("2023-10-06")],
            [pd.Timestamp("2023-10-08"), pd.Timestamp("2023-10-10")],
        ]

        # Call the function
        result = split_dates_by_gap(dates, lookback, all_dates)

        assert len(result) == len(expected_result)
        assert result == expected_result
