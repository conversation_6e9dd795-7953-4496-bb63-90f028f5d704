import pytest
import pandas as pd
import numpy as np
from main.data.auditor import Auditor
from main.enums import StorageType
from main.config.config_base import ConfigBase
from test.utility.mocker.dbStore_mocker import DbStoreMocker  # type: ignore
from typing import cast


class MockConfig(ConfigBase):
    def __init__(self):
        self.DATE_TODAY = pd.Timestamp.now().normalize()
        self.UNIVERSE_DTYPE_TO_COLUMN_DICT = {
            "opt_trd": ["ID", "timestamp", "Close", "Cons_Volume"]
        }
        self.COLUMNS_DICT = {"opt": ["ID", "timestamp", "Close", "Cons_Volume"]}
        self.LIBRARY_KEYWORDS_TO_SKIP_CHECK = (
            ConfigBase().LIBRARY_KEYWORDS_TO_SKIP_CHECK.copy()
        )


@pytest.fixture(scope="class")
def auditor_obj():
    auditor = Auditor(config=MockConfig())
    auditor._Auditor__stores[StorageType.DB] = DbStoreMocker()
    return auditor


@pytest.mark.usefixtures("auditor_obj")
class TestAuditor:
    def test_auditor_list_file_locations_success(self, auditor_obj: Auditor):
        expected_file_locations = ["nse/5_min/opt/trd", "nse/5_min/opt/ord"]
        output_file_locations = auditor_obj.list_file_locations(
            storage_type=StorageType.DB
        )
        assert output_file_locations == expected_file_locations

    def test_auditor_list_file_names_success(self, auditor_obj: Auditor):
        expected_file_names = ["5001", "5002", "5003", "5008"]
        output_file_names = auditor_obj.list_file_names(
            storage_type=StorageType.DB, file_location="nse/5_min/opt/trd"
        )
        assert output_file_names == expected_file_names

    def test_auditor_list_file_names_invalid_library_raise_exception(
        self, auditor_obj: Auditor
    ):
        expected_message = "An error occurred while listing the file names at invalid_library from store StorageType.DB"
        with pytest.raises(Exception) as exception_info:
            auditor_obj.list_file_names(
                storage_type=StorageType.DB, file_location="invalid_library"
            )
        assert str(exception_info.value) == expected_message

    def test_auditor_read_success_when_both_dates_none(self, auditor_obj: Auditor):
        expected_output = pd.read_parquet(
            "./test/utility/test_data/arctic_test_data.parquet"
        )
        read_output = cast(
            pd.DataFrame,
            auditor_obj.read(
                storage_type=StorageType.DB,
                file_location="nse/5_min/opt/trd",
                file_name="symbol",
            ),
        )
        assert read_output.iloc[0]["ID"] == 147502023012505001
        assert read_output.equals(expected_output)

    def test_auditor_read_success_when_only_end_date_none(self, auditor_obj: Auditor):
        expected_output = pd.read_parquet(
            "./test/utility/test_data/arctic_test_data.parquet"
        )
        read_output = cast(
            pd.DataFrame,
            auditor_obj.read(
                storage_type=StorageType.DB,
                file_location="nse/5_min/opt/trd",
                file_name="symbol",
                start_date=pd.Timestamp("2023-01-23"),
            ),
        )
        assert read_output.iloc[0]["ID"] == 147502023012505001
        assert read_output.equals(expected_output)

    def test_auditor_read_success_when_both_dates_defined(self, auditor_obj: Auditor):
        expected_output = pd.read_parquet(
            "./test/utility/test_data/arctic_test_data.parquet"
        )
        read_output = cast(
            pd.DataFrame,
            auditor_obj.read(
                storage_type=StorageType.DB,
                file_location="nse/5_min/opt/trd",
                file_name="symbol",
                start_date=pd.Timestamp("2023-01-23"),
                end_date=pd.Timestamp("2023-01-24"),
            ),
        )
        assert read_output.iloc[0]["ID"] == 147502023012505001
        assert read_output.equals(expected_output)

    def test_auditor_read_raise_dateerror_exception_with_both_dates_defined(
        self, auditor_obj: Auditor
    ):
        expected_message = "DateRangeError: start_date 2022-09-09 00:00:00 can't be more than end_date 2022-07-09 00:00:00"
        with pytest.raises(Exception) as exception_info:
            auditor_obj.read(
                storage_type=StorageType.DB,
                file_location="nse/5_min/opt/trd",
                file_name="invalid_symbol",
                start_date=pd.Timestamp("2022-09-09"),
                end_date=pd.Timestamp("2022-07-09"),
            )
        assert str(exception_info.value) == expected_message

    def test_auditor_read_raise_dateerror_exception_when_start_date_not_defined(
        self, auditor_obj: Auditor
    ):
        expected_message = "DateRangeError: start_date can't be None since end_date 2022-09-09 00:00:00 is provided"
        with pytest.raises(Exception) as exception_info:
            auditor_obj.read(
                storage_type=StorageType.DB,
                file_location="nse/5_min/opt/trd",
                file_name="invalid_symbol",
                end_date=pd.Timestamp("2022-09-09"),
            )
        assert str(exception_info.value) == expected_message

    def test_auditor_read_raise_readerror_exception(self, auditor_obj: Auditor):
        expected_message = "ReadError: Found error during reading invalid_symbol at nse/5_min/opt/trd from store StorageType.DB due to:\nSome error occured while reading from db store"
        with pytest.raises(Exception) as exception_info:
            auditor_obj.read(
                storage_type=StorageType.DB,
                file_location="nse/5_min/opt/trd",
                file_name="invalid_symbol",
            )
        assert str(exception_info.value) == expected_message

    def test_auditor_append_success(self, auditor_obj: Auditor):
        append_call = auditor_obj.append(
            storage_type=StorageType.DB,
            file_location="nse/5_min/opt/trd",
            file_name="symbol",
            data=pd.DataFrame(),
            metadata={},
        )
        assert append_call == 1

    def test_auditor_append_raise_exception(self, auditor_obj: Auditor):
        expected_message = "AppendError: Found error during appending invalid_symbol at nse/5_min/opt/trd from store StorageType.DB due to:\nFileError: Invalid file location or file name"
        with pytest.raises(Exception) as exception_info:
            auditor_obj.append(
                storage_type=StorageType.DB,
                file_location="nse/5_min/opt/trd",
                file_name="invalid_symbol",
                data=pd.DataFrame(),
                metadata={},
            )
        assert str(exception_info.value) == expected_message

    def test_auditor_update_success(self, auditor_obj: Auditor):
        update_call = auditor_obj.update(
            storage_type=StorageType.DB,
            file_location="nse/5_min/opt/trd",
            file_name="symbol",
            data=pd.DataFrame(),
            metadata={},
        )
        assert update_call == 1

    def test_auditor_update_raise_exception(self, auditor_obj: Auditor):
        expected_message = "UpdateError: Found error during updating invalid_symbol at nse/5_min/opt/trd from store StorageType.DB due to:\nFileError: Invalid file location or file name"
        with pytest.raises(Exception) as exception_info:
            auditor_obj.update(
                storage_type=StorageType.DB,
                file_location="nse/5_min/opt/trd",
                file_name="invalid_symbol",
                data=pd.DataFrame(),
                metadata={},
            )
        assert str(exception_info.value) == expected_message

    def test_auditor_write_success(self, auditor_obj: Auditor):
        write_call = auditor_obj.write(
            storage_type=StorageType.DB,
            file_location="nse/5_min/opt/trd",
            file_name="symbol",
            data=pd.DataFrame(),
            metadata={},
        )
        assert write_call == 1

    def test_auditor_write_raise_exception(self, auditor_obj: Auditor):
        expected_message = "WriteError: Found error during writing invalid_symbol at nse/5_min/opt/trd from store StorageType.DB: Error writing to db store"
        with pytest.raises(Exception) as exception_info:
            auditor_obj.write(
                storage_type=StorageType.DB,
                file_location="nse/5_min/opt/trd",
                file_name="invalid_symbol",
                data=pd.DataFrame(),
                metadata={},
            )
        assert str(exception_info.value) == expected_message

    def test_auditor_read_metadata_success(self, auditor_obj: Auditor):
        read_metadata_output = auditor_obj.read_metadata(
            storage_type=StorageType.DB,
            file_location="nse/5_min/opt/trd",
            file_name="symbol",
        )
        column_value = read_metadata_output["column"]

        assert isinstance(column_value, str) or (
            isinstance(column_value, dict)
            and all(
                isinstance(k, str) and isinstance(v, float)
                for k, v in column_value.items()
            )
        )
        assert read_metadata_output["column"]["nan_ratio"] == 0.2

    def test_auditor_read_metadata_is_none(self, auditor_obj: Auditor):
        metadata = auditor_obj.read_metadata(
            storage_type=StorageType.DB,
            file_location="nse/5_min/opt/trd",
            file_name="invalid_symbol",
        )
        assert metadata is None

    def test_auditor_write_metadata_success(self, auditor_obj: Auditor):
        write_metadata_call = auditor_obj.write_metadata(
            storage_type=StorageType.DB,
            file_location="nse/5_min/opt/trd",
            file_name="symbol",
            metadata={},
        )
        assert write_metadata_call == True

    def test_auditor_write_metadata_raise_exception(self, auditor_obj: Auditor):
        expected_message = "MetadataWriteError: Found error during writing metadata_dict to invalid_symbol located at nse/5_min/opt/trd inside store: StorageType.DB: Error writing metadata to db store"
        with pytest.raises(Exception) as exception_info:
            auditor_obj.write_metadata(
                storage_type=StorageType.DB,
                file_location="nse/5_min/opt/trd",
                file_name="invalid_symbol",
                metadata={},
            )
        assert str(exception_info.value) == expected_message

    def test_auditor_update_operation_history_success(self, auditor_obj: Auditor):
        output = auditor_obj.update_operation_history(
            storage_type=StorageType.DB,
            file_location="nse/5_min/opt/trd",
            file_name="symbol",
            version_number=0,
            operation_type="read",
            username="mantraraj",
            comment="",
        )
        assert output is None

    def test_auditor_update_operation_history_different_storage_raise_exception(
        self, auditor_obj: Auditor
    ):
        expected_message = "OperationAuditError: invalid store StorageType.FILE for writing operation history for the library nse/5_min/opt/trd"
        with pytest.raises(Exception) as exception_info:
            auditor_obj.update_operation_history(
                storage_type=StorageType.FILE,
                file_location="nse/5_min/opt/trd",
                file_name="symbol",
                version_number=0,
                operation_type="read",
                username="mantraraj",
                comment="",
            )
        assert str(exception_info.value) == expected_message

    def test_auditor_update_operation_history_invalid_library_raise_exception(
        self, auditor_obj: Auditor
    ):
        expected_message = "OperationAuditError: Found error during writing operation history for symbol in invalid_library: FileError: Invalid file location or file name"
        with pytest.raises(Exception) as exception_info:
            auditor_obj.update_operation_history(
                storage_type=StorageType.DB,
                file_location="invalid_library",
                file_name="symbol",
                version_number=0,
                operation_type="read",
                username="mantraraj",
                comment="",
            )
        assert str(exception_info.value) == expected_message

    def test_update_metadata_with_corpact_applied(
        self, auditor_obj: Auditor, monkeypatch
    ):
        def mock_read_metadata(*args, **kwargs):
            return {
                "start_timestamp": "2023-01-01",
                "last_timestamp": "2023-01-31",
                "length": "100",
            }

        monkeypatch.setattr(auditor_obj, "read_metadata", mock_read_metadata)

        data = pd.DataFrame(
            {
                "timestamp": [pd.Timestamp("2023-02-01")],
                "Close": [1],
                "Cons_Volume": [100],
            }
        )
        result = auditor_obj.update_metadata(
            storage_type=StorageType.DB,
            file_location="nse/5_min/opt/trd",
            file_name="symbol",
            data=data,
            operation="append",
            checker_report="corpact_applied",
        )

        assert "last_corpact_applied" in result
        assert result["last_corpact_applied"] == str(auditor_obj._config.DATE_TODAY)

    def test_update_metadata_with_missing_metadata(
        self, auditor_obj: Auditor, monkeypatch
    ):
        def mock_read_metadata(*args, **kwargs):
            return None

        monkeypatch.setattr(auditor_obj, "read_metadata", mock_read_metadata)

        data = pd.DataFrame(
            {
                "timestamp": [pd.Timestamp("2023-02-01")],
                "Close": [1],
                "Cons_Volume": [100],
            }
        )

        with pytest.raises(Exception) as exception_info:
            auditor_obj.update_metadata(
                storage_type=StorageType.DB,
                file_location="nse/5_min/opt/trd",
                file_name="symbol",
                data=data,
                operation="append",
                checker_report="some report",
            )

        assert (
            "MetadataReadError: Not available for symbol in nse/5_min/opt/trd"
            == str(exception_info.value)
        )

    def test_update_metadata_with_data_already_appended(
        self, auditor_obj: Auditor, monkeypatch
    ):
        def mock_read_metadata(*args, **kwargs):
            return {
                "start_timestamp": "2023-01-01 00:00:00",
                "last_timestamp": "2023-02-01 00:00:00",
                "length": "100",
                "timestamp": {
                    "nan_ratio": 0.0,
                    "intraday_jump": 0.0,
                    "overnight_open_jump": 0.0,
                    "overnight_close_jump": 0.0,
                },
                "Close": {
                    "nan_ratio": 0.1,
                    "intraday_jump": 0.2,
                    "overnight_open_jump": 0.3,
                    "overnight_close_jump": 0.4,
                },
                "Cons_Volume": {
                    "nan_ratio": 0.1,
                    "intraday_jump": 0.2,
                    "overnight_open_jump": 0.3,
                    "overnight_close_jump": 0.4,
                },
            }

        monkeypatch.setattr(auditor_obj, "read_metadata", mock_read_metadata)

        data = pd.DataFrame(
            {
                "Close": [1],
                "Cons_Volume": [100],
            },
            index=pd.Index([pd.Timestamp("2023-02-01")], name="timestamp"),
        )

        with pytest.raises(Exception) as exception_info:
            auditor_obj.update_metadata(
                storage_type=StorageType.DB,
                file_location="nse/5_min/opt/trd",
                file_name="symbol",
                data=data,
                operation="append",
                checker_report="some report",
            )

        assert "Data is already appended for symbol in nse/5_min/opt/trd" == str(
            exception_info.value
        )

    def test_update_metadata_exception_handling(
        self, auditor_obj: Auditor, monkeypatch
    ):
        def mock_read_metadata(*args, **kwargs):
            return {
                "start_timestamp": "2023-01-01",
                "last_timestamp": "2023-01-31",
                "length": "100",
                "timestamp": {
                    "nan_ratio": 0.0,
                    "intraday_jump": 0.0,
                    "overnight_open_jump": 0.0,
                    "overnight_close_jump": 0.0,
                },
                "Cons_Volume": {
                    "nan_ratio": 0.1,
                    "intraday_jump": 0.2,
                    "overnight_open_jump": 0.3,
                    "overnight_close_jump": 0.4,
                },
                # Missing column metadata for 'Close'
            }

        expected_message = "MetadataUpdateError: Unexpected column Close found in data while updating metadata_dict after appending to symbol located at nse/5_min/opt/trd inside store: StorageType.DB: 'Close'"
        monkeypatch.setattr(auditor_obj, "read_metadata", mock_read_metadata)

        data = pd.DataFrame(
            {
                "timestamp": [pd.Timestamp("2023-02-01")],
                "Close": [1],
                "Cons_Volume": [100],
            }
        ).set_index("timestamp")

        with pytest.raises(Exception) as exception_info:
            auditor_obj.update_metadata(
                storage_type=StorageType.DB,
                file_location="nse/5_min/opt/trd",
                file_name="symbol",
                data=data,
                operation="append",
                checker_report="some report",
            )

        assert str(exception_info.value) == expected_message

    def test_update_metadata_with_missing_start_timestamp(
        self, auditor_obj: Auditor, monkeypatch
    ):
        def mock_read_metadata(*args, **kwargs):
            return {
                "last_timestamp": "2023-01-31 00:00:00",
                "length": "100",
                "timestamp": {
                    "nan_ratio": 0.0,
                    "intraday_jump": 0.0,
                    "overnight_open_jump": 0.0,
                    "overnight_close_jump": 0.0,
                },
                "Close": {
                    "nan_ratio": 0.1,
                    "intraday_jump": 0.2,
                    "overnight_open_jump": 0.3,
                    "overnight_close_jump": 0.4,
                },
                "Cons_Volume": {
                    "nan_ratio": 0.1,
                    "intraday_jump": 0.2,
                    "overnight_open_jump": 0.3,
                    "overnight_close_jump": 0.4,
                },
            }

        monkeypatch.setattr(auditor_obj, "read_metadata", mock_read_metadata)

        data = pd.DataFrame(
            {
                "timestamp": [pd.Timestamp("2023-02-01")],
                "Close": [1],
                "Cons_Volume": [100],
            }
        ).set_index("timestamp")
        result = auditor_obj.update_metadata(
            storage_type=StorageType.DB,
            file_location="nse/5_min/opt/trd",
            file_name="symbol",
            data=data,
            operation="update",
            checker_report="some report",
        )
        assert "start_timestamp" in result
        assert result["start_timestamp"] == "2023-02-01 00:00:00"

    def test_update_metadata_with_standard_universe(
        self, auditor_obj: Auditor, monkeypatch
    ):
        def mock_read_metadata(*args, **kwargs):
            return {
                "start_timestamp": "2023-01-01 00:00:00",
                "last_timestamp": "2023-01-31 00:00:00",
                "length": "100",
                "Close": {
                    "nan_ratio": 0.1,
                    "intraday_jump": 0.2,
                    "overnight_open_jump": 0.3,
                    "overnight_close_jump": 0.4,
                },
                "Cons_Volume": {
                    "nan_ratio": 0.1,
                    "intraday_jump": 0.2,
                    "overnight_open_jump": 0.3,
                    "overnight_close_jump": 0.4,
                },
                "timestamp": {
                    "nan_ratio": 0.0,
                    "intraday_jump": 0.0,
                    "overnight_open_jump": 0.0,
                    "overnight_close_jump": 0.0,
                },
            }

        monkeypatch.setattr(auditor_obj, "read_metadata", mock_read_metadata)

        data = pd.DataFrame(
            {
                "timestamp": [pd.Timestamp("2023-02-01")],
                "Close": [1],
                "Cons_Volume": [100],
            }
        ).set_index("timestamp")

        def mock_create_metadata_from_report(*args, **kwargs):
            return {
                "Close": {
                    "nan_ratio": 0.0,
                    "intraday_jump": 0.5,
                    "overnight_open_jump": 0.6,
                    "overnight_close_jump": 0.7,
                },
                "Cons_Volume": {
                    "nan_ratio": 0.0,
                    "intraday_jump": 0.5,
                    "overnight_open_jump": 0.6,
                    "overnight_close_jump": 0.7,
                },
                "timestamp": {
                    "nan_ratio": 0.0,
                    "intraday_jump": 0.0,
                    "overnight_open_jump": 0.0,
                    "overnight_close_jump": 0.0,
                },
            }

        monkeypatch.setattr(
            "main.data.auditor.create_metadata_from_report",
            mock_create_metadata_from_report,
        )

        result = auditor_obj.update_metadata(
            storage_type=StorageType.DB,
            file_location="nse/5_min/slippages/trd",
            file_name="symbol",
            data=data,
            operation="append",
            checker_report="some report",
        )

        assert "last_timestamp" in result
        assert result["last_timestamp"] == "2023-02-01 00:00:00"
        assert result["length"] == "101"

    def test_update_metadata_nan_ratio_and_jumps_calculation(
        self, auditor_obj: Auditor, monkeypatch
    ):
        def mock_read_metadata(*args, **kwargs):
            return {
                "start_timestamp": "2023-01-01",
                "last_timestamp": "2023-01-31",
                "length": "100",
                "timestamp": {
                    "nan_ratio": 0.0,
                    "intraday_jump": 0.0,
                    "overnight_open_jump": 0.0,
                    "overnight_close_jump": 0.0,
                },
                "Close": {
                    "nan_ratio": 0.05,
                    "intraday_jump": 0.2,
                    "overnight_open_jump": 0.3,
                    "overnight_close_jump": 0.4,
                },
                "Cons_Volume": {
                    "nan_ratio": 0.02,
                    "intraday_jump": 0.1,
                    "overnight_open_jump": 0.2,
                    "overnight_close_jump": 0.3,
                },
            }

        def mock_create_metadata_from_report(*args, **kwargs):
            return {
                "timestamp": {
                    "nan_ratio": 0.0,
                    "intraday_jump": 0.0,
                    "overnight_open_jump": 0.0,
                    "overnight_close_jump": 0.0,
                },
                "Close": {
                    "nan_ratio": 0.0,
                    "intraday_jump": 0.5,
                    "overnight_open_jump": 0.4,
                    "overnight_close_jump": 0.6,
                },
                "Cons_Volume": {
                    "nan_ratio": 0.0,
                    "intraday_jump": 0.05,
                    "overnight_open_jump": 0.4,
                    "overnight_close_jump": 0.2,
                },
            }

        monkeypatch.setattr(auditor_obj, "read_metadata", mock_read_metadata)
        monkeypatch.setattr(
            "main.data.auditor.create_metadata_from_report",
            mock_create_metadata_from_report,
        )

        data = pd.DataFrame(
            {
                "timestamp": [pd.Timestamp(f"2023-02-{i + 1}") for i in range(20)],
                "Close": [np.nan if i % 5 == 0 else i for i in range(20)],
                "Cons_Volume": [np.nan if i % 10 == 0 else i * 100 for i in range(20)],
            }
        ).set_index("timestamp")

        result = auditor_obj.update_metadata(
            storage_type=StorageType.DB,
            file_location="nse/5_min/opt/trd",
            file_name="symbol",
            data=data,
            operation="append",
            checker_report="some report",
        )

        # Verify nan_ratio calculations
        assert round(result["timestamp"]["nan_ratio"], 3) == 0
        assert round(result["Close"]["nan_ratio"], 3) == 0.075
        assert round(result["Cons_Volume"]["nan_ratio"], 3) == 0.033

        # Verify jump values are updated to the maximum
        assert result["timestamp"]["intraday_jump"] == 0
        assert result["timestamp"]["overnight_open_jump"] == 0
        assert result["timestamp"]["overnight_close_jump"] == 0

        assert result["Close"]["intraday_jump"] == 0.5
        assert result["Close"]["overnight_open_jump"] == 0.4
        assert result["Close"]["overnight_close_jump"] == 0.6

        assert result["Cons_Volume"]["intraday_jump"] == 0.1
        assert result["Cons_Volume"]["overnight_open_jump"] == 0.4
        assert result["Cons_Volume"]["overnight_close_jump"] == 0.3
