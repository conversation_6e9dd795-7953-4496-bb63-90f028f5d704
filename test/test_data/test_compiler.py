import numpy as np
import pytest
from mock import patch  # type: ignore
import pandas as pd
from main.data.compiler import Compiler
from main.config.config_factory import ConfigFactory


@pytest.fixture(scope="class")
def compiler_obj():
    def mock_read(args, **kwargs):
        if kwargs["file_name"] == "symbol_change":
            symbol_change = pd.DataFrame(columns=["symbol", "current"])
            symbol_change.index.name = "date"
            return symbol_change
        elif kwargs["file_name"] == "demerger_merger":
            demerger_merger = pd.DataFrame(columns=["Symbol", "ID"])
            demerger_merger.index.name = "exdate"
            return demerger_merger
        elif "mapping_dict" in kwargs["file_name"]:
            return {
                "NIFTY": 5001,
                "BANKNIFTY": 5002,
                "FINNIFTY": 5003,
                "NIFTYNXT50": 5004,
                "MIDCPNIFTY": 5008,
                "SBIN": 1075,
            }
        elif kwargs["file_name"] == "corpact":
            corpact_data = pd.DataFrame(columns=["ID", "adj_factor"])
            corpact_data.index.name = "exdate"
            return corpact_data
        elif (
            kwargs["file_name"]
            == "nse_5min_raw_opt_oi_trd_test_symbol_20240101_20240102"
        ):
            return pd.read_parquet(
                "./test/utility/test_data/option_oi_test_data.parquet"
            )
        elif (
            kwargs["file_name"]
            == "nse_5min_raw_futidx_trd_test_symbol_20240101_20240102"
        ):
            return pd.read_parquet(
                "./test/utility/test_data/underlying_test_data.parquet"
            )
        elif (
            kwargs["file_name"] == "nse_5min_opt_trd_test_symbol_20240101_20240102"
            or kwargs["file_name"]
            == "nse_5min_raw_opt_trd_test_symbol_20240101_20240102"
        ):
            return pd.read_parquet("./test/utility/test_data/option_test_data.parquet")
        elif kwargs["file_name"] == "SBIN":
            return pd.read_parquet(
                "./test/utility/test_data/compiler_cash_test_data.parquet"
            )
        elif kwargs["file_name"] == "nse_1min_raw_cash_trd_1075_20240101_20240102":
            raw_cash = pd.read_parquet(
                "./test/utility/test_data/compiler_cash_test_data.parquet"
            )
            raw_cash["Symbol"] = "SBIN"
            raw_cash["ID"] = 1075
            return raw_cash
        elif (
            kwargs["file_name"]
            == "nse_1min_raw_futidx_fut_trd_test_symbol_20240101_20240102"
            or kwargs["file_name"]
            == "nse_1min_raw_futidx_fut_trd_5001_20240101_20240102"
        ):
            raw_futidx_fut = pd.read_parquet(
                "./test/utility/test_data/option_test_data.parquet"
            )
            raw_futidx_fut["ID"] = raw_futidx_fut["ID"] % int(1e13)
            if "5001" in kwargs["file_name"]:
                raw_futidx_fut = raw_futidx_fut[raw_futidx_fut["ID"] % 10000 == 5001]
            return raw_futidx_fut
        elif "expiry_dict" in kwargs["file_name"]:
            return {
                pd.Timestamp("2024-01-01"): {
                    "near_month": pd.Timestamp("2024-01-25"),
                    "next_month": pd.Timestamp("2024-02-29"),
                    "far_month": pd.Timestamp("2024-03-28"),
                }
            }

        # For any other read operation, return empty DataFrame
        return pd.DataFrame()

    with patch("main.data.auditor.Auditor.read", mock_read):
        config = ConfigFactory("nse")
        compiler = Compiler(config=config)

        # Mock expiry_dict in data_store
        compiler.data_store.expiry_dict = {
            pd.Timestamp("2024-01-01"): {
                "near_month": pd.Timestamp("2024-01-25"),
                "next_month": pd.Timestamp("2024-02-29"),
                "far_month": pd.Timestamp("2024-03-28"),
            }
        }

        yield compiler


@pytest.mark.usefixtures("compiler_obj")
class TestCompiler:
    option_test_df = pd.read_parquet(
        "./test/utility/test_data/option_test_data.parquet"
    )
    underlying_test_df = pd.read_parquet(
        "./test/utility/test_data/underlying_test_data.parquet"
    )
    option_oi_test_df = pd.read_parquet(
        "./test/utility/test_data/option_oi_test_data.parquet"
    )
    cash_test_df = pd.read_parquet(
        "./test/utility/test_data/compiler_cash_test_data.parquet"
    )

    def test_check_universe_list(self, compiler_obj):
        expected_message = (
            "CompileError: ['optcom', 'optcur'] are invalid universes in universe_list!"
        )
        with pytest.raises(Exception) as exception_info:
            compiler_obj._check_universe_list(universe_list=["optcom", "optcur"])
        assert str(exception_info.value) == expected_message

    def test_extract_raw_dependencies(self, compiler_obj):
        compiler_obj._extract_raw_dependencies(universe="opt")
        assert compiler_obj._universe_list == [
            "raw_opt",
            "raw_opt_oi",
            "raw_futidx",
        ]

    def test_add_post_dependencies(self, compiler_obj):
        compiler_obj._universe_list = []
        compiler_obj._add_post_dependencies(universe="raw_opt")
        assert compiler_obj.dag["raw_opt"] == ["opt"]
        assert compiler_obj._universe_list == ["opt"]

    def test_add_universe_list(self, compiler_obj):
        compiler_obj._universe_list = []
        compiler_obj._add_universe_list(universe_list=["opt"])
        assert compiler_obj.dag["raw_opt"] == ["opt"]
        assert compiler_obj.dag["raw_opt_oi"] == ["opt", "opt_ord", "futidx"]
        assert compiler_obj.dag["raw_futidx"] == [
            "opt",
            "opt_ord",
            "futidx",
        ]
        assert compiler_obj._universe_list == [
            "raw_opt",
            "raw_opt_oi",
            "raw_futidx",
            "opt",
            "opt_ord",
            "futidx",
        ]

    def test_load_after_market_data(self, compiler_obj):
        # Test when universe is not in UNIVERSE_TO_AFTER_MARKET_SYMBOL_LIST
        compiler_obj._load_after_market_data(universe="unknown_universe")

        # Mock the config to include a test universe
        original_config = compiler_obj._config.UNIVERSE_TO_AFTER_MARKET_SYMBOL_LIST
        compiler_obj._config.UNIVERSE_TO_AFTER_MARKET_SYMBOL_LIST = {
            "test_universe": ["test_symbol"]
        }

        # Test when universe is in the list
        with patch.object(
            compiler_obj._Compiler__auditor,
            "read",
            return_value=pd.DataFrame({"data": [1, 2, 3]}),
        ):
            compiler_obj._load_after_market_data(universe="test_universe")
            assert hasattr(compiler_obj.data_store, "test_symbol")
            assert isinstance(compiler_obj.data_store.test_symbol, pd.DataFrame)

        # Restore original config
        compiler_obj._config.UNIVERSE_TO_AFTER_MARKET_SYMBOL_LIST = original_config

    def test_load_minio_files_list(self, compiler_obj):
        # Test when universe is not in UNIVERSE_TO_MINIO_FILES_LIST
        compiler_obj._load_minio_files_list(universe="unknown_universe")

        # Mock the config to include a test universe
        original_minio_files = compiler_obj._config.UNIVERSE_TO_MINIO_FILES_LIST
        original_file_dict = compiler_obj._config.FILE_DICT

        compiler_obj._config.UNIVERSE_TO_MINIO_FILES_LIST = {
            "test_universe": {"test_file": "TEST_FILE"}
        }
        compiler_obj._config.FILE_DICT = {"TEST_FILE": ["test_location", "test_name"]}

        # Test when universe is in the list
        with patch.object(
            compiler_obj._Compiler__auditor, "read", return_value={"test": "data"}
        ):
            compiler_obj._load_minio_files_list(universe="test_universe")
            assert hasattr(compiler_obj.data_store, "test_file")
            assert compiler_obj.data_store.test_file == {"test": "data"}

        # Restore original config
        compiler_obj._config.UNIVERSE_TO_MINIO_FILES_LIST = original_minio_files
        compiler_obj._config.FILE_DICT = original_file_dict

    def test_clear_universe_data(self, compiler_obj):
        compiler_obj.data_store.test_attr1 = "test_value1"
        compiler_obj.data_store.test_attr2 = "test_value2"

        assert hasattr(compiler_obj.data_store, "test_attr1")
        assert hasattr(compiler_obj.data_store, "test_attr2")

        compiler_obj._Compiler__clear_universe_data()

        # Again mock expiry_dict in data_store as it is cleared
        compiler_obj.data_store.expiry_dict = {
            pd.Timestamp("2024-01-01"): {
                "near_month": pd.Timestamp("2024-01-25"),
                "next_month": pd.Timestamp("2024-02-29"),
                "far_month": pd.Timestamp("2024-03-28"),
            }
        }

        assert not hasattr(compiler_obj.data_store, "test_attr1")
        assert not hasattr(compiler_obj.data_store, "test_attr2")

    def test_add_columns(self, compiler_obj):
        output_df = compiler_obj._add_columns(
            universe="opt",
            universe_df=self.option_test_df,
            frequency=5,
            dtype="trd",
            symbol="test_symbol",
            start_date=pd.Timestamp(2024, 1, 1),
            end_date=pd.Timestamp(2024, 1, 2),
        )
        assert set(output_df.columns) == set(
            compiler_obj._config.COLUMNS_DICT["opt"]
        ) - {"timestamp"}
        assert "Open_int" in output_df
        assert "iv" in output_df

    def test_add_columns_timestamp_not_found_raise_exception(self, compiler_obj):
        data = self.option_test_df.copy()
        data = data.reset_index().drop(columns="timestamp")
        expected_message = "CompileError: timestamp should be there by default in opt!!"
        with pytest.raises(Exception) as exception_info:
            compiler_obj._add_columns(
                universe="opt",
                universe_df=data,
                frequency=5,
                dtype="trd",
                symbol="test_symbol",
                start_date=pd.Timestamp(2024, 1, 1),
                end_date=pd.Timestamp(2024, 1, 2),
            )
        assert str(exception_info.value) == expected_message

    def test_add_columns_futstk_oi_near_month(self, compiler_obj):
        data = self.option_oi_test_df
        output_df = compiler_obj._add_columns(
            universe="futidx_oi",
            universe_df=data,
            frequency=5,
            dtype="trd",
            symbol="test_symbol",
            start_date=pd.Timestamp(2024, 1, 1),
            end_date=pd.Timestamp(2024, 1, 2),
        )
        assert "near_month" not in output_df

    def test_add_columns_cm_bhav(self, compiler_obj):
        # Create mock data for raw_cm_bhav
        raw_cm_bhav_data = pd.DataFrame(
            {
                "date": [
                    pd.Timestamp("2024-01-01"),
                ],
                "symbol": ["SBIN"],
                "series": ["EQ"],
                "open": [550.0],
                "high": [560.0],
                "low": [545.0],
                "close": [555.0],
                "last": [555.0],
                "prevclose": [548.0],
                "tottrdqty": [1000000],
                "tottrdval": [550000000],
                "totaltrades": [
                    5000,
                ],
                "isin": ["INE062A01020"],
                "ID": [1075],
            }
        )

        raw_cm_bhav_data.set_index("date", inplace=True)
        output_df = compiler_obj._add_columns(
            universe="cm_bhav",
            universe_df=raw_cm_bhav_data,
            frequency=1440,
            dtype="trd",
            symbol="SBIN",
            start_date=pd.Timestamp(2024, 1, 1),
            end_date=pd.Timestamp(2024, 1, 2),
        )

        assert set(output_df.columns) == set(
            compiler_obj._config.COLUMNS_DICT["cm_bhav"]
        ) - {"date"}

    def test_add_columns_futidx_bhav_expiry_rank_repo_rate(self, compiler_obj):
        # Create mock data for raw_futidx_bhav
        raw_futidx_bhav_data = pd.DataFrame(
            {
                "date": [
                    pd.Timestamp("2024-01-01"),
                ],
                "instrument": ["FUTIDX"],
                "symbol": ["NIFTY"],
                "expiry_dt": [
                    pd.Timestamp("2024-01-25"),
                ],
                "strike_pr": [0],
                "option_typ": ["XX"],
                "open": [21500.0],
                "high": [21550.0],
                "low": [21450.0],
                "close": [21520.0],
                "settle_pr": [21520.0],
                "contracts": [100000],
                "val_inlakh": [21520.0],
                "open_int": [2000000],
                "chg_in_oi": [50000],
                "ID": [5001],
            }
        )

        raw_futidx_bhav_data.set_index("date", inplace=True)

        # Mock repo_rate data
        repo_rate_data = pd.DataFrame(
            {"repo_rate": [0.065]},
            index=pd.DatetimeIndex([pd.Timestamp("2024-01-01")], name="date"),
        )
        compiler_obj.data_store.repo_rate = repo_rate_data

        result_df = compiler_obj._add_columns(
            universe="futidx_bhav",
            universe_df=raw_futidx_bhav_data,
            frequency=1440,
            dtype="trd",
            symbol="5001",
            start_date=pd.Timestamp(2024, 1, 1),
            end_date=pd.Timestamp(2024, 1, 2),
        )

        assert set(result_df.columns) == set(
            compiler_obj._config.COLUMNS_DICT["futidx_bhav"]
        ) - {"date"}

        assert result_df.iloc[0]["balte_id"] == 5001
        assert result_df.iloc[0]["expiry_rank"] == 1
        assert result_df.iloc[0]["repo_rate"] == 0.065

    def test_add_columns_opt_ord(self, compiler_obj):
        opt_ord_data = self.option_test_df.copy()
        opt_ord_data = opt_ord_data.drop(columns=["Cons_Volume", "Vwap"])

        original_columns = compiler_obj._config.UNIVERSE_DTYPE_TO_COLUMN_DICT[
            "opt_ord"
        ].copy()

        # Modify columns dict to add additional columns
        compiler_obj._config.UNIVERSE_DTYPE_TO_COLUMN_DICT["opt_ord"].extend(
            ["sym_id", "expiry_date", "strike", "option_type"]
        )

        result_df = compiler_obj._add_columns(
            universe="opt",
            universe_df=opt_ord_data,
            frequency=5,
            dtype="ord",
            symbol="test_symbol",
            start_date=pd.Timestamp(2024, 1, 1),
            end_date=pd.Timestamp(2024, 1, 2),
        )

        assert set(result_df.columns) == set(
            compiler_obj._config.UNIVERSE_DTYPE_TO_COLUMN_DICT["opt_ord"]
        ) - {"timestamp"}
        assert result_df["sym_id"].iloc[0] == 5001
        assert result_df["expiry_date"].iloc[0] == pd.Timestamp(2024, 1, 25)
        assert result_df["strike"].iloc[0] == 18000
        assert result_df["option_type"].iloc[0] == 0
        assert result_df["Cons_Volume"].iloc[0] == 27050

        # Revert columns dict to original state
        compiler_obj._config.UNIVERSE_DTYPE_TO_COLUMN_DICT["opt_ord"] = original_columns

    def test_add_columns_futidx_fut(self, compiler_obj):
        futidx_fut_data = self.option_test_df.copy()
        futidx_fut_data["ID"] = futidx_fut_data["ID"] % int(1e13)
        result_df = compiler_obj._add_columns(
            universe="futidx_fut",
            universe_df=futidx_fut_data,
            frequency=1,
            dtype="trd",
            symbol="test_symbol",
            start_date=pd.Timestamp(2024, 1, 1),
            end_date=pd.Timestamp(2024, 1, 2),
        )

        assert set(result_df.columns) == set(
            compiler_obj._config.COLUMNS_DICT["futidx_fut"]
        ) - {"timestamp"}

    def test_modify_columns_opt(self, compiler_obj):
        result_df = compiler_obj._modify_columns(
            symbol="test_symbol",
            universe="opt",
            universe_df=self.option_test_df,
            frequency=1,
            dtype="trd",
        )
        pd.testing.assert_frame_equal(result_df, self.option_test_df)

    def test_modify_columns_cash(self, compiler_obj):
        cash_test_df = self.cash_test_df.copy()
        # Create corpact data
        corpact_data = pd.DataFrame(
            {"exdate": [pd.Timestamp("2024-01-02")], "ID": [1075], "adj_factor": [0.5]}
        )
        corpact_data.set_index("exdate", inplace=True)

        compiler_obj.data_store.corpact = corpact_data
        result_df = compiler_obj._modify_columns(
            symbol="1075",
            universe="cash",
            universe_df=cash_test_df,
            frequency=1,
            dtype="trd",
        )
        assert set(result_df.columns) == set(
            compiler_obj._config.COLUMNS_DICT["cash"]
        ) - {"timestamp"}
        assert result_df["Close"].iloc[0] == 321.1

    def test_modify_columns_fut(self, compiler_obj):
        fut = self.option_test_df.copy()
        fut["ID"] = fut["ID"] % int(1e13)
        fut["ID"] //= 10000
        fut["ID"] = fut["ID"] * int(1e4) + 1075
        fut["Next_Cons_Volume"] = fut["Cons_Volume"]

        original_modify_columns_operations = (
            compiler_obj._config.MODIFY_COLUMNS_OPERATIONS["fut"].copy()
        )

        # Modify columns operations to add additional operational
        compiler_obj._config.MODIFY_COLUMNS_OPERATIONS["fut"].extend(
            ["apply_dividend_removal"]
        )

        # Mock repo_rate data
        repo_rate_data = pd.DataFrame(
            {"repo_rate": [0.065]},
            index=pd.DatetimeIndex([pd.Timestamp("2024-01-01")], name="date"),
        )
        compiler_obj.data_store.repo_rate = repo_rate_data

        # Mock corpact data
        corpact_data = pd.DataFrame(columns=["ID", "adj_factor"])
        corpact_data.index.name = "exdate"
        compiler_obj.data_store.corpact = corpact_data

        # Mock corpact_dividend_info data
        corpact_dividend_info_data = pd.DataFrame(
            {
                "ID": [1075],
                "adj_factor": [0.99],
                "exdate": [pd.Timestamp("2024-01-05")],
            },
            index=pd.DatetimeIndex([pd.Timestamp("2024-01-01")], name="date"),
        )
        compiler_obj.data_store.corpact_dividend_info = corpact_dividend_info_data

        result_df = compiler_obj._modify_columns(
            symbol="1075",
            universe="fut",
            universe_df=fut,
            frequency=1,
            dtype="trd",
        )
        assert set(result_df.columns) == set(
            compiler_obj._config.COLUMNS_DICT["fut"]
        ) - {"timestamp"}

        assert result_df["Close"].iloc[0] == self.option_test_df["Close"].iloc[0]
        # Check for dividend removal and basis adjustment
        assert (
            abs(
                result_df["Close"].iloc[-1]
                - (self.option_test_df["Close"].iloc[-1] * (1 - 0.004274)) / 0.99
            )
            < 1e-6
        )

        # Revert modify columns operations to original state
        compiler_obj._config.MODIFY_COLUMNS_OPERATIONS[
            "fut"
        ] = original_modify_columns_operations

    def test_modify_columns_futstk_oi(self, compiler_obj):
        futstk_oi_data = pd.DataFrame(
            {
                "timestamp": [pd.Timestamp(2024, 1, 1, 13)] * 3,
                "ID": [2024012521075, 2024022921075, 2024032921075],
                "OI": [10550, 10650, 10750],
            }
        )
        futstk_oi_data.set_index("timestamp", inplace=True)

        # Create mock lotsize data
        lotsize_data = pd.DataFrame(
            {"ID": [1075], "near_month": [50], "next_month": [50]},
            index=pd.DatetimeIndex([pd.Timestamp("2024-01-01")], name="date"),
        )
        compiler_obj.data_store.lotsize_stk = lotsize_data

        # Create mock corpact data
        corpact_data = pd.DataFrame(
            {"ID": [1075], "adj_factor": [0.67]},
            index=pd.DatetimeIndex([pd.Timestamp("2024-01-02")], name="exdate"),
        )
        compiler_obj.data_store.corpact = corpact_data

        result_df = compiler_obj._modify_columns(
            symbol="1075",
            universe="futstk_oi",
            universe_df=futstk_oi_data,
            frequency=1,
            dtype="trd",
        )
        assert set(result_df.columns) == set(
            compiler_obj._config.COLUMNS_DICT["futstk_oi"]
        ) - {"timestamp"}
        assert result_df["near_month"].iloc[0] == np.round(10550 / 0.67 / 50) * 50

    def test_filter_data_filter_id_fno(self, compiler_obj):
        fut_data = self.option_test_df.copy()
        fut_data["ID"] = fut_data["ID"] % int(1e13)
        fut_data["ID"] //= 10000
        fut_data["ID"] = fut_data["ID"] * int(1e4) + 1075
        fut_data["Next_Cons_Volume"] = fut_data["Cons_Volume"]
        new_row = fut_data.iloc[[0]]
        new_row.index = [pd.Timestamp("2024-01-02 13:00:00")]
        new_row.index.name = fut_data.index.name
        fut_data = pd.concat([fut_data, new_row])

        # Mock isfno data in data_store
        isfno_data = pd.DataFrame(
            {
                "ID": [1075],  # SBIN
                "date": [pd.Timestamp("2024-01-01")],
                "0": [True],
            }
        )
        isfno_data.set_index("date", inplace=True)
        compiler_obj.data_store.isfno = isfno_data

        result = compiler_obj._filter_data(
            symbol="1075",
            universe="fut",
            universe_df=fut_data,
            frequency=1,
            dtype="trd",
            start_date=pd.Timestamp("2024-01-01"),
            end_date=pd.Timestamp("2024-01-03"),
        )

        assert set(result.columns) == set(compiler_obj._config.COLUMNS_DICT["fut"]) - {
            "timestamp"
        }
        assert set(result.index.date) == {pd.Timestamp("2024-01-01").date()}

    def test_filter_data_filter_series(self, compiler_obj):
        test_data = pd.DataFrame(
            {
                "timestamp": [
                    pd.Timestamp("2024-01-01 09:30:00"),
                    pd.Timestamp("2024-01-01 10:00:00"),
                    pd.Timestamp("2024-01-01 10:30:00"),
                ],
                "Close": [100, 101, 102],
                "series": ["EQ", "BE", "EQ"],
            }
        )
        test_data.set_index("timestamp", inplace=True)

        # Mock the config to include series filter for cm_bhav
        original_series_filter = compiler_obj._config.SERIES_FILTER_MAP.get(
            "cm_bhav", []
        )
        compiler_obj._config.SERIES_FILTER_MAP["cm_bhav"] = ["EQ"]

        original_filter_operation_list = (
            compiler_obj._config.FILTER_DATA_OPERATIONS.get("cm_bhav", [])
        )
        compiler_obj._config.FILTER_DATA_OPERATIONS["cm_bhav"] = ["filter_series"]

        result = compiler_obj._filter_data(
            symbol="SBIN",
            universe="cm_bhav",
            universe_df=test_data,
            frequency=1440,
            dtype="trd",
            start_date=pd.Timestamp("2024-01-01"),
            end_date=pd.Timestamp("2024-01-02"),
        )

        assert len(result) == 2
        assert all(result["series"] == "EQ")

        # Restore original config
        if original_series_filter:
            compiler_obj._config.SERIES_FILTER_MAP["cm_bhav"] = original_series_filter
        else:
            del compiler_obj._config.SERIES_FILTER_MAP["cm_bhav"]

        if original_filter_operation_list:
            compiler_obj._config.FILTER_DATA_OPERATIONS[
                "cm_bhav"
            ] = original_filter_operation_list
        else:
            del compiler_obj._config.FILTER_DATA_OPERATIONS["cm_bhav"]

    def test_compile_success(self, compiler_obj, monkeypatch):
        output_df_list = {}

        def mock_write(args, **kwargs):
            output_df_list[kwargs["file_name"]] = kwargs["data"]

        def mock_list_file_names(args, **kwargs):
            return ["SBIN", "nse_1min_raw_cash_trd_1075_20240101_20240102"]

        def mock_read_metadata(args, **kwargs):
            return {
                "start_timestamp": pd.Timestamp("2024-01-01"),
                "last_timestamp": pd.Timestamp("2024-01-02"),
            }

        with patch("main.data.auditor.Auditor.write", mock_write), patch(
            "main.data.auditor.Auditor.list_file_names", mock_list_file_names
        ), patch("main.data.auditor.Auditor.read_metadata", mock_read_metadata):
            compilation_response = compiler_obj.compile(
                universe_list=["cash"],
                frequency=1,
                dtype="trd",
            )
            assert compilation_response == "Compilation Success!!"
            assert len(output_df_list) == 2

            assert set(
                output_df_list["nse_1min_cash_trd_1075_20240101_20240102"].columns
            ) == set(compiler_obj._config.COLUMNS_DICT["cash"]) - {"timestamp"}
            assert len(
                output_df_list["nse_1min_cash_trd_1075_20240101_20240102"]
            ) == len(output_df_list["nse_1min_raw_cash_trd_1075_20240101_20240102"])

        # Again mock expiry dict as it is cleared
        compiler_obj.data_store.expiry_dict = {
            pd.Timestamp("2024-01-01"): {
                "near_month": pd.Timestamp("2024-01-25"),
                "next_month": pd.Timestamp("2024-02-29"),
                "far_month": pd.Timestamp("2024-03-28"),
            }
        }

    def test_compile_futidx_fut_universe(self, compiler_obj, monkeypatch):
        # Dictionary to store output DataFrames
        output_df_list = {}

        def mock_write(args, **kwargs):
            output_df_list[kwargs["file_name"]] = kwargs["data"]

        def mock_list_file_names(args, **kwargs):
            return ["NIFTY", "nse_1min_raw_futidx_fut_trd_5001_20240101_20240102"]

        original_filter_data = compiler_obj._filter_data

        def mock_filter_data(args, **kwargs):
            if kwargs["universe"][:3] == "raw":
                raw_futidx_fut = pd.read_parquet(
                    "./test/utility/test_data/option_test_data.parquet"
                )
                raw_futidx_fut["ID"] = raw_futidx_fut["ID"] % int(1e13)
                raw_futidx_fut = raw_futidx_fut[raw_futidx_fut["ID"] % 10000 == 5001]
                return raw_futidx_fut
            else:
                return original_filter_data(
                    kwargs["symbol"],
                    kwargs["universe"],
                    kwargs["universe_df"],
                    kwargs["frequency"],
                    kwargs["dtype"],
                    kwargs["start_date"],
                    kwargs["end_date"],
                )

        with patch(
            "main.data.auditor.Auditor.list_file_names", mock_list_file_names
        ), patch("main.data.compiler.Compiler._filter_data", mock_filter_data), patch(
            "main.data.auditor.Auditor.write", mock_write
        ):
            compilation_response = compiler_obj.compile(
                universe_list=["futidx_fut"],
                frequency=1,
                dtype="trd",
                start_date=pd.Timestamp(2024, 1, 1),
                end_date=pd.Timestamp(2024, 1, 2),
            )
            assert compilation_response == "Compilation Success!!"
            assert set(
                output_df_list["nse_1min_futidx_fut_trd_5001_20240101_20240102"].columns
            ) == set(compiler_obj._config.COLUMNS_DICT["futidx_fut"]) - {"timestamp"}
            assert "nse_1min_futidx_fut_trd_5001_20240101_20240102" in output_df_list
            assert set(
                output_df_list["nse_1min_futidx_fut_trd_5001_20240101_20240102"]["ID"]
                // int(1e5)
            ) == {20240125}

        # Again mock expiry dict as it is cleared
        compiler_obj.data_store.expiry_dict = {
            pd.Timestamp("2024-01-01"): {
                "near_month": pd.Timestamp("2024-01-25"),
                "next_month": pd.Timestamp("2024-02-29"),
                "far_month": pd.Timestamp("2024-03-28"),
            }
        }
