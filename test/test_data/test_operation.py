import datetime
import pytest
import pandas as pd
import numpy as np
from main.data.operation import Operator
from main.config.config_factory import ConfigFactory
from main.data.utility import compare_floats
from main.enums import Operation


@pytest.fixture(scope="class")
def operator_obj():
    config = ConfigFactory("nse")
    operator = Operator(config=config)
    return operator


@pytest.mark.usefixtures("operator_obj")
class TestOperation:
    option_test_df = pd.read_parquet(
        "./test/utility/test_data/option_test_data.parquet"
    )
    underlying_test_df = pd.read_parquet(
        "./test/utility/test_data/underlying_test_data.parquet"
    )
    option_oi_test_df = pd.read_parquet(
        "./test/utility/test_data/option_oi_test_data.parquet"
    )
    column_bucket_test_df = pd.read_parquet(
        "./test/utility/test_data/column_bucket_test_data.parquet"
    )

    def test_apply_operation_invalid_arguments_for_modify_operation(
        self, operator_obj: Operator
    ):
        timestamps = pd.date_range(start="2023-01-01", periods=3, freq="D")
        data = pd.DataFrame(
            {
                "ID": [5001, 5001, 5001],
                "Close": [100, 200, 300],
            },
            index=pd.Index(timestamps, name="timestamp"),
        )
        expected_error_message = "Invalid arguments passed for MODIFY operation!!"
        with pytest.raises(Exception) as exception_info:
            operator_obj.apply_operation(
                operation_name=Operation.MODIFY,
                symbol="5001",
                universe_name="test_universe",
                universe_df=data,  # Only provide universe_df, but none of the required arguments
            )
        assert str(exception_info.value) == expected_error_message

    def test_apply_operation_invalid_operation(self, operator_obj: Operator):
        timestamps = pd.date_range(start="2023-01-01", periods=3, freq="D")
        data = pd.DataFrame(
            {
                "ID": [5001, 5001, 5001],
                "Close": [100, 200, 300],
            },
            index=pd.Index(timestamps, name="timestamp"),
        )

        class CustomOperation:
            INVALID = "invalid"

        expected_error_message = (
            "Invalid operation, should be one of the values in Operation enum!!"
        )
        with pytest.raises(Exception) as exception_info:
            operator_obj.apply_operation(
                operation_name=CustomOperation.INVALID,
                symbol="5001",
                universe_name="test_universe",
                universe_df=data,
            )
        assert str(exception_info.value) == expected_error_message

    def test_round_to_nearest_lotsize_success(self, operator_obj: Operator):
        timestamps = pd.date_range(start="2023-01-01", periods=3, freq="D")
        df = pd.DataFrame(
            {
                "near_month": [100.5, 200.7, 300.2],
                "next_month": [150.3, 250.8, 350.1],
                "far_month": [200.6, 300.9, 400.4],
                "other_col": [10, 20, 30],
            },
            index=pd.Index(timestamps, name="timestamp"),
        )

        lotsize = pd.DataFrame(
            {
                "near_month": [50, 50, 50],
                "next_month": [75, 75, 75],
                "far_month": [100, 100, 100],
            },
            index=pd.Index(timestamps, name="date"),
        )

        result = operator_obj.apply_operation(
            operation_name=Operation.MODIFY,
            symbol="symbol_name",
            universe_name="opt",
            universe_df=df,
            lotsize=lotsize,
        )

        assert len(result) == len(df)
        assert list(result.columns) == list(df.columns)
        assert result.iloc[0]["near_month"] == 100
        assert result.iloc[1]["near_month"] == 200
        assert result.iloc[2]["near_month"] == 300

        assert result.iloc[0]["next_month"] == 150
        assert result.iloc[1]["next_month"] == 225
        assert result.iloc[2]["next_month"] == 375

        assert result.iloc[0]["far_month"] == 200
        assert result.iloc[1]["far_month"] == 300
        assert result.iloc[2]["far_month"] == 400

        # Check that other columns are preserved
        assert result.iloc[0]["other_col"] == 10
        assert result.iloc[1]["other_col"] == 20
        assert result.iloc[2]["other_col"] == 30

    def test_round_to_nearest_lotsize_with_zero_values(self, operator_obj: Operator):
        timestamps = pd.date_range(start="2023-01-01", periods=3, freq="D")
        df = pd.DataFrame(
            {
                "near_month": [0, 200.7, 300.2],
                "next_month": [150.3, 0, 350.1],
                "far_month": [200.6, 300.9, 0],
            },
            index=pd.Index(timestamps, name="timestamp"),
        )

        lotsize = pd.DataFrame(
            {
                "near_month": [50, 50, 50],
                "next_month": [75, 75, 75],
                "far_month": [100, 100, 100],
            },
            index=pd.Index(timestamps, name="date"),
        )

        result = operator_obj.apply_operation(
            operation_name=Operation.MODIFY,
            symbol="symbol_name",
            universe_name="opt",
            universe_df=df,
            lotsize=lotsize,
        )

        # Check that zero values remain zero
        assert result.iloc[0]["near_month"] == 0
        assert result.iloc[1]["next_month"] == 0
        assert result.iloc[2]["far_month"] == 0

        # Check that non-zero values are rounded
        assert result.iloc[1]["near_month"] == 200
        assert result.iloc[2]["near_month"] == 300
        assert result.iloc[0]["next_month"] == 150
        assert result.iloc[2]["next_month"] == 375
        assert result.iloc[0]["far_month"] == 200
        assert result.iloc[1]["far_month"] == 300

    def test_round_to_nearest_lotsize_minimum_lotsize(self, operator_obj: Operator):
        timestamps = pd.date_range(start="2023-01-01", periods=3, freq="D")
        df = pd.DataFrame(
            {
                "near_month": [10.5, 20.7, 30.2],
                "next_month": [15.3, 25.8, 35.1],
                "far_month": [20.6, 30.9, 40.4],
            },
            index=pd.Index(timestamps, name="timestamp"),
        )

        lotsize = pd.DataFrame(
            {
                "near_month": [50, 50, 50],
                "next_month": [75, 75, 75],
                "far_month": [100, 100, 100],
            },
            index=pd.Index(timestamps, name="date"),
        )

        result = operator_obj.apply_operation(
            operation_name=Operation.MODIFY,
            symbol="symbol_name",
            universe_name="opt",
            universe_df=df,
            lotsize=lotsize,
        )
        assert result.iloc[0]["near_month"] == 50
        assert result.iloc[1]["near_month"] == 50
        assert result.iloc[2]["near_month"] == 50

        assert result.iloc[0]["next_month"] == 75
        assert result.iloc[1]["next_month"] == 75
        assert result.iloc[2]["next_month"] == 75

        assert result.iloc[0]["far_month"] == 100
        assert result.iloc[1]["far_month"] == 100
        assert result.iloc[2]["far_month"] == 100

    def test_round_to_nearest_lotsize_with_missing_dates(self, operator_obj: Operator):
        timestamps = pd.date_range(start="2023-01-01", periods=3, freq="D")
        df = pd.DataFrame(
            {
                "near_month": [100.5, 200.7, 300.2],
                "next_month": [150.3, 250.8, 350.1],
                "far_month": [200.6, 300.9, 400.4],
            },
            index=pd.Index(timestamps, name="timestamp"),
        )

        dates = pd.DatetimeIndex(
            [
                pd.Timestamp("2023-01-01"),
                pd.Timestamp("2023-01-03"),  # Missing 2023-01-02
            ]
        )
        lotsize = pd.DataFrame(
            {
                "near_month": [50, 50],
                "next_month": [75, 75],
                "far_month": [100, 100],
            },
            index=pd.Index(dates, name="date"),
        )

        result = operator_obj.apply_operation(
            operation_name=Operation.MODIFY,
            symbol="symbol_name",
            universe_name="opt",
            universe_df=df,
            lotsize=lotsize,
        )

        assert result.iloc[0]["near_month"] == 100
        assert result.iloc[2]["near_month"] == 300

        assert result.iloc[1]["near_month"] == 201  # 200.7 rounded to 201

    def test_round_to_nearest_lotsize_with_nan_values(self, operator_obj: Operator):
        timestamps = pd.date_range(start="2023-01-01", periods=3, freq="D")
        df = pd.DataFrame(
            {
                "near_month": [100.5, np.nan, 300.2],
                "next_month": [np.nan, 250.8, 350.1],
                "far_month": [200.6, 300.9, np.nan],
            },
            index=pd.Index(timestamps, name="timestamp"),
        )
        lotsize = pd.DataFrame(
            {
                "near_month": [50, 50, 50],
                "next_month": [75, 75, 75],
                "far_month": [100, 100, 100],
            },
            index=pd.Index(timestamps, name="date"),
        )

        result = operator_obj.apply_operation(
            operation_name=Operation.MODIFY,
            symbol="symbol_name",
            universe_name="opt",
            universe_df=df,
            lotsize=lotsize,
        )

        # Check that NaN values remain NaN
        assert pd.isna(result.iloc[1]["near_month"])
        assert pd.isna(result.iloc[0]["next_month"])
        assert pd.isna(result.iloc[2]["far_month"])

        # Check that non-NaN values are rounded
        assert result.iloc[0]["near_month"] == 100
        assert result.iloc[2]["near_month"] == 300
        assert result.iloc[1]["next_month"] == 225
        assert result.iloc[2]["next_month"] == 375
        assert result.iloc[0]["far_month"] == 200
        assert result.iloc[1]["far_month"] == 300

    def test_pivot_by_expiry_basic_functionality(self, operator_obj: Operator):
        timestamps = pd.date_range(start="2023-01-01", periods=5, freq="D")
        universe_df = pd.DataFrame(
            {
                "ID": [5001, 5001, 5001, 5001, 5001],
                "expiry": pd.to_datetime(
                    [
                        "2023-01-15",
                        "2023-01-15",
                        "2023-02-15",
                        "2023-03-15",
                        "2023-01-15",
                    ]
                ),
                "OI": [100, 200, 300, 400, 500],
            },
            index=pd.Index(timestamps, name="timestamp"),
        )

        expiry_dict = {
            pd.Timestamp("2023-01-01"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
                "far_month": pd.Timestamp("2023-03-15"),
            },
            pd.Timestamp("2023-01-02"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
                "far_month": pd.Timestamp("2023-03-15"),
            },
            pd.Timestamp("2023-01-03"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
                "far_month": pd.Timestamp("2023-03-15"),
            },
            pd.Timestamp("2023-01-04"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
                "far_month": pd.Timestamp("2023-03-15"),
            },
            pd.Timestamp("2023-01-05"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
                "far_month": pd.Timestamp("2023-03-15"),
            },
        }

        symbol_map = {"5001": 5001}

        result = operator_obj.apply_operation(
            operation_name=Operation.MODIFY,
            symbol="5001",
            universe_name="futidx",
            universe_df=universe_df,
            expiry_dict=expiry_dict,
            symbol_map=symbol_map,
            value_column="OI",
            expiry_categories=["near_month", "next_month"],
            ffill=True,
        )

        assert list(result.columns) == ["ID", "near_month", "next_month"]
        assert len(result) == 4
        assert result["ID"].iloc[0] == 5001

        assert result["near_month"].iloc[0] == 100
        assert result["next_month"].iloc[2] == 300

        assert not result["near_month"].isna().any()
        assert np.isnan(result["next_month"].iloc[0])

    def test_pivot_by_expiry_with_expiry_filtering(self, operator_obj: Operator):
        timestamps = pd.date_range(start="2023-01-10", periods=3, freq="D")
        universe_df = pd.DataFrame(
            {
                "ID": [5001, 5001, 5001],
                "expiry": pd.to_datetime(
                    ["2023-01-05", "2023-01-15", "2023-02-15"]
                ),  # First expiry is in the past
                "OI": [100, 200, 300],
            },
            index=pd.Index(timestamps, name="timestamp"),
        )

        expiry_dict = {
            pd.Timestamp("2023-01-10"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
                "far_month": pd.Timestamp("2023-03-15"),
            },
            pd.Timestamp("2023-01-11"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
                "far_month": pd.Timestamp("2023-03-15"),
            },
            pd.Timestamp("2023-01-12"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
                "far_month": pd.Timestamp("2023-03-15"),
            },
        }

        symbol_map = {"5001": 5001}

        result = operator_obj.apply_operation(
            operation_name=Operation.MODIFY,
            symbol="5001",
            universe_name="futidx",
            universe_df=universe_df,
            expiry_dict=expiry_dict,
            symbol_map=symbol_map,
            value_column="OI",
            expiry_categories=["near_month", "next_month"],
            ffill=True,
        )

        assert len(result) == 2
        assert result["near_month"].iloc[0] == 200
        assert result["next_month"].iloc[1] == 300

    def test_pivot_by_expiry_futidx_fut_oi_universe(self, operator_obj: Operator):
        timestamps = pd.date_range(start="2023-01-01", periods=3, freq="D")
        universe_df = pd.DataFrame(
            {
                "ID": [1001, 1001, 1001],
                "expiry": pd.to_datetime(["2023-01-15", "2023-02-15", "2023-03-15"]),
                "OI": [100, 200, 300],
            },
            index=timestamps,
        )

        expiry_dict = {
            pd.Timestamp("2023-01-01"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
                "far_month": pd.Timestamp("2023-03-15"),
            },
            pd.Timestamp("2023-01-02"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
                "far_month": pd.Timestamp("2023-03-15"),
            },
            pd.Timestamp("2023-01-03"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
                "far_month": pd.Timestamp("2023-03-15"),
            },
        }

        symbol_map = {"symbol_name": 9999}

        result = operator_obj.apply_operation(
            operation_name=Operation.MODIFY,
            symbol="symbol_name",
            universe_name="futidx_fut_oi",
            universe_df=universe_df,
            expiry_dict=expiry_dict,
            symbol_map=symbol_map,
            value_column="OI",
            expiry_categories=["near_month", "next_month"],
            ffill=True,
        )

        assert result["ID"].iloc[0] == 9999

        assert result["near_month"].iloc[0] == 100
        assert result["next_month"].iloc[1] == 200

    def test_pivot_by_expiry_with_missing_expiry_ranks(self, operator_obj: Operator):
        timestamps = pd.date_range(start="2023-01-01", periods=4, freq="D")
        universe_df = pd.DataFrame(
            {
                "ID": [5001, 5001, 5001, 5001],
                "expiry": pd.to_datetime(
                    ["2023-01-15", "2023-04-15", "2023-02-15", "2023-03-15"]
                ),  # Second expiry doesn't match any month
                "OI": [100, 200, 300, 400],
            },
            index=timestamps,
        )

        expiry_dict = {
            pd.Timestamp("2023-01-01"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
                "far_month": pd.Timestamp("2023-03-15"),
            },
            pd.Timestamp("2023-01-02"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
                "far_month": pd.Timestamp("2023-03-15"),
            },
            pd.Timestamp("2023-01-03"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
                "far_month": pd.Timestamp("2023-03-15"),
            },
            pd.Timestamp("2023-01-04"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
                "far_month": pd.Timestamp("2023-03-15"),
            },
        }

        symbol_map = {"5001": 5001}

        result = operator_obj.apply_operation(
            operation_name=Operation.MODIFY,
            symbol="5001",
            universe_name="futidx_fut_oi",
            universe_df=universe_df,
            expiry_dict=expiry_dict,
            symbol_map=symbol_map,
            value_column="OI",
            expiry_categories=["near_month", "next_month"],
            ffill=True,
        )
        # only near and next month should be present
        assert len(result) == 2

        assert result["near_month"].iloc[0] == 100
        assert result["next_month"].iloc[1] == 300

    def test_filter_series(self, operator_obj: Operator):
        timestamps = pd.date_range(start="2023-01-01", periods=5, freq="D")
        df = pd.DataFrame(
            {
                "ID": [1001, 1002, 1003, 1004, 1005],
                "series": ["CE", "PE", "CE", "FUT", "PE"],
                "value": [100, 200, 300, 400, 500],
            },
            index=timestamps,
        )

        series_filter_list = ["CE", "PE"]
        result = operator_obj.apply_operation(
            operation_name=Operation.FILTER,
            symbol="symbol_name",
            universe_name="opt",
            universe_df=df,
            series_filter_list=series_filter_list,
        )

        assert len(result) == 4  # 4 rows should remain (2 CE and 2 PE)
        assert "FUT" not in result["series"].values
        assert set(result["series"].unique()) == set(["CE", "PE"])
        assert result.iloc[0]["value"] == 100
        assert result.iloc[1]["value"] == 200
        assert result.iloc[2]["value"] == 300
        assert result.iloc[3]["value"] == 500

    def test_add_oi_success(self, operator_obj: Operator):
        output_with_oi = operator_obj.apply_operation(
            operation_name=Operation.OI,
            symbol="symbol_name",
            universe_name="opt",
            option_df=self.option_test_df,
            option_oi_df=self.option_oi_test_df,
        )
        assert len(output_with_oi) == len(self.option_test_df)
        assert output_with_oi.iloc[0].Open_int == 11700
        assert output_with_oi.Open_int.isna().sum() == 0

    def test_add_oi_duplicate_in_oi_data_fails(self, operator_obj: Operator):
        option_oi_test = self.option_oi_test_df
        option_oi_test = pd.concat(
            [option_oi_test.head(1), option_oi_test, option_oi_test.tail(1)]
        )
        output_with_oi = operator_obj.apply_operation(
            operation_name=Operation.OI,
            symbol="symbol_name",
            universe_name="opt",
            option_df=self.option_test_df,
            option_oi_df=option_oi_test,
        )
        assert len(output_with_oi) > len(self.option_test_df)

    def test_add_oi_less_data_in_oi_success_with_nan(self, operator_obj: Operator):
        option_oi_test = self.option_oi_test_df
        option_oi_test = option_oi_test[:-1]
        output_with_oi = operator_obj.apply_operation(
            operation_name=Operation.OI,
            symbol="symbol_name",
            universe_name="opt",
            option_df=self.option_test_df,
            option_oi_df=option_oi_test,
        )
        assert len(output_with_oi) == len(self.option_test_df)

    def test_add_oi_exception_handling(self, operator_obj: Operator):
        timestamps = pd.date_range(start="2023-01-01", periods=3, freq="D")
        option_df = pd.DataFrame(
            {
                "Close": [100, 200, 300],  # ID column missing
            },
            index=pd.Index(timestamps, name="timestamp"),
        )

        option_oi_df = pd.DataFrame(
            {
                "ID": [1001, 1001, 1002],
                "OI": [100, 200, 300],
            },
            index=pd.Index(timestamps, name="timestamp"),
        )

        expected_error_prefix = "Add OI operation failed due to KeyError('ID')"

        with pytest.raises(Exception) as exception_info:
            operator_obj.apply_operation(
                operation_name=Operation.OI,
                symbol="symbol_name",
                universe_name="opt",
                option_df=option_df,
                option_oi_df=option_oi_df,
            )

        assert str(exception_info.value) == expected_error_prefix

    def test_add_pcr_success(self, operator_obj: Operator):
        output_with_pcr = operator_obj.apply_operation(
            operation_name=Operation.PCR,
            symbol="5001",
            universe_name="futidx",
            underlying_df=self.underlying_test_df,
            option_oi_df=self.option_oi_test_df,
        )
        assert len(output_with_pcr) == len(self.underlying_test_df)
        assert (output_with_pcr.iloc[0].pcr - 1.07) < 0.01
        assert output_with_pcr.pcr.isna().sum() == 4

    def test_add_pcr_new_ID_in_oi_data_success(self, operator_obj: Operator):
        option_oi_test = self.option_oi_test_df
        option_oi_test = pd.concat([option_oi_test, option_oi_test.tail(1)])
        option_oi_test["ID"][-1] = 545002024012515011
        output_with_pcr = operator_obj.apply_operation(
            operation_name=Operation.PCR,
            symbol="symbol_name",
            universe_name="futidx",
            underlying_df=self.underlying_test_df,
            option_oi_df=option_oi_test,
        )
        assert len(output_with_pcr) == len(self.underlying_test_df)

    def test_add_greeks_success(self, operator_obj: Operator):
        output_with_greeks = operator_obj.apply_operation(
            operation_name=Operation.GREEKS,
            symbol="symbol_name",
            universe_name="opt",
            option_df=self.option_test_df,
            underlying_df=self.underlying_test_df,
        )
        assert len(output_with_greeks) == len(self.option_test_df)
        assert (output_with_greeks.iloc[0].iv - 0.306) < 0.001
        assert (output_with_greeks.iloc[0].delta + 0.005) < 0.001
        assert (output_with_greeks.iloc[0].theta + 0.544) < 0.01
        assert (output_with_greeks.iloc[0].vega - 0.908) < 0.01
        assert output_with_greeks.iv.isna().sum() == 29

    def test_add_cons_volume_basic_functionality(self, operator_obj: Operator):
        timestamps = pd.date_range(start="2023-01-01", periods=4, freq="D")
        option_df = pd.DataFrame(
            {
                "ID": [1001, 1001, 1002, 1002],
                "Close": [100, 110, 200, 210],
            },
            index=pd.Index(timestamps, name="timestamp"),
        )

        underlying_df = pd.DataFrame(
            {
                "ID": [1001, 1001, 1002, 1002],
                "Cons_Volume": [1000, 1100, 2000, 2100],
            },
            index=pd.Index(timestamps, name="timestamp"),
        )

        result = operator_obj.apply_operation(
            operation_name=Operation.CONS_VOLUME,
            symbol="symbol_name",
            option_df=option_df,
            underlying_df=underlying_df,
            universe_name="opt",
        )

        assert "Cons_Volume" in result.columns
        assert len(result) == len(option_df)

        assert result.iloc[0]["Cons_Volume"] == 1000
        assert result.iloc[1]["Cons_Volume"] == 1100
        assert result.iloc[2]["Cons_Volume"] == 2000
        assert result.iloc[3]["Cons_Volume"] == 2100

    def test_add_cons_volume_with_missing_values(self, operator_obj: Operator):
        timestamps = pd.date_range(start="2023-01-01", periods=4, freq="1min")
        option_df = pd.DataFrame(
            {
                "ID": [5001, 5001, 5001, 5001],
                "Close": [100, 110, 200, 210],
            },
            index=pd.Index(timestamps, name="timestamp"),
        )

        underlying_df = pd.DataFrame(
            {
                "ID": [5001, 5001, 5001],  # Missing entry for second timestamp
                "Cons_Volume": [1000, 2000, 2100],
            },
            index=pd.Index(timestamps[[0, 2, 3]], name="timestamp"),
        )

        result = operator_obj.apply_operation(
            operation_name=Operation.CONS_VOLUME,
            symbol="5001",
            universe_name="opt",
            option_df=option_df,
            underlying_df=underlying_df,
        )

        assert "Cons_Volume" in result.columns
        assert len(result) == len(option_df)

        assert result.iloc[0]["Cons_Volume"] == 1000
        assert (
            result.iloc[1]["Cons_Volume"] == 1000
        )  # Forward-filled from previous timestamp
        assert result.iloc[2]["Cons_Volume"] == 2000
        assert result.iloc[3]["Cons_Volume"] == 2100

    def test_add_cons_volume_with_different_timestamps(self, operator_obj: Operator):
        timestamps = [
            pd.Timestamp("2023-01-01 10:00:00"),
            pd.Timestamp("2023-01-01 11:00:00"),
            pd.Timestamp("2023-01-02 10:00:00"),
            pd.Timestamp("2023-01-02 11:00:00"),
        ]
        option_df = pd.DataFrame(
            {
                "ID": [1001, 1001, 1002, 1002],
                "Close": [100, 110, 200, 210],
            },
            index=pd.Index(timestamps, name="timestamp"),
        )
        timestamps = [
            pd.Timestamp("2023-01-01 10:00:00"),
            pd.Timestamp("2023-01-02 10:00:00"),
        ]
        underlying_df = pd.DataFrame(
            {
                "ID": [1001, 1002],
                "Cons_Volume": [1000, 2000],
            },
            index=pd.Index(timestamps, name="timestamp"),
        )

        result = operator_obj.apply_operation(
            operation_name=Operation.CONS_VOLUME,
            symbol="symbol_name",
            universe_name="opt",
            option_df=option_df,
            underlying_df=underlying_df,
        )
        assert "Cons_Volume" in result.columns
        assert len(result) == len(option_df)

        assert result.iloc[0]["Cons_Volume"] == 1000
        assert result.iloc[1]["Cons_Volume"] == 1000
        assert result.iloc[2]["Cons_Volume"] == 2000
        assert result.iloc[3]["Cons_Volume"] == 2000

    def test_add_next_cons_volume_basic_functionality(self, operator_obj: Operator):
        timestamps = pd.date_range(start="2023-01-01", periods=4, freq="D")
        data = pd.DataFrame(
            {
                "ID": [2023011525001, 2023011525001, 2023021525001, 2023021525001],
                "Close": [100, 110, 200, 210],
                "Cons_Volume": [1000, 1100, 2000, 2100],
            },
            index=pd.Index(timestamps, name="timestamp"),
        )

        underlying_data = pd.DataFrame(
            {
                "ID": [2023011525001, 2023011525001, 2023021525001, 2023021525001],
                "Cons_Volume": [1000, 1100, 2000, 2100],
            },
            index=pd.Index(timestamps, name="timestamp"),
        )

        expiry_dict = {
            pd.Timestamp("2023-01-01"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
            },
            pd.Timestamp("2023-01-02"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
            },
            pd.Timestamp("2023-01-03"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
            },
            pd.Timestamp("2023-01-04"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
            },
        }

        result = operator_obj.apply_operation(
            operation_name=Operation.NEXT_CONS_VOLUME,
            symbol="5001",
            universe_name="futidx_fut",
            option_df=data,
            underlying_df=underlying_data,
            expiry_dict=expiry_dict,
        )

        assert "Next_Cons_Volume" in result.columns
        assert len(result) == len(data)

        # First two rows should have next month's volume (2000, 2100)
        # Last two rows should have NaN as there's no "next next" month
        assert (
            pd.isna(result.iloc[0]["Next_Cons_Volume"])
            or result.iloc[0]["Next_Cons_Volume"] == 2000
        )
        assert (
            pd.isna(result.iloc[1]["Next_Cons_Volume"])
            or result.iloc[1]["Next_Cons_Volume"] == 2100
        )
        assert pd.isna(result.iloc[2]["Next_Cons_Volume"])
        assert pd.isna(result.iloc[3]["Next_Cons_Volume"])

    def test_add_next_cons_volume_with_missing_expiry_data(
        self, operator_obj: Operator
    ):
        timestamps = pd.date_range(start="2023-01-01", periods=4, freq="D")
        data = pd.DataFrame(
            {
                "ID": [
                    2023011525001,
                    2023011525001,
                    2023031525001,
                    2023031525001,
                ],  # March expiry not in dict
                "Close": [100, 110, 200, 210],
                "Cons_Volume": [1000, 1100, 2000, 2100],
            },
            index=pd.Index(timestamps, name="timestamp"),
        )

        underlying_data = pd.DataFrame(
            {
                "ID": [2023011525001, 2023011525001, 2023021525001, 2023021525001],
                "Cons_Volume": [1000, 1100, 2000, 2100],
            },
            index=pd.Index(timestamps, name="timestamp"),
        )

        expiry_dict = {
            pd.Timestamp("2023-01-01"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
            },
            pd.Timestamp("2023-01-02"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
            },
            pd.Timestamp("2023-01-03"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
            },
            pd.Timestamp("2023-01-04"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
            },
        }

        result = operator_obj.apply_operation(
            operation_name=Operation.NEXT_CONS_VOLUME,
            symbol="5001",
            universe_name="futidx_fut",
            option_df=data,
            underlying_df=underlying_data,
            expiry_dict=expiry_dict,
        )

        assert "Next_Cons_Volume" in result.columns
        assert len(result) == len(data)

        # First two rows should have next month's volume (2000, 2100)
        # Last two rows should have NaN as they don't match any expiry in the dict
        assert (
            pd.isna(result.iloc[0]["Next_Cons_Volume"])
            or result.iloc[0]["Next_Cons_Volume"] == 2000
        )
        assert (
            pd.isna(result.iloc[1]["Next_Cons_Volume"])
            or result.iloc[1]["Next_Cons_Volume"] == 2100
        )
        assert pd.isna(result.iloc[2]["Next_Cons_Volume"])
        assert pd.isna(result.iloc[3]["Next_Cons_Volume"])

    def test_add_next_cons_volume_with_empty_data(self, operator_obj: Operator):
        data = pd.DataFrame(
            columns=["ID", "Close"],
            index=pd.DatetimeIndex([], name="timestamp"),
        )

        underlying_data = pd.DataFrame(
            columns=["ID", "Cons_Volume"],
            index=pd.DatetimeIndex([], name="timestamp"),
        )

        expiry_dict = {
            pd.Timestamp("2023-01-01"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
            },
        }

        result = operator_obj.apply_operation(
            operation_name=Operation.NEXT_CONS_VOLUME,
            symbol="5001",
            universe_name="futidx_fut",
            option_df=data,
            underlying_df=underlying_data,
            expiry_dict=expiry_dict,
        )

        assert len(result) == 0
        assert "Next_Cons_Volume" in result.columns

    def test_add_next_cons_volume_with_mismatched_timestamps(
        self, operator_obj: Operator
    ):
        data_timestamps = pd.date_range(start="2023-01-01", periods=2, freq="D")
        data = pd.DataFrame(
            {
                "ID": [2023011525001, 2023021525001],
                "Close": [100, 200],
                "Cons_Volume": [1000, 2000],
            },
            index=pd.Index(data_timestamps, name="timestamp"),
        )
        underlying_timestamps = pd.date_range(start="2023-01-02", periods=2, freq="D")
        underlying_data = pd.DataFrame(
            {
                "ID": [2023011525001, 2023021525001],
                "Cons_Volume": [1100, 2100],
            },
            index=pd.Index(underlying_timestamps, name="timestamp"),
        )

        expiry_dict = {
            pd.Timestamp("2023-01-01"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
            },
            pd.Timestamp("2023-01-02"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
            },
            pd.Timestamp("2023-01-03"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
            },
        }

        result = operator_obj.apply_operation(
            operation_name=Operation.NEXT_CONS_VOLUME,
            symbol="5001",
            universe_name="futidx_fut",
            option_df=data,
            underlying_df=underlying_data,
            expiry_dict=expiry_dict,
        )

        assert "Next_Cons_Volume" in result.columns
        assert len(result) == len(data)

        assert pd.isna(result.iloc[0]["Next_Cons_Volume"])
        assert pd.isna(result.iloc[1]["Next_Cons_Volume"])

    def test_add_next_cons_volume_with_multiple_expiry_ranks(
        self, operator_obj: Operator
    ):
        timestamps = [pd.Timestamp("2023-01-01 09:17:00")] * 4
        data = pd.DataFrame(
            {
                "ID": [2023011525001, 2023021525001, 2023031525001, 2023041525001],
                "Close": [100, 200, 300, 400],
                "Cons_Volume": [1000, 2000, 3000, 4000],
            },
            index=pd.Index(timestamps, name="timestamp"),
        )

        underlying_data = pd.DataFrame(
            {
                "ID": [2023011525001, 2023021525001, 2023031525001, 2023041525001],
                "Cons_Volume": [1000, 2000, 3000, 4000],
            },
            index=pd.Index(timestamps, name="timestamp"),
        )

        expiry_dict = {
            pd.Timestamp("2023-01-01"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
            },
        }

        result = operator_obj.apply_operation(
            operation_name=Operation.NEXT_CONS_VOLUME,
            symbol="5001",
            universe_name="futidx_fut",
            option_df=data,
            underlying_df=underlying_data,
            expiry_dict=expiry_dict,
        )

        assert "Next_Cons_Volume" in result.columns
        assert len(result) == len(data)
        assert result.iloc[0]["Next_Cons_Volume"] == 2000
        assert pd.isna(result.iloc[1]["Next_Cons_Volume"])
        assert pd.isna(result.iloc[2]["Next_Cons_Volume"])
        assert pd.isna(result.iloc[3]["Next_Cons_Volume"])

    def test_resample_data_1_min_to_5_min_success(self, operator_obj: Operator):
        data = pd.read_parquet(
            "./test/utility/test_data/resample_data_1_min_to_5_min_test_data.parquet"
        )
        resample_data = operator_obj.resample_data_1_min_to_5_min(
            universe="opt", data=data
        )
        assert compare_floats(resample_data.iloc[1, 2], 112.0)

    def test_resample_data_1_min_to_5_min_extra_column_raises_exception(
        self, operator_obj: Operator
    ):
        data = pd.read_parquet(
            "./test/utility/test_data/resample_data_1_min_to_5_min_test_data.parquet"
        )
        data["extra_column"] = data["Cons_Volume"] > 500
        extra_columns = ["extra_column"]
        expected_message = f"resample_data_1_min_to_5_min error: extra columns: {extra_columns} in data"
        with pytest.raises(Exception) as exception_info:
            operator_obj.resample_data_1_min_to_5_min(universe="opt", data=data)
        assert str(exception_info.value) == expected_message

    def test_resample_data_1_min_to_5_min_missing_column_success(
        self, operator_obj: Operator
    ):
        data = pd.read_parquet(
            "./test/utility/test_data/resample_data_1_min_to_5_min_test_data.parquet"
        )
        data.drop("Open", axis=1, inplace=True)
        resample_data = operator_obj.resample_data_1_min_to_5_min(
            universe="opt", data=data
        )
        assert "Open" not in resample_data.columns

    def test_create_column_bucket_success(self, operator_obj: Operator):
        output_dict = operator_obj.create_column_bucket(
            universe="optcom", data=self.column_bucket_test_df
        )
        assert len(output_dict) == 11
        assert compare_floats(
            output_dict["Open_int"].loc[
                (pd.to_datetime(datetime.date(2023, 10, 19)), 96252023102315008),
                "12:55",
            ],
            18300.0,
        )
        for data_column in output_dict.values():
            assert len(data_column.index) == 10
            assert len(data_column.columns) == 9

    def test_create_column_bucket_duplicate_entry_raises_exception(
        self, operator_obj: Operator
    ):
        expected_message = "Creation of column bucket fails due to ValueError('Index contains duplicate entries, cannot reshape')"
        data = pd.read_parquet(
            "./test/utility/test_data/create_column_bucket_duplicate_entry_test_data.parquet"
        )
        with pytest.raises(Exception) as exception_info:
            operator_obj.create_column_bucket(universe="optcom", data=data)
        assert str(exception_info.value) == expected_message

    def test_filter_id_basic_functionality(self, operator_obj: Operator):
        timestamps = pd.date_range(start="2023-01-01", periods=5, freq="D")
        data = pd.DataFrame(
            {
                "ID": [5001, 5001, 5001, 5001, 5001],
                "Close": [100, 200, 300, 400, 500],
            },
            index=pd.Index(timestamps, name="timestamp"),
        )

        filter_timestamps = pd.date_range(
            start="2023-01-01", periods=3, freq="D"
        ).to_list()
        filtering_list = pd.DataFrame(
            {
                "ID": [5001, 5001, 5001, 5002, 5002, 5002],
            },
            index=pd.Index(filter_timestamps + filter_timestamps, name="date"),
        )

        result = operator_obj.apply_operation(
            operation_name=Operation.FILTER,
            symbol="5001",
            universe_name="test_universe",
            universe_df=data,
            id_list=filtering_list,
        )

        assert len(result) == 3
        assert len(result["ID"].unique()) == 1
        assert result["ID"].unique()[0] == 5001
        assert result.index.name == "timestamp"
        assert list(result.index) == list(filter_timestamps)
        assert result.iloc[0]["Close"] == 100
        assert result.iloc[1]["Close"] == 200
        assert result.iloc[2]["Close"] == 300

    def test_filter_id_empty_filtering_list(self, operator_obj: Operator):
        timestamps = pd.date_range(start="2023-01-01", periods=5, freq="D")
        data = pd.DataFrame(
            {
                "ID": [5001, 5001, 5001, 5001, 5001],
                "Close": [100, 200, 300, 400, 500],
            },
            index=pd.Index(timestamps, name="timestamp"),
        )

        filtering_list = pd.DataFrame(
            {
                "ID": [],
            },
            index=pd.Index([], name="date"),
        )

        result = operator_obj.apply_operation(
            operation_name=Operation.FILTER,
            symbol="5001",
            universe_name="test_universe",
            universe_df=data,
            id_list=filtering_list,
        )

        assert len(result) == 0
        assert list(result.columns) == list(data.columns)

    def test_filter_id_no_matching_symbol(self, operator_obj: Operator):
        timestamps = pd.date_range(start="2023-01-01", periods=5, freq="D")
        data = pd.DataFrame(
            {
                "ID": [1001, 1001, 1001, 1001, 1001],
                "value": [100, 200, 300, 400, 500],
            },
            index=pd.Index(timestamps, name="timestamp"),
        )

        filter_timestamps = pd.date_range(start="2023-01-01", periods=3, freq="D")
        filtering_list = pd.DataFrame(
            {
                "ID": [1002, 1002, 1002],
            },
            index=pd.Index(filter_timestamps, name="date"),
        )

        result = operator_obj.apply_operation(
            operation_name=Operation.FILTER,
            symbol="1001",
            universe_name="test_universe",
            universe_df=data,
            id_list=filtering_list,
        )

        assert len(result) == 0
        assert list(result.columns) == list(data.columns)

    def test_filter_id_with_date_column_in_data(self, operator_obj: Operator):
        timestamps = pd.date_range(start="2023-01-01", periods=5, freq="D")
        data = pd.DataFrame(
            {
                "ID": [1001, 1001, 1001, 1001, 1001],
                "Close": [100, 200, 300, 400, 500],
                "date": timestamps.normalize(),
            },
            index=pd.Index(timestamps, name="timestamp"),
        )

        filter_timestamps = pd.date_range(start="2023-01-01", periods=3, freq="D")
        filtering_list = pd.DataFrame(
            {
                "ID": [1001, 1001, 1001],
            },
            index=pd.Index(filter_timestamps, name="date"),
        )

        result = operator_obj.apply_operation(
            operation_name=Operation.FILTER,
            symbol="1001",
            universe_name="test_universe",
            universe_df=data,
            id_list=filtering_list,
        )

        assert len(result) == 3
        assert result["ID"].unique()[0] == 1001
        assert "date" not in result.columns
        assert result.index.name == "timestamp"

    def test_filter_id_with_date_in_index_names(self, operator_obj: Operator):
        timestamps = pd.date_range(start="2023-01-01", periods=5, freq="D")
        data = pd.DataFrame(
            {
                "ID": [1001, 1001, 1001, 1001, 1001],
                "Close": [100, 200, 300, 400, 500],
                "timestamp": timestamps,
            },
            index=pd.Index(timestamps, name="date"),
        )

        filter_timestamps = pd.date_range(start="2023-01-01", periods=3, freq="D")
        filtering_list = pd.DataFrame(
            {
                "ID": [1001, 1001, 1001],
            },
            index=pd.Index(filter_timestamps, name="date"),
        )

        result = operator_obj.apply_operation(
            operation_name=Operation.FILTER,
            symbol="1001",
            universe_name="test_universe",
            universe_df=data,
            id_list=filtering_list,
        )

        assert len(result) == 3
        assert result["ID"].unique()[0] == 1001
        assert result.index.name == "timestamp"

    def test_filter_near_contract_basic_functionality(self, operator_obj: Operator):
        timestamps = pd.date_range(start="2023-01-01", periods=3, freq="D")
        data = pd.DataFrame(
            {
                "ID": [
                    2023011525001,  # Near month
                    2023021525001,  # Next month
                    2023031525001,  # Far month
                ],
                "Close": [100, 200, 300],
            },
            index=pd.Index(timestamps, name="timestamp"),
        )

        expiry_dict = {
            pd.Timestamp("2023-01-01"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
                "far_month": pd.Timestamp("2023-03-15"),
            },
            pd.Timestamp("2023-01-02"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
                "far_month": pd.Timestamp("2023-03-15"),
            },
            pd.Timestamp("2023-01-03"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
                "far_month": pd.Timestamp("2023-03-15"),
            },
        }

        result = operator_obj.apply_operation(
            operation_name=Operation.FILTER,
            symbol="5001",
            universe_name="test_universe",
            universe_df=data.copy(),
            expiry_dict=expiry_dict,
            expiry_categories=["near_month"],
            filter_rank=1,
        )

        assert len(result) == 1
        assert result["ID"].iloc[0] == 2023011525001
        assert "expiry" not in result.columns
        assert "expiry_rank" not in result.columns
        assert "date" not in result.columns
        assert "near_month" not in result.columns
        assert "next_month" not in result.columns
        assert result.index.name == "timestamp"

    def test_filter_near_contract_empty_dataframe(self, operator_obj: Operator):
        data = pd.DataFrame(columns=["ID", "value"])
        expiry_dict = {
            pd.Timestamp("2023-01-01"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
                "far_month": pd.Timestamp("2023-03-15"),
            },
        }

        result = operator_obj.apply_operation(
            operation_name=Operation.FILTER,
            symbol="5001",
            universe_name="test_universe",
            universe_df=data,
            expiry_dict=expiry_dict,
            expiry_categories=["near_month"],
            filter_rank=1,
        )

        assert len(result) == 0
        assert list(result.columns) == list(data.columns)

    def test_filter_near_contract_no_matching_expiry(self, operator_obj: Operator):
        timestamps = pd.date_range(start="2023-01-01", periods=3, freq="D")
        data = pd.DataFrame(
            {
                # near and next month expiry is not present in the data
                "ID": [
                    2023041525001,
                    2023051525001,
                    2023061525001,
                ],
                "Close": [100, 200, 300],
            },
            index=pd.Index(timestamps, name="timestamp"),
        )
        expiry_dict = {
            pd.Timestamp("2023-01-01"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
            },
            pd.Timestamp("2023-01-02"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
            },
            pd.Timestamp("2023-01-03"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
            },
        }

        result = operator_obj.apply_operation(
            operation_name=Operation.FILTER,
            symbol="5001",
            universe_name="test_universe",
            universe_df=data,
            expiry_dict=expiry_dict,
            expiry_categories=["near_month"],
            filter_rank=1,
        )

        assert len(result) == 0

    def test_filter_near_contract_missing_expiry_dates(self, operator_obj: Operator):
        timestamps = pd.date_range(start="2023-01-01", periods=5, freq="D")
        data = pd.DataFrame(
            {
                "ID": [
                    2023011525001,
                    2023021525001,
                    2023011525001,
                    2023031525001,
                    2023021525001,
                ],
                "Close": [100, 200, 300, 400, 500],
            },
            index=pd.Index(timestamps, name="timestamp"),
        )
        expiry_dict = {
            pd.Timestamp("2023-01-01"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
            },
            pd.Timestamp("2023-01-03"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
            },
            pd.Timestamp("2023-01-05"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
            },
            # Missing entries for 2023-01-02 and 2023-01-04
        }

        result = operator_obj.apply_operation(
            operation_name=Operation.FILTER,
            symbol="5001",
            universe_name="test_universe",
            universe_df=data,
            expiry_dict=expiry_dict,
            expiry_categories=["near_month"],
            filter_rank=1,
        )

        assert len(result) == 2
        assert all(
            [
                pd.Timestamp("2023-01-01") in result.index,
                pd.Timestamp("2023-01-03") in result.index,
            ]
        )
        assert "ID" in result.columns
        assert "Close" in result.columns
        assert "expiry" not in result.columns
        assert "expiry_rank" not in result.columns

    def test_filter_symbol_change_basic_functionality(self, operator_obj: Operator):
        original_read_method = operator_obj.read
        old_symbol_data = pd.DataFrame(
            {
                "Close": [100, 110, 120],
                "Volume": [1000, 1100, 1200],
                "Symbol": ["OLD_SYM"] * 3,
            },
            index=pd.DatetimeIndex(
                ["2022-01-01", "2022-01-02", "2022-01-03"], name="timestamp"
            ),
        )
        new_symbol_data = pd.DataFrame(
            {
                "Close": [200, 210, 220],
                "Volume": [2000, 2100, 2200],
                "Symbol": ["CURRENT_SYM"] * 3,
            },
            index=pd.DatetimeIndex(
                ["2022-01-04", "2022-01-05", "2022-01-06"], name="timestamp"
            ),
        )
        filtering_list = pd.DataFrame(
            {
                "symbol": ["OLD_SYM", "CURRENT_SYM"],
                "current": ["CURRENT_SYM", "CURRENT_SYM"],
            },
            index=pd.DatetimeIndex(["2022-01-04", "2022-01-04"], name="date"),
        )

        def mock_read(*args, **kwargs):
            if kwargs.get("file_name") == "OLD_SYM":
                return old_symbol_data
            elif kwargs.get("file_name") == "CURRENT_SYM":
                return new_symbol_data
            return pd.DataFrame()

        try:
            operator_obj.read = mock_read
            result_old = operator_obj.apply_operation(
                operation_name=Operation.FILTER,
                symbol="OLD_SYM",
                frequency=1,
                dtype="trd",
                universe_name="test_universe",
                universe_df=pd.DataFrame(),
                symbol_change=filtering_list.copy(),
                start_date=pd.Timestamp("2022-01-01"),
                end_date=pd.Timestamp("2022-01-06"),
            )
            assert len(result_old) == 0

            result = operator_obj.apply_operation(
                operation_name=Operation.FILTER,
                symbol="CURRENT_SYM",
                frequency=1,
                dtype="trd",
                universe_name="test_universe",
                universe_df=pd.DataFrame(),
                symbol_change=filtering_list.copy(),
                start_date=pd.Timestamp("2022-01-01"),
                end_date=pd.Timestamp("2022-01-06"),
            )

            assert len(result) == 6
            assert "Symbol" in result.columns
            assert all(result["Symbol"] == "CURRENT_SYM")

            expected_close_values = [100, 110, 120, 200, 210, 220]
            assert all(result["Close"] == expected_close_values)

        finally:
            operator_obj.read = original_read_method

    def test_filter_symbol_change_no_symbol_change(self, operator_obj: Operator):
        original_read_method = operator_obj.read
        symbol_data = pd.DataFrame(
            {
                "Close": [100, 110, 120],
                "Volume": [1000, 1100, 1200],
                "Symbol": ["TEST_SYM"] * 3,
            },
            index=pd.DatetimeIndex(
                ["2022-01-01", "2022-01-02", "2022-01-03"], name="timestamp"
            ),
        )

        filtering_list = pd.DataFrame(
            {
                "symbol": ["OTHER_SYM"],
                "current": ["OTHER_SYM"],
            },
            index=pd.DatetimeIndex(["2022-01-01"], name="date"),
        )

        def mock_read(*args, **kwargs):
            if kwargs.get("file_name") == "TEST_SYM":
                return symbol_data
            return pd.DataFrame()

        try:
            operator_obj.read = mock_read

            result = operator_obj.apply_operation(
                operation_name=Operation.FILTER,
                symbol="TEST_SYM",
                universe_name="test_universe",
                frequency=1,
                dtype="trd",
                universe_df=pd.DataFrame(),
                symbol_change=filtering_list.copy(),
                start_date=pd.Timestamp("2022-01-01"),
                end_date=pd.Timestamp("2022-01-03"),
            )

            # Verify results
            assert len(result) == 3
            assert "Symbol" in result.columns
            assert all(result["Symbol"] == "TEST_SYM")

        finally:
            operator_obj.read = original_read_method

    def test_filter_symbol_change_read_error(self, operator_obj: Operator):
        original_read_method = operator_obj.read
        filtering_list = pd.DataFrame(
            {
                "symbol": ["TEST_SYM"],
                "current": ["TEST_SYM"],
            },
            index=pd.DatetimeIndex(["2022-01-01"], name="date"),
        )

        def mock_read(*args, **kwargs):
            raise Exception("Read error")

        try:
            operator_obj.read = mock_read

            result = operator_obj.apply_operation(
                operation_name=Operation.FILTER,
                symbol="TEST_SYM",
                universe_name="test_universe",
                frequency=1,
                dtype="trd",
                universe_df=pd.DataFrame(),
                symbol_change=filtering_list.copy(),
                start_date=pd.Timestamp("2022-01-01"),
                end_date=pd.Timestamp("2022-01-03"),
            )

            assert len(result) == 0

        finally:
            operator_obj.read = original_read_method

    def test_filter_demerger_merger_basic_functionality(self, operator_obj: Operator):
        timestamps = pd.date_range(start="2023-01-01", periods=10, freq="D")
        data = pd.DataFrame(
            {
                "ID": [5001] * 10,
                "Close": list(range(100, 1100, 100)),
                "Symbol": ["SYMBOL1"] * 10,
            },
            index=pd.Index(timestamps, name="timestamp"),
        )

        filtering_list = pd.DataFrame(
            {
                "Symbol": ["SYMBOL1"] * 3,
                "ID": [4001, 4501, 5001],
                "exdate": [
                    pd.Timestamp("2023-01-03"),  # Before this date, use ID 4001
                    pd.Timestamp("2023-01-07"),  # Between these dates, use ID 4501
                    pd.Timestamp("2023-01-15"),
                ],
            },
        ).set_index("exdate")

        result = operator_obj.apply_operation(
            operation_name=Operation.FILTER,
            symbol="SYMBOL1",
            ID=5001,  # Current ID
            universe_name="test_universe",
            universe_df=data.copy(),
            demerger_merger=filtering_list,
        )
        assert len(result) == len(data)

        # Check ID assignments based on dates
        # Before 2023-01-03: ID should be 4001
        assert all(result.loc[result.index < pd.Timestamp("2023-01-03"), "ID"] == 4001)

        # Between 2023-01-03 and 2023-01-07: ID should be 4501
        mask = (result.index >= pd.Timestamp("2023-01-03")) & (
            result.index < pd.Timestamp("2023-01-07")
        )
        assert all(result.loc[mask, "ID"] == 4501)

        # After 2023-01-07: ID should remain 5001
        assert all(result.loc[result.index >= pd.Timestamp("2023-01-07"), "ID"] == 5001)

    def test_filter_demerger_merger_empty_dataframe(self, operator_obj: Operator):
        data = pd.DataFrame(columns=["ID", "Close"])
        filtering_list = pd.DataFrame(
            {
                "Symbol": ["SYMBOL1"],
                "ID": [4001],
                "exdate": [pd.Timestamp("2023-01-03")],
            },
        ).set_index("exdate")

        result = operator_obj.apply_operation(
            operation_name=Operation.FILTER,
            symbol="SYMBOL1",
            ID=4001,
            universe_name="test_universe",
            universe_df=data.copy(),
            demerger_merger=filtering_list,
        )
        assert len(result) == 0
        assert list(result.columns) == list(data.columns)

    def test_filter_demerger_merger_no_matching_symbol(self, operator_obj: Operator):
        timestamps = pd.date_range(start="2023-01-01", periods=5, freq="D")
        data = pd.DataFrame(
            {
                "ID": [5001] * 5,
                "Close": list(range(100, 600, 100)),
            },
            index=pd.Index(timestamps, name="timestamp"),
        )

        filtering_list = pd.DataFrame(
            {
                "Symbol": ["DIFFERENT_SYMBOL"],
                "ID": [4001],
                "exdate": [pd.Timestamp("2023-01-03")],
            }
        ).set_index("exdate")

        result = operator_obj.apply_operation(
            operation_name=Operation.FILTER,
            symbol="SYMBOL1",
            ID=5001,
            universe_name="test_universe",
            universe_df=data.copy(),
            demerger_merger=filtering_list,
        )
        assert len(result) == len(data)
        assert data.equals(result)

    def test_apply_corpact_basic_functionality(self, operator_obj: Operator):
        timestamps = pd.date_range(start="2023-01-01", periods=5, freq="D")
        data = pd.DataFrame(
            {
                "ID": [5001, 5001, 5001, 5001, 5001],
                "Open": [100.0, 200.0, 300.0, 400.0, 500.0],
                "High": [110.0, 210.0, 310.0, 410.0, 510.0],
                "Low": [90.0, 190.0, 290.0, 390.0, 490.0],
                "Close": [105.0, 205.0, 305.0, 405.0, 505.0],
                "Cons_Volume": [1000, 2000, 3000, 4000, 5000],
            },
            index=pd.Index(timestamps, name="timestamp"),
        )

        corpact_dates = [pd.Timestamp("2023-01-03"), pd.Timestamp("2023-01-05")]
        corpact_info = pd.DataFrame(
            {
                "ID": [5001, 5001],
                "adj_factor": [2.0, 1.5],
            },
            index=pd.Index(corpact_dates, name="exdate"),
        )

        original_columns_dict = operator_obj._config.COLUMNS_DICT
        operator_obj._config.COLUMNS_DICT = {
            "test_universe": ["ID", "Open", "High", "Low", "Close", "Cons_Volume"]
        }

        try:
            result = operator_obj.apply_operation(
                operation_name=Operation.MODIFY,
                symbol="5001",
                universe_name="test_universe",
                universe_df=data.copy(),
                corpact_info=corpact_info,
            )

            assert len(result) == len(data)
            assert "adj_factor" not in result.columns
            # Dates before 2023-01-03 are multiplied by both adjustment factors
            assert result.loc[pd.Timestamp("2023-01-01"), "Open"] == 100.0 * 2.0 * 1.5
            assert result.loc[pd.Timestamp("2023-01-02"), "Close"] == 205.0 * 2.0 * 1.5

            # Dates between 2023-01-03 and 2023-01-05 (should be multiplied by 1.5)
            assert result.loc[pd.Timestamp("2023-01-03"), "High"] == 310.0 * 1.5
            assert result.loc[pd.Timestamp("2023-01-04"), "Low"] == 390.0 * 1.5

            # Dates after 2023-01-05 are not adjusted
            assert result.loc[pd.Timestamp("2023-01-05"), "Open"] == 500.0

            # Cons_Volume is divided by the adjustment factor (inverse operation)
            assert result.loc[pd.Timestamp("2023-01-01"), "Cons_Volume"] == 1000 / (
                2.0 * 1.5
            )
            assert result.loc[pd.Timestamp("2023-01-03"), "Cons_Volume"] == 3000 / 1.5

        finally:
            operator_obj._config.COLUMNS_DICT = original_columns_dict

    def test_apply_corpact_empty_data(self, operator_obj: Operator):
        data = pd.DataFrame(
            columns=["ID", "Open", "High", "Low", "Close", "Cons_Volume"],
            index=pd.DatetimeIndex([], name="timestamp"),
        )

        corpact_dates = [pd.Timestamp("2023-01-03")]
        corpact_info = pd.DataFrame(
            {
                "ID": [5001],
                "adj_factor": [2.0],
            },
            index=pd.Index(corpact_dates, name="exdate"),
        )

        original_columns_dict = operator_obj._config.COLUMNS_DICT
        operator_obj._config.COLUMNS_DICT = {
            "test_universe": ["ID", "Open", "High", "Low", "Close", "Cons_Volume"]
        }

        try:
            result = operator_obj.apply_operation(
                operation_name=Operation.MODIFY,
                symbol="5001",
                universe_name="test_universe",
                universe_df=data.copy(),
                corpact_info=corpact_info,
            )
            assert len(result) == 0
            assert set(result.columns) == set(
                ["ID", "Open", "High", "Low", "Close", "Cons_Volume"]
            )

        finally:
            operator_obj._config.COLUMNS_DICT = original_columns_dict

    def test_apply_corpact_keep_adj_factor(self, operator_obj: Operator):
        timestamps = pd.date_range(start="2023-01-01", periods=3, freq="D")
        data = pd.DataFrame(
            {
                "ID": [5001, 5001, 5001],
                "Open": [100.0, 200.0, 300.0],
                "Close": [105.0, 205.0, 305.0],
            },
            index=pd.Index(timestamps, name="timestamp"),
        )

        corpact_dates = [pd.Timestamp("2023-01-02")]
        corpact_info = pd.DataFrame(
            {
                "ID": [5001],
                "adj_factor": [2.0],
            },
            index=pd.Index(corpact_dates, name="exdate"),
        )
        original_columns_dict = operator_obj._config.COLUMNS_DICT
        operator_obj._config.COLUMNS_DICT = {
            "test_universe": ["ID", "Open", "Close", "adj_factor"]
        }

        try:
            result = operator_obj.apply_operation(
                operation_name=Operation.MODIFY,
                symbol="5001",
                universe_name="test_universe",
                universe_df=data.copy(),
                corpact_info=corpact_info,
            )

            assert len(result) == len(data)
            assert "adj_factor" in result.columns

            # Corpact does not applied to adj_factor itself
            assert result.loc[pd.Timestamp("2023-01-01"), "adj_factor"] == 2.0
            assert result.loc[pd.Timestamp("2023-01-02"), "adj_factor"] == 1.0
            assert result.loc[pd.Timestamp("2023-01-03"), "adj_factor"] == 1.0

        finally:
            operator_obj._config.COLUMNS_DICT = original_columns_dict

    def test_apply_basis_adjustment_basic_functionality(self, operator_obj: Operator):
        timestamps = pd.date_range(start="2023-01-01", periods=3, freq="D")
        data = pd.DataFrame(
            {
                "ID": [2023011525001, 2023011525001, 2023011525001],
                "Open": [100.0, 200.0, 300.0],
                "High": [110.0, 210.0, 310.0],
                "Low": [90.0, 190.0, 290.0],
                "Close": [105.0, 205.0, 305.0],
                "Vwap": [102.0, 202.0, 302.0],
            },
            index=pd.Index(timestamps, name="timestamp"),
        )

        repo_dates = [pd.Timestamp("2023-01-01"), pd.Timestamp("2023-01-03")]
        reporate_info = pd.DataFrame(
            {
                "repo_rate": [0.05, 0.06],
            },
            index=pd.Index(repo_dates, name="date"),
        )

        result = operator_obj.apply_operation(
            operation_name=Operation.MODIFY,
            symbol="symbol_name",
            universe_name="futidx_fut",
            universe_df=data.copy(),
            reporate_info=reporate_info,
        )

        assert len(result) == len(data)
        assert "repo_rate" not in result.columns  # Should be removed
        assert "expiry" not in result.columns  # Should be removed
        assert "date" not in result.columns  # Should be removed

        # Calculate expected adjustments
        # For 2023-01-01: 14 days to expiry, repo rate 5%
        days_to_expiry_1 = (
            pd.Timestamp("2023-01-15") - pd.Timestamp("2023-01-01")
        ).days
        adj_1 = 1 - (0.05 * days_to_expiry_1 / 365)

        # For 2023-01-02: 13 days to expiry, repo rate 5% (ffilled)
        days_to_expiry_2 = (
            pd.Timestamp("2023-01-15") - pd.Timestamp("2023-01-02")
        ).days
        adj_2 = 1 - (0.05 * days_to_expiry_2 / 365)

        # For 2023-01-03: 12 days to expiry, repo rate 6%
        days_to_expiry_3 = (
            pd.Timestamp("2023-01-15") - pd.Timestamp("2023-01-03")
        ).days
        adj_3 = 1 - (0.06 * days_to_expiry_3 / 365)

        assert result.loc[pd.Timestamp("2023-01-01"), "Open"] == 100.0 * adj_1
        assert result.loc[pd.Timestamp("2023-01-02"), "High"] == 210.0 * adj_2
        assert result.loc[pd.Timestamp("2023-01-03"), "Low"] == 290.0 * adj_3
        assert result.loc[pd.Timestamp("2023-01-01"), "Close"] == 105.0 * adj_1
        assert result.loc[pd.Timestamp("2023-01-02"), "Vwap"] == 202.0 * adj_2

    def test_apply_basis_adjustment_empty_data(self, operator_obj: Operator):
        data = pd.DataFrame(columns=["ID", "Open", "High", "Low", "Close", "Vwap"])

        repo_dates = [pd.Timestamp("2023-01-01")]
        reporate_info = pd.DataFrame(
            {
                "repo_rate": [0.05],
            },
            index=pd.Index(repo_dates, name="date"),
        )

        result = operator_obj.apply_operation(
            operation_name=Operation.MODIFY,
            symbol="symbol_name",
            universe_name="futidx_fut",
            universe_df=data.copy(),
            reporate_info=reporate_info,
        )

        assert len(result) == 0
        assert list(result.columns) == ["ID", "Open", "High", "Low", "Close", "Vwap"]

    def test_apply_basis_adjustment_with_first_nan_repo_rate(
        self, operator_obj: Operator
    ):
        timestamps = pd.date_range(start="2023-01-01", periods=3, freq="D")
        data = pd.DataFrame(
            {
                "ID": [2023011525001, 2023011525001, 2023011525001],
                "Open": [100.0, 200.0, 300.0],
                "Close": [105.0, 205.0, 305.0],
            },
            index=pd.Index(timestamps, name="timestamp"),
        )

        repo_dates = [
            pd.Timestamp("2023-01-01"),
            pd.Timestamp("2023-01-02"),
            pd.Timestamp("2023-01-03"),
        ]
        reporate_info = pd.DataFrame(
            {
                "repo_rate": [np.nan, 0.06, 0.07],
            },
            index=pd.Index(repo_dates, name="date"),
        )

        result = operator_obj.apply_operation(
            operation_name=Operation.MODIFY,
            symbol="symbol_name",
            universe_name="futidx_fut",
            universe_df=data.copy(),
            reporate_info=reporate_info,
        )

        assert len(result) == len(data)

        # Calculate expected adjustments
        # The first NaN value should be filled with the first non-NaN value (0.06)

        # For 2023-01-01: 14 days to expiry, repo rate 0.06 (filled from 2023-01-02)
        days_to_expiry_1 = (
            pd.Timestamp("2023-01-15") - pd.Timestamp("2023-01-01")
        ).days
        adj_1 = 1 - (0.06 * days_to_expiry_1 / 365)

        # For 2023-01-02: 13 days to expiry, repo rate 0.06
        days_to_expiry_2 = (
            pd.Timestamp("2023-01-15") - pd.Timestamp("2023-01-02")
        ).days
        adj_2 = 1 - (0.06 * days_to_expiry_2 / 365)

        # For 2023-01-03: 12 days to expiry, repo rate 0.07
        days_to_expiry_3 = (
            pd.Timestamp("2023-01-15") - pd.Timestamp("2023-01-03")
        ).days
        adj_3 = 1 - (0.07 * days_to_expiry_3 / 365)

        assert result.loc[pd.Timestamp("2023-01-01"), "Open"] == 100.0 * adj_1
        assert result.loc[pd.Timestamp("2023-01-02"), "Open"] == 200.0 * adj_2
        assert result.loc[pd.Timestamp("2023-01-03"), "Open"] == 300.0 * adj_3

    def test_apply_dividend_removal(self, operator_obj: Operator):
        timestamps = pd.date_range(start="2023-01-01", periods=5, freq="D")
        data = pd.DataFrame(
            {
                "ID": [20230115001, 20230115001, 20230115001, 20230115001, 20230115001],
                "Open": [100.0, 200.0, 300.0, 400.0, 500.0],
                "High": [110.0, 210.0, 310.0, 410.0, 510.0],
                "Low": [90.0, 190.0, 290.0, 390.0, 490.0],
                "Close": [105.0, 205.0, 305.0, 405.0, 505.0],
                "Cons_Volume": [100, 200, 300, 400, 500],
            },
            index=pd.Index(timestamps, name="timestamp"),
        )

        expiry_dict = {
            pd.Timestamp("2023-01-01"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
            },
            pd.Timestamp("2023-01-02"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
            },
            pd.Timestamp("2023-01-03"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
            },
            pd.Timestamp("2023-01-04"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
            },
            pd.Timestamp("2023-01-05"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
            },
        }

        # Create proper corpact_dividend_info with announcement dates and ex-dates
        ann_dates = [pd.Timestamp("2023-01-02"), pd.Timestamp("2023-01-04")]
        corpact_dividend_info = pd.DataFrame(
            {
                "adj_factor": [1.02, 1.01],  # 2% and 1% dividend adjustments
                "exdate": [pd.Timestamp("2023-01-10"), pd.Timestamp("2023-01-12")],
            },
            index=pd.Index(ann_dates, name="ann_date"),
        )

        result = operator_obj.apply_operation(
            operation_name=Operation.MODIFY,
            symbol="20230115001",
            universe_name="test_universe",
            universe_df=data.copy(),
            corpact_dividend_info=corpact_dividend_info,
            expiry_dict=expiry_dict,
        )

        assert len(result) == len(data)
        assert "Vwap" not in result.columns

        for col in ["Open", "High", "Low", "Close"]:
            assert col in result.columns
        assert result["Cons_Volume"].iloc[0] == data["Cons_Volume"].iloc[0]

        empty_data = pd.DataFrame(columns=data.columns)
        result_empty = operator_obj.apply_operation(
            operation_name=Operation.MODIFY,
            symbol="20230115001",
            universe_name="test_universe",
            universe_df=empty_data.copy(),
            corpact_dividend_info=corpact_dividend_info,
            expiry_dict=expiry_dict,
        )

        assert len(result_empty) == 0
        assert list(result_empty.columns) == list(empty_data.columns)

        # Test with zero dividend factor - should raise ValueError
        corpact_dividend_info_zero = pd.DataFrame(
            {
                "adj_factor": [1.02, 0.0],  # Zero factor should cause error
                "exdate": [pd.Timestamp("2023-01-10"), pd.Timestamp("2023-01-12")],
            },
            index=pd.Index(ann_dates, name="ann_date"),
        )

        expected_error_message = (
            "One or more dividend factors are 0, cannot apply dividend removal"
        )
        with pytest.raises(ValueError) as exception_info:
            operator_obj.apply_operation(
                operation_name=Operation.MODIFY,
                symbol="20230115001",
                universe_name="test_universe",
                universe_df=data.copy(),
                corpact_dividend_info=corpact_dividend_info_zero,
                expiry_dict=expiry_dict,
            )
        assert str(exception_info.value) == expected_error_message

    def test_apply_dividend_removal_multiple_dates_complex(
        self, operator_obj: Operator
    ):
        """Test dividend removal with multiple announcement dates and complex scenarios."""
        timestamps = pd.date_range(start="2023-01-01", periods=10, freq="D")
        data = pd.DataFrame(
            {
                "ID": [20230120001] * 10,  # Expiry on 2023-01-20
                "Open": [100.0 + i * 10 for i in range(10)],
                "High": [110.0 + i * 10 for i in range(10)],
                "Low": [90.0 + i * 10 for i in range(10)],
                "Close": [105.0 + i * 10 for i in range(10)],
                "Vwap": [102.0 + i * 10 for i in range(10)],
                "Cons_Volume": [1000 + i * 100 for i in range(10)],
            },
            index=pd.Index(timestamps, name="timestamp"),
        )

        expiry_dict = {
            pd.Timestamp(f"2023-01-{i:02d}"): {
                "near_month": pd.Timestamp("2023-01-20"),
                "next_month": pd.Timestamp("2023-02-20"),
            }
            for i in range(1, 11)
        }

        ann_dates = [
            pd.Timestamp("2023-01-02"),  # Announced on Jan 2
            pd.Timestamp("2023-01-05"),  # Announced on Jan 5
            pd.Timestamp("2023-01-08"),  # Announced on Jan 8
        ]
        corpact_dividend_info = pd.DataFrame(
            {
                "adj_factor": [1.05, 1.03, 1.02],  # 5%, 3%, 2% dividend adjustments
                "exdate": [
                    pd.Timestamp("2023-01-15"),  # Ex-date Jan 15
                    pd.Timestamp("2023-01-18"),  # Ex-date Jan 18
                    pd.Timestamp("2023-01-22"),  # Ex-date Jan 22 (after expiry)
                ],
            },
            index=pd.Index(ann_dates, name="ann_date"),
        )

        result = operator_obj.apply_operation(
            operation_name=Operation.MODIFY,
            symbol="20230120001",
            universe_name="test_universe",
            universe_df=data.copy(),
            corpact_dividend_info=corpact_dividend_info,
            expiry_dict=expiry_dict,
        )

        assert len(result) == len(data)

        # Verify that all price columns are present and adjusted
        price_columns = ["Open", "High", "Low", "Close", "Vwap"]
        for col in price_columns:
            assert col in result.columns
            # Prices should be different from original (adjusted)
            assert not result[col].equals(data[col])

        # Volume should remain unchanged
        assert result["Cons_Volume"].to_list() == data["Cons_Volume"].to_list()

        corpact_dividend_info_nan = pd.DataFrame(
            {
                "adj_factor": [1.05, np.nan, 1.02],  # NaN should be filled with 1.0
                "exdate": [
                    pd.Timestamp("2023-01-15"),
                    pd.Timestamp("2023-01-18"),
                    pd.Timestamp("2023-01-22"),
                ],
            },
            index=pd.Index(ann_dates, name="ann_date"),
        )

        result_nan = operator_obj.apply_operation(
            operation_name=Operation.MODIFY,
            symbol="20230120001",
            universe_name="test_universe",
            universe_df=data.copy(),
            corpact_dividend_info=corpact_dividend_info_nan,
            expiry_dict=expiry_dict,
        )

        assert len(result_nan) == len(data)

    def test_apply_dividend_removal_edge_cases(self, operator_obj: Operator):
        """Test dividend removal edge cases and error conditions."""
        timestamps = pd.date_range(start="2023-01-01", periods=5, freq="D")
        data = pd.DataFrame(
            {
                "ID": [20230115001] * 5,
                "Open": [100.0, 200.0, 300.0, 400.0, 500.0],
                "High": [110.0, 210.0, 310.0, 410.0, 510.0],
                "Low": [90.0, 190.0, 290.0, 390.0, 490.0],
                "Close": [105.0, 205.0, 305.0, 405.0, 505.0],
                "Cons_Volume": [100, 200, 300, 400, 500],
            },
            index=pd.Index(timestamps, name="timestamp"),
        )

        expiry_dict = {
            pd.Timestamp(f"2023-01-{i:02d}"): {
                "near_month": pd.Timestamp("2023-01-15"),
                "next_month": pd.Timestamp("2023-02-15"),
            }
            for i in range(1, 6)
        }

        # Test 1: Empty corpact_dividend_info
        empty_corpact = pd.DataFrame(columns=["adj_factor", "exdate"])
        result_empty_corpact = operator_obj.apply_operation(
            operation_name=Operation.MODIFY,
            symbol="20230115001",
            universe_name="test_universe",
            universe_df=data.copy(),
            corpact_dividend_info=empty_corpact,
            expiry_dict=expiry_dict,
        )
        # Should return original data unchanged
        assert result_empty_corpact.equals(data)

        # Test 2: Invalid corpact_dividend_info structure (no adj_factor)
        invalid_corpact = pd.DataFrame(
            {
                "invalid_col": [1.02, 1.01],
                "exdate": [pd.Timestamp("2023-01-10"), pd.Timestamp("2023-01-12")],
            },
            index=pd.Index(
                [pd.Timestamp("2023-01-02"), pd.Timestamp("2023-01-04")],
                name="ann_date",
            ),
        )

        with pytest.raises(KeyError) as exception_info:
            operator_obj.apply_operation(
                operation_name=Operation.MODIFY,
                symbol="20230115001",
                universe_name="test_universe",
                universe_df=data.copy(),
                corpact_dividend_info=invalid_corpact,
                expiry_dict=expiry_dict,
            )
        assert "adj_factor" in str(exception_info.value)

        # Test 3: Data with no matching expiry conditions
        future_expiry_data = pd.DataFrame(
            {
                "ID": [20250115001] * 5,  # Future expiry that won't match conditions
                "Open": [100.0, 200.0, 300.0, 400.0, 500.0],
                "High": [110.0, 210.0, 310.0, 410.0, 510.0],
                "Low": [90.0, 190.0, 290.0, 390.0, 490.0],
                "Close": [105.0, 205.0, 305.0, 405.0, 505.0],
                "Cons_Volume": [100, 200, 300, 400, 500],
            },
            index=pd.Index(timestamps, name="timestamp"),
        )

        corpact_dividend_info = pd.DataFrame(
            {
                "adj_factor": [1.02, 1.01],
                "exdate": [pd.Timestamp("2023-01-10"), pd.Timestamp("2023-01-12")],
            },
            index=pd.Index(
                [pd.Timestamp("2023-01-02"), pd.Timestamp("2023-01-04")],
                name="ann_date",
            ),
        )

        result_no_match = operator_obj.apply_operation(
            operation_name=Operation.MODIFY,
            symbol="20250115001",
            universe_name="test_universe",
            universe_df=future_expiry_data.copy(),
            corpact_dividend_info=corpact_dividend_info,
            expiry_dict=expiry_dict,
        )

        # Should return data with minimal changes (no dividend adjustments applied)
        assert len(result_no_match) == len(future_expiry_data)
