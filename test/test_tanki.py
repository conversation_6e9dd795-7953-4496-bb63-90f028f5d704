from io import BytesIO
import sys
from unittest.mock import MagicMock

import numpy as np
from mock import patch  # type: ignore
import pandas as pd
import pytest

from main.tanki import Tanki
from main.library import Library
from main.enums import StorageType


@pytest.fixture(scope="class")
def tanki_obj():
    tanki = Tanki(exchange_type="nse")
    tanki._Tanki__logged_in = True
    tanki._Tanki__config.ALLOWED_SUDDEN_JUMP = 2.25
    tanki._Tanki__config.DATE_TODAY = pd.Timestamp("2024-02-06")

    df = pd.read_parquet("./test/utility/test_data/checker_test_data.parquet")
    user_test_data_df = pd.read_parquet(
        "./test/utility/test_data/user_test_data.parquet"
    )
    all_dates_bytes = b""
    try:
        with open(
            "./test/utility/test_data/minio_all_dates_test_data.npy", "rb"
        ) as file:
            all_dates_bytes = file.read()
    except Exception:
        raise Exception("Error in reading ALL_DATES")

    ALL_DATES = np.load(BytesIO(all_dates_bytes), allow_pickle=True)

    def mock_list_file_locations(*args, **kwargs):
        return ["nse/5_min/opt/trd", "nse/5_min/opt/ord"]

    def mock_checker_read(*args, **kwargs):
        if kwargs["file_name"] == "user_info":
            return user_test_data_df
        if (
            kwargs["storage_type"] == StorageType.FILE
            and kwargs["file_location"] == "commondata"
            and kwargs["file_name"] == "balte_uploads/ALL_DATES.npy"
        ):
            return ALL_DATES
        return df

    def mock_checker_read_metadata(*args, **kwargs):
        metadata_dict = {}
        for column in [
            "Open",
            "High",
            "Low",
            "Close",
            "Cons_Volume",
            "OI",
            "Open_int",
            "Vwap",
            "iv",
            "delta",
            "gamma",
            "theta",
            "vega",
        ]:
            metadata_dict[column] = {
                "nan_ratio": 0.2,
                "intraday_jump": 0.0,
                "overnight_open_jump": 0.0,
                "overnight_close_jump": 0.0,
            }

        metadata_dict["start_timestamp"] = metadata_dict[
            "last_timestamp"
        ] = "2024-02-06"

        return metadata_dict

    def mock_check_overnight_sudden_jumps(*args, **kwargs):
        return "check_overnight_sudden_jumps: Passed \n"

    patch1 = patch("main.data.auditor.Auditor.read", mock_checker_read)
    patch2 = patch(
        "main.data.auditor.Auditor.read_metadata", mock_checker_read_metadata
    )
    patch3 = patch(
        "main.data.checker.Checker.check_overnight_sudden_jumps",
        mock_check_overnight_sudden_jumps,
    )
    patch4 = patch("main.library.Library.list_file_locations", mock_list_file_locations)

    with patch1, patch2, patch3, patch4:
        yield tanki


@pytest.mark.usefixtures("tanki_obj")
class TestTanki:
    def test_tanki_getitem_library_absent_success(self, tanki_obj: Tanki):
        assert isinstance(tanki_obj["nse/5_min/opt/trd"], Library)

    def test_tanki_getitem_login_raise_exception(self, tanki_obj: Tanki):
        tanki_obj._Tanki__logged_in = False
        expected_message = "Permission denied: user not logged in"
        with pytest.raises(Exception) as exception_info:
            tanki_obj["another_library"]
        tanki_obj._Tanki__logged_in = True
        assert str(exception_info.value) == expected_message

    def test_tanki_list_libraries_success(self, tanki_obj: Tanki):
        tanki_obj._Tanki__library_list[
            tanki_obj._Tanki__vault["metadata_library"]
        ] = Library(
            config=tanki_obj._Tanki__config,
            lib_name=tanki_obj._Tanki__vault["metadata_library"],
        )
        expected_libraries = ["nse/5_min/opt/trd", "nse/5_min/opt/ord"]
        output_libraries = tanki_obj.list_libraries()
        del tanki_obj._Tanki__library_list[tanki_obj._Tanki__vault["metadata_library"]]
        assert output_libraries == expected_libraries

    def test_tanki_get_read_permissions_success(self, tanki_obj: Tanki):
        user_test_data = pd.read_parquet(
            "./test/utility/test_data/user_test_data.parquet"
        )
        tanki_obj._Tanki__user_data = user_test_data[
            user_test_data.username == "mantraraj"
        ]
        expected_read_permissions = [
            "library",
            "nse/5_min/opt/ord",
            "nse/5_min/opt/trd",
        ]
        output_read_permissions = tanki_obj.get_read_permissions()
        assert output_read_permissions == expected_read_permissions

    def test_tanki_get_read_permissions_logged_in_false_fails(sef, tanki_obj: Tanki):
        tanki_obj._Tanki__logged_in = False
        expected_message = "Permission denied: user not logged in"
        with pytest.raises(Exception) as exception_info:
            tanki_obj.get_read_permissions()
        tanki_obj._Tanki__logged_in = True
        assert str(exception_info.value) == expected_message

    def test_tanki_get_read_permissions_user_data_none_fails(sef, tanki_obj: Tanki):
        tanki_obj._Tanki__user_data = None
        expected_message = "UserInfoError: user data not found"
        with pytest.raises(Exception) as exception_info:
            tanki_obj.get_read_permissions()
        assert str(exception_info.value) == expected_message

    def test_tanki_get_write_permissions_success(self, tanki_obj: Tanki):
        user_test_data = pd.read_parquet(
            "./test/utility/test_data/user_test_data.parquet"
        )
        tanki_obj._Tanki__user_data = user_test_data[
            user_test_data.username == "mantraraj"
        ]
        expected_write_permissions = [
            "library",
            "nse/5_min/opt/ord",
            "nse/5_min/opt/trd",
        ]
        output_write_permissions = tanki_obj.get_write_permissions()
        assert output_write_permissions == expected_write_permissions

    def test_tanki_get_write_permissions_logged_in_false_fails(sef, tanki_obj: Tanki):
        tanki_obj._Tanki__logged_in = False
        expected_message = "Permission denied: user not logged in"
        with pytest.raises(Exception) as exception_info:
            tanki_obj.get_write_permissions()
        tanki_obj._Tanki__logged_in = True
        assert str(exception_info.value) == expected_message

    def test_tanki_get_write_permissions_user_data_none_fails(sef, tanki_obj: Tanki):
        tanki_obj._Tanki__user_data = None
        expected_message = "UserInfoError: user data not found"
        with pytest.raises(Exception) as exception_info:
            tanki_obj.get_write_permissions()
        assert str(exception_info.value) == expected_message

    def test_tanki_check_data_all_checks_called(self, tanki_obj: Tanki):
        output_message = tanki_obj.check_data("db", "nse/5_min/opt/trd", "symbol")
        for check in tanki_obj._Tanki__checker.checks:
            assert check.value in output_message

    def test_check_data_with_optional_parameters(self, tanki_obj: Tanki):
        with patch("main.data.checker.Checker.check_data") as mock_check_data:
            mock_check_data.return_value = ""

            data = pd.DataFrame({"col1": [1, 2, 3]})
            check_list = ["check_columns_set", "check_nan_entries"]
            ignore_checks = ["check_OHLC"]

            result = tanki_obj.check_data(
                storage_type="db",
                file_location="nse/5_min/opt/trd",
                file_name="symbol",
                data=data,
                check_list=check_list,
                ignore_checks=ignore_checks,
            )

            assert result == ""

            mock_check_data.assert_called_once_with(
                storage_type=StorageType.DB,
                file_location="nse/5_min/opt/trd",
                file_name="symbol",
                data=data,
                check_list=check_list,
                ignore_checks=ignore_checks,
            )

    def test_check_data_invalid_storage_type_raises_exception(self, tanki_obj: Tanki):
        expected_message = "Invalid storage type: invalid_storage"
        with pytest.raises(Exception) as exception_info:
            tanki_obj.check_data(
                storage_type="invalid_storage",
                file_location="nse/5_min/opt/trd",
                file_name="symbol",
            )
        assert str(exception_info.value) == expected_message

    def test_check_data_not_logged_in_raises_exception(self, tanki_obj: Tanki):
        tanki_obj._Tanki__logged_in = False

        expected_message = "Permission denied: user not logged in"
        with pytest.raises(Exception) as exception_info:
            tanki_obj.check_data(
                storage_type="db",
                file_location="nse/5_min/opt/trd",
                file_name="symbol",
            )

        # Resetting the logged_in status to True
        tanki_obj._Tanki__logged_in = True

        assert str(exception_info.value) == expected_message

    def test_login_success(self, tanki_obj: Tanki):
        tanki_obj._Tanki__logged_in = False
        username = "mantraraj"
        password = "mantraraj"
        tanki_obj.login(username=username, password=password)
        assert tanki_obj._Tanki__logged_in
        tanki_obj._Tanki__user_data = None
        del tanki_obj._Tanki__library_list[tanki_obj._Tanki__vault["metadata_library"]]

    def test_tanki_list_libraries_login_raises_exception(self, tanki_obj: Tanki):
        tanki_obj._Tanki__logged_in = False
        expected_message = "Permission denied: user not logged in"
        with pytest.raises(Exception) as exception_info:
            tanki_obj.list_libraries()
        tanki_obj._Tanki__logged_in = True
        assert str(exception_info.value) == expected_message

    def test_login_username_type_raises_exception(self, tanki_obj: Tanki):
        username = ["mantraraj"]
        password = "mantraraj"
        expected_message = f"{username} is not of type str"
        with pytest.raises(Exception) as exception_info:
            tanki_obj.login(username=username, password=password)
        assert str(exception_info.value) == expected_message

    def test_login_password_type_raises_exception(self, tanki_obj: Tanki):
        username = "mantraraj"
        password = ["mantraraj"]
        expected_message = f"{password} is not of type str"
        with pytest.raises(Exception) as exception_info:
            tanki_obj.login(username=username, password=password)
        assert str(exception_info.value) == expected_message

    def test_login_user_not_found_raises_exception(self, tanki_obj: Tanki):
        username = "user_name_not_exists"
        password = "user_name_not_exists"
        expected_message = f"User {username} does not exist. Please get it created"
        with pytest.raises(Exception) as exception_info:
            tanki_obj.login(username=username, password=password)
        assert str(exception_info.value) == expected_message

    def test_login_wrong_password_raises_exception(self, tanki_obj: Tanki):
        username = "mantraraj"
        password = "wrong_password"
        expected_message = f"Wrong password for {username}"
        with pytest.raises(Exception) as exception_info:
            tanki_obj.login(username=username, password=password)
        assert str(exception_info.value) == expected_message

    def test_compile_data_success(self, tanki_obj: Tanki):
        mock_compiler = MagicMock()
        mock_compiler.compile.return_value = "Compilation Success!!"

        with patch("main.tanki.Compiler", return_value=mock_compiler):
            universe_list = ["opt"]
            frequency = 5
            dtype = "trd"
            start_date = pd.Timestamp("2024-01-01")
            end_date = pd.Timestamp("2024-01-31")

            result = tanki_obj.compile_data(
                universe_list=universe_list,
                frequency=frequency,
                dtype=dtype,
                start_date=start_date,
                end_date=end_date,
            )

            assert result == "Compilation Success!!"

            mock_compiler.compile.assert_called_once_with(
                universe_list=universe_list,
                frequency=frequency,
                dtype=dtype,
                start_date=start_date,
                end_date=end_date,
            )
