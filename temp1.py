# import datetime
# from io import BytesIO
# import pickle
# import pandas as pd
# import numpy as np
# from typing import List, Optional, cast, Dict
# import sys

# sys.path.append("/home/<USER>/repos/data_auditing")
# from main.data.auditor import Auditor
# from main.enums import StorageType
# from main.decorators import load_data
# from main.config.config_NSE import ConfigNSE
# from main.enums import StorageType
# from arcticdb import Arctic

# store = Arctic("s3://*************:9000:arctic-db?access=super&secret=doopersecret")

# config = ConfigNSE()
# auditor = Auditor(config=config)


# def get_missing_for_docs(library):
#     library = library
#     library_split = library.split("/")
#     exchange = library_split[0]
#     frequency = library_split[1][0]
#     universe = library_split[2]
#     dtype = library_split[-1]

#     def missing_dates(
#         storage_type: StorageType,
#         file_location: str,
#         file_name: str,
#         data: pd.DataFrame,
#     ) -> str:
#         metadata = None

#         all_dates_object = auditor.read(
#             storage_type=StorageType.FILE,
#             file_location=config.FILE_DICT["ALL_DATES"][0],
#             file_name=config.FILE_DICT["ALL_DATES"][1],
#         )
#         all_dates_list = np.load(BytesIO(all_dates_object.data), allow_pickle=True)
#         ALL_DATES = pd.DataFrame(all_dates_list, columns=["ALL_DATES"])

#         # if len(data) == 0:
#         #     return set(ALL_DATES["ALL_DATES"].dt.date.values)

#         # if (metadata is None) or ("last_timestamp" not in metadata):
#         #     checked_messages += f"Metadata is none or doesn't have last_timestamp for {file_location} with symbol = {file_name}\n"

#         given_dates = set(pd.DatetimeIndex(data.index).date)
#         start_date = min(given_dates)
#         if (
#             (metadata is not None)
#             and ("last_timestamp" in metadata)
#             and (metadata["last_timestamp"].date() < start_date)
#         ):
#             start_date = metadata["last_timestamp"].date()
#             given_dates.add(start_date)
#         end_date = max(given_dates)

#         allowed_dates = set(
#             date
#             for date in ALL_DATES["ALL_DATES"].dt.date.values
#             if date >= start_date and date <= end_date
#         )

#         missing_dates = allowed_dates.difference(given_dates)
#         missing_dates = sorted(missing_dates)

#         return missing_dates

#         # missing_dates_df = pd.DataFrame(missing_dates, columns=["missing_dates"])

#         # missing_dates_df.to_csv(
#         #     f"/home/<USER>/repos/data_auditing/cash/missing_dates/{universe}/{dtype}/download_files/{file_name}_missing_dates.csv"
#         # )

#     def missing_times(
#         storage_type: StorageType,
#         file_location: str,
#         file_name: str,
#         data: pd.DataFrame,
#     ) -> str:
#         file_location_split = file_location.split("/")
#         frequency = file_location_split[1][0]
#         universe = file_location_split[2]

#         all_time_set = {
#             stamp.time()
#             for stamp in pd.date_range(
#                 start=config.MARKET_HOURS_DICT[universe]["open"],
#                 end=config.MARKET_HOURS_DICT[universe]["close"],
#                 freq=f"{frequency}T",
#                 inclusive="right",
#             )
#         }

#         date_wise_data_list = [
#             (date, group)
#             for date, group in data.groupby(pd.DatetimeIndex(data.index).date)
#         ]

#         missing_date_to_times = dict()
#         for date, date_wise_data in date_wise_data_list:
#             given_time_set = set(
#                 cast(datetime.time, cast(pd.DatetimeIndex, date_wise_data.index).time)
#             )
#             date_wise_missing_time_set = all_time_set - given_time_set

#             if len(date_wise_missing_time_set) > 0:
#                 if date not in missing_date_to_times:
#                     missing_date_to_times[date] = set()
#                 missing_date_to_times[date].update(date_wise_missing_time_set)

#         sorted_date_time_items = sorted(missing_date_to_times.items())
#         sorted_date_time_dict = {
#             date: sorted(times) for date, times in sorted_date_time_items
#         }
#         missing_date_to_times = sorted_date_time_dict

#         missing_date_to_times_df = pd.DataFrame()
#         missing_date_to_times_df["date"] = missing_date_to_times.keys()
#         missing_date_to_times_df["missing_times"] = list(missing_date_to_times.values())
#         missing_date_to_times_df.to_csv(
#             f"/home/<USER>/repos/data_auditing/docs/data_reports/{universe}/{dtype}/download_files/{file_name}_missing_times.csv"
#         )

#     lib = store["nse/1_min/cash/trd"]

#     # all_dates_object = auditor.read(
#     #         storage_type=StorageType.FILE,
#     #         file_location=config.FILE_DICT["ALL_DATES"][0],
#     #         file_name=config.FILE_DICT["ALL_DATES"][1],
#     #     )
#     # all_dates_list = np.load(BytesIO(all_dates_object.data), allow_pickle=True)

#     # all_missing_list = []
#     # for date in all_dates_list:
#     #     flag = True
#     #     for sym in ['1075', '1013', '484', '485']:
#     #         data = lib.read(sym, date_range=(date, date + pd.Timedelta(days=1))).data
#     #         if len(data) > 0:
#     #             flag = False
#     #     if flag:
#     #         all_missing_list.append(date)

#     missing_dates_list = {}
#     for sym in ["1075", "1013", "485"]:
#         data = lib.read(sym).data
#         missing_dates_list[sym] = missing_dates(
#             storage_type=StorageType.DB,
#             file_location="nse/1_min/cash/trd",
#             file_name=sym,
#             data=data,
#         )

#     with open("cash_1min_trd_missing_dates.pkl", "wb") as f:
#         pickle.dump(missing_dates_list, f)

#     # missing_times(
#     #     storage_type=StorageType.DB, file_location=library, file_name=sym, data=data
#     # )


# # for symbol in ["5002"]:
# # get_missing_for_docs(library="nse/1_min/cash/trd", symbol=str(symbol))

# # get_missing_for_docs(library="nse/1_min/cash/trd")

# print()


# import pandas as pd
# from main.data.checker import BhavcopyChecker
# from main.config.config_factory import ConfigFactory
# from arcticdb import Arctic
# from main.enums import StorageType

# config = ConfigFactory(exchange_type="nse")
# bhavcopyChecker = BhavcopyChecker(config=config)

# library = "nse/1440_min/after_market/trd"

# bhavcopyChecker.check_with_bhav(
#     storage_type=StorageType.DB, file_location=library, file_name="index_bhav"
# )


# import datetime
# import sys
# import pandas as pd
# from arcticdb import Arctic
# import pickle

# symbol_to_balte_id = {}
# with open("/media/hdd/jupyterdata/mantraraj/symbol_to_balte_id", "rb") as f:
#     symbol_to_balte_id = pickle.load(f)


# store = Arctic("s3://*************:9000:arctic-db?access=super&secret=doopersecret")
# lib = store["nse/1_min/raw_cash/trd"]
# libold = store["nse/1_min/raw_cash_old/trd"]

# already=lib.list_symbols()

# for sym in libold.list_symbols():
#     if sym in already:
#         continue
#     try:
#         dfold = libold.read(sym).data
#         dfnew = pd.read_parquet(
#             f"/media/hdd/jupyterdata/mantraraj/raw_cash_from_cash/{symbol_to_balte_id[sym]}.parquet"
#         )
#         dfnew = dfnew[dfnew.Close > 0]
#         dfnew = dfnew[dfnew.index.date < datetime.date(2024, 3, 12)]
#         dfnew["ID"] = sym
#         dfnew["ID"] = dfnew["ID"].astype("str")
#         dfnew = dfnew.rename(columns={"ID": "Symbol"})

#         df = pd.concat([dfold, dfnew])
#         df = df.reset_index()
#         df = df.drop_duplicates(subset=["timestamp"], keep="first")
#         df = df.set_index("timestamp").sort_index()

#         lib.write(sym, df)
#     except Exception as e:
#         print(f"Failed for the symbol: {sym} due to: {e}\n")
#         continue

# print()


# import pandas as pd
# from arcticdb import Arctic

# store = Arctic("s3://*************:9000:arctic-db?access=super&secret=doopersecret")

# lib = store['nse/1440_min/after_market/trd']

# dfsc = lib.read("symbol_change").data
# dfdm = lib.read("demerger_merger").data

# print()


# import os
# import pandas as pd
# import pickle

# base_path = "/home/<USER>/repos/data_auditing/raw_cash"
# already_paths = []
# base_tick_path = "/mnt/companydata/MarketData/eq/tick/root_trd"


# for sym in os.listdir(base_path):
#     for date_str in os.listdir(f"{base_path}/{sym}"):
#         already_paths.append(f"{base_tick_path}/{date_str[:-8]}/{sym}/XX/XX/{sym}.trd")

# with open("done_cash_paths_211", "wb") as f:
#     pickle.dump(already_paths, f)

# print()


## getting futures path

# from multiprocessing import Manager, Pool
# import os
# import pandas as pd
# import pickle
# import re


# def get_symbol_to_balte_id():
#     # get from balte and save it
#     with open("/media/hdd/jupyterdata/mantraraj/symbol_to_balte_id", "rb") as f:
#         return pickle.load(f)


# base_path_211 = "/mnt/companydata/MarketData/eq/tick/root_trd"
# starting_balte_id = 1
# ending_balte_id = 5000
# futures_type = "FF"
# symbol_to_balte_id = get_symbol_to_balte_id()
# pattern = re.compile(r"^[A-Z]+[0-9]{2}-[A-Za-z]{3}-[0-9]{4}\.trd$")

# dates_list = os.listdir(base_path_211)

# valid_dates_list = []
# invalid_dates_list = []

# for date in dates_list:
#     if len(date) == 8 and date.isdigit():
#         valid_dates_list.append(date)
#     else:
#         invalid_dates_list.append(date)

# print("\nGot date list...\n")

# # /mnt/companydata/MarketData/eq/tick/root_trd/01012018/ACC/22022018/FF/ACC22-Feb-2018.trd


# def get_sym_paths(date, shared_list):
#     path1 = f"{base_path_211}/{date}"
#     try:
#         for sym in os.listdir(path1):
#             path2 = f"{path1}/{sym}"
#             if (sym not in symbol_to_balte_id) or (
#                 symbol_to_balte_id[sym] >= starting_balte_id
#                 and symbol_to_balte_id[sym] <= ending_balte_id
#             ):
#                 for expiry in os.listdir(path2):
#                     path3 = f"{path2}/{expiry}"
#                     if len(expiry) == 8 and expiry.isdigit():
#                         path4 = f"{path3}/{futures_type}"
#                         if os.path.exists(path4):
#                             for contracts in os.listdir(path4):
#                                 if pattern.match(contracts):
#                                     shared_list.append(f"{path4}/{contracts}")

#     except Exception as e:
#         print(f"Failed for {date} due to: {e}\n")
#         return


# shared_list = Manager().list()

# print("Started getting all paths...\n")
# with Pool() as pool:
#     pool.starmap(get_sym_paths, [(date, shared_list) for date in valid_dates_list])
# print("Cpmpleted getting all paths\n")

# all_paths = list(shared_list)
# all_paths

# print("Storing all_paths list...\n")
# with open("fut_trd_all_paths", "wb") as f:
#     pickle.dump(all_paths, f)
# print("Storing all_paths list done\n")


import pandas as pd
import os
import datetime
import pickle
from arcticdb import Arctic

store = Arctic("s3://*************:9000:arctic-db?access=super&secret=doopersecret")
storek = Arctic("s3://*************:9000:kivi-arcticdb?access=super&secret=doopersecret")



# lib=storek['nse/1_min/cash/trd']
# for sym in lib.list_symbols():
#     if os.path.exists(f"/home/<USER>/repos/data_auditing/backup_cash_1min_trd/{sym}.parquet"):
#         continue
#     obj=lib.read(sym)
#     df=obj.data
#     meta=obj.metadata
#     if df is None: continue
    
#     with open(f"/home/<USER>/repos/data_auditing/backup_cash_1min_trd_metadata/{sym}", "wb") as f:
#         pickle.dump(meta, f)
#     df.to_parquet(f"/home/<USER>/repos/data_auditing/backup_cash_1min_trd/{sym}.parquet")
#     print(f"Done for {sym}")


print()
# library = "nse/1_min/cash/trd"
# lib = store[library]

# base_path = "/home/<USER>/repos/data_auditing/raw_cash"
# for sym in os.listdir(base_path):
#     df = pd.read_parquet(f"{base_path}/{sym}/combined.parquet")
#     lib.write(sym, df)


# libf=store['nse/1_min/fut/trd']
# librf=store['nse/1_min/raw_fut/trd']
# symbol_to_balte_id={}
# with open("/home/<USER>/repos/data_auditing/symbol_to_balte_id", "rb") as f:
#     symbol_to_balte_id=pickle.load(f)
# balte_id_to_symbol={}
# for k, v in symbol_to_balte_id.items():
#     balte_id_to_symbol[v]=k
# syms=os.listdir("/media/hdd/jupyterdata/mantraraj/fut_1min_trd_old_arctic")


# diff_percent = {}
# threshold = 0.1
# len_diff = {}

# for sym in ['1075']:
#     dfn=libf.read(sym).data
#     if f"{sym}.parquet" in syms:
#         dfo=pd.read_parquet(f"/media/hdd/jupyterdata/mantraraj/fut_1min_trd_old_arctic/{sym}.parquet")
#         dfo=dfo[dfo.index.date<datetime.date(2024,9,1)]
#         dfo=dfo[~dfo.Close.isna()]
#         dfn=dfn[~dfn.Close.isna()]

#         len_diff[sym] = len(dfn)-len(dfo)
#     else:
#         len_diff[sym] = len(dfn)

#     if len_diff[sym] < 0:
#         symbol=balte_id_to_symbol[int(sym)]
#         dfr=librf.read(symbol).data
#         diff_list=list(set(dfo.index)-set(dfn.index))
#         dfr=dfr[dfr.index.isin(diff_list)]

#         diff_list=list(set(diff_list)-set(dfr.index))

#         dfof=dfo[dfo.index.isin(diff_list)]
#         if (dfof.Open!=dfof.Close).any() or (dfof.Open!=dfof.High).any() or (dfof.Open!=dfof.Low).any() or (dfof.Close!=dfof.High).any() or (dfof.Close!=dfof.Low).any() or (dfof.High!=dfof.Low).any():
#             pass


#         diff_list_f = [ts for ts in diff_list if ts >= pd.Timestamp(2022,6,6)]

#         if len(dfr) < len(diff_list):
#             pass

#         # merged_df = pd.merge(dfn, dfo, on=["timestamp"], how="inner")
#         # bool_arr = None

#         # for col in ["Open", "High", "Low", "Close", "Cons_Volume"]:
#         #     merged_df[f"{col}_diff"]=abs(merged_df[f"{col}_x"] - merged_df[f"{col}_y"])/merged_df[[f"{col}_x", f"{col}_y"]].min(axis=1)
#         #     if bool_arr is None:
#         #         bool_arr = merged_df[f"{col}_diff"] > threshold
#         #     else:
#         #         bool_arr |= merged_df[f"{col}_diff"] > threshold

#         # cdiffp=bool_arr.sum() / len(merged_df) * 100
#         # if cdiffp:
#         #     diff_percent[sym] = cdiffp

# with open(f"fut_old_vs_new_len_diff", "wb") as f:
#     pickle.dump(len_diff, f)

print()


import datetime
import pandas as pd
from multiprocessing import Pool
from main.data.auditor import Auditor
from main.data.operation import Operator
from main.config.config_NSE import ConfigNSE
from main.enums import StorageType
from main.data.compiler import Compiler
import time
import os


def resample():
    opt = Operator(config=ConfigNSE())
    aud = Auditor(config=ConfigNSE())

    # if ("1min_cash_trd" in symbol) and ("raw" not in symbol):
    for sym in aud.list_file_names(
        storage_type=StorageType.DB, file_location="nse/1_min/cash/trd"
    ):
        one_min_data = aud.read(
            storage_type=StorageType.DB,
            file_location="nse/1_min/cash/trd",
            file_name=sym,
        )
        if len(one_min_data) > 0:
            five_min_data = opt.resample_data_1_min_to_5_min(
                universe="cash", data=one_min_data
            )
        else:
            five_min_data = one_min_data

        five_min_data = five_min_data[
            (five_min_data.index.time > datetime.time(9, 15))
            & (five_min_data.index.time <= datetime.time(15, 30))
        ]

        print(f"{sym} resampling done!!")
        aud.write(
            storage_type=StorageType.DB,
            file_location="nse/5_min/cash/trd",
            file_name=sym,
            data=five_min_data,
            metadata_dict={},
        )


if __name__ == "__main__":
    args = []

    # print("Started resampling...")
    # resample()
    # print("Done :-)")


# import pandas as pd
# import os
# from arcticdb import Arctic

# store=Arctic("s3://*************:9000:arctic-db?access=super&secret=doopersecret")
# library="nse/1_min/raw_fut/trd"
# lib=store[library]

# base_path="/home/<USER>/repos/data_auditing/raw_fut_from_tick_on_20240312"

# symbols=lib.list_symbols()

# for sym in os.listdir(base_path):
#     sym='SBIN_combined.parquet'
#     dfold=pd.DataFrame()
#     symbol=sym.split("_")[0]
#     if symbol in symbols:
#         dfold=lib.read(symbol).data
#     df=pd.read_parquet(f"{base_path}/{sym}")
#     df['expiry']=pd.to_datetime(df.expiry)
#     if len(dfold)>0:
#         df=pd.concat([dfold,df])
#         df=df.reset_index()
#         df=df.drop_duplicates(subset=['timestamp', 'expiry'], keep='first')
#         df=df.set_index('timestamp').sort_index()
#     lib.write(symbol, df)

# print()


# import pandas as pd
# import os
# from arcticdb import Arctic

# store = Arctic("s3://*************:9000:arctic-db?access=super&secret=doopersecret")
# library = "nse/1_min/raw_fut/trd"
# libf = store[library]
# libfo = store[library.replace("raw_fut", "raw_fut_old_20240901")]
# syms = libf.list_symbols()

# for sym in os.listdir("/home/<USER>/repos/data_auditing/final_raw_fut_tick_20240901"):
#     if sym in syms:
#         dfold=libf.read(sym).data
#     else:
#         dfold=pd.DataFrame()

#     df=pd.read_parquet(f"/home/<USER>/repos/data_auditing/final_raw_fut_tick_20240901/{sym}/combined.parquet")
#     df=pd.concat([dfold,df])
#     df=df.reset_index()
#     df=df.drop_duplicates(subset=['timestamp','expiry']).set_index('timestamp')
#     df=df.sort_index()

#     libf.write(sym, df)


# print("Done!!")


# from multiprocessing import Pool
# import pickle
# import time
# from arcticdb import Arctic
# from minio import Minio
# import pandas as pd
# import numpy as np
# import datetime
# import os

# store = Arctic("s3://*************:9000:arctic-db?access=super&secret=doopersecret")
# librf = store["nse/1_min/raw_fut/trd"]

# MINIO_END_POINT = "*************:11009"
# MINIO_ACCESS_KEY = "minioreader"
# MINIO_SECRET_KEY = "reasersecret"

# fstore = Minio(MINIO_END_POINT, MINIO_ACCESS_KEY, MINIO_SECRET_KEY, secure=False)

# balte_id_to_symbol={}
# with open("/media/hdd/jupyterdata/mantraraj/balte_id_to_symbol", "rb") as f:
#     balte_id_to_symbol=pickle.load(f)


# symbol_to_balte_id={}
# with open("/media/hdd/jupyterdata/mantraraj/symbol_to_balte_id", "rb") as f:
#     symbol_to_balte_id=pickle.load(f)

# optstk_expiry_dict = pickle.loads(
#     fstore.get_object("commondata", "balte_uploads/optstk_expiry_dict").data
# )
# optstk_expiry_df = pd.DataFrame.from_dict(optstk_expiry_dict, orient="index")[
#     ["near_month", "next_month"]
# ]
# optstk_expiry_df["ID"] = "GEN_STOCKS"
# optstk_expiry_df.index.name = "date"
# optstk_expiry_df = optstk_expiry_df[["ID", "near_month", "next_month"]]
# expiry_df = optstk_expiry_df

# expiry_df["near_month"]=expiry_df["near_month"].replace(pd.Timestamp(2008, 12, 25), pd.Timestamp(2008, 12, 24))
# expiry_df["next_month"]=expiry_df["next_month"].replace(pd.Timestamp(2008, 12, 25), pd.Timestamp(2008, 12, 24))
# expiry_df["near_month"]=expiry_df["near_month"].replace(pd.Timestamp(2008, 4, 30), pd.Timestamp(2008, 4, 29))
# expiry_df["next_month"]=expiry_df["next_month"].replace(pd.Timestamp(2008, 4, 30), pd.Timestamp(2008, 4, 29))


# year_month_expiry = {}
# for dt, row in expiry_df.iterrows():
#     year1=row['near_month'].year
#     month1=row["near_month"].month
#     year2=row['next_month'].year
#     month2=row['next_month'].month
#     if (year1, month1) not in year_month_expiry:
#         year_month_expiry[(year1, month1)] = {row['near_month']}
#     else:
#         year_month_expiry[(year1, month1)].add(row['near_month'])
#     if (year2, month2) not in year_month_expiry:
#         year_month_expiry[(year2, month2)] = {row['next_month']}
#     else:
#         year_month_expiry[(year2, month2)].add(row['next_month'])


# for k, v in year_month_expiry.items():
#     if len(v) > 1:
#         print(k, v)

# year_month_expiry_without_set = {}
# for k, v in year_month_expiry.items():
#     year_month_expiry_without_set[k] = v.pop()

# for sym in os.listdir("/media/hdd/jupyterdata/mantraraj/raw_fut_from_fut_raw"):
#     if sym[0]=='.': continue
#     # if sym[:-8] in symbol_to_balte_id: continue
#     # df=pd.read_parquet(f"/media/hdd/jupyterdata/mantraraj/raw_fut_from_fut_raw/{sym}")
#     # symbol=sym[:-8]
#     # if int(symbol) not in balte_id_to_symbol: continue

#     # df['year_month']=list(zip(df.index.year, df.index.month))
#     # df['expiry'] = df['year_month'].map(year_month_expiry_without_set)
#     # df=df.drop(columns=['year_month'])
#     # df["Vwap"]=np.nan
#     # df=df[~df.Close.isna()]
#     # df=df[['ID','expiry', "Open", "High", "Low", "Close", "Vwap", "Cons_Volume"]]
#     # df["ID"]=df["ID"].map(balte_id_to_symbol)
#     # df.to_parquet(f"/media/hdd/jupyterdata/mantraraj/raw_fut_from_fut_raw/{balte_id_to_symbol[int(symbol)]}.parquet")
#     # os.remove(f"/media/hdd/jupyterdata/mantraraj/raw_fut_from_fut_raw/{sym}")
#     # print()


# # for sym in librf.list_symbols():
# #     df = librf.read(sym).data
# #     df['year_month']=list(zip(df['expiry'].dt.year, df['expiry'].dt.month))
# #     df['nexpiry'] = df['year_month'].map(year_month_expiry_without_set)
# #     df=df.drop(columns=['expiry', 'year_month'])
# #     df=df.rename(columns={"nexpiry":"expiry"})
# #     df=df[['ID','expiry', "Open", "High", "Low", "Close", "Vwap", "Cons_Volume"]]

# #     librf.write(sym, df)


# print()


## addressing prem comments on optstk data

# lib=store['nse/1_min/optstk/trd']

# print()
DATA_TYPES = {
    "ID": "uint64",
    "Open": "float32",
    "Open_int": "float32",
    "High": "float32",
    "Low": "float32",
    "Close": "float32",
    "iv": "float32",
    "gamma": "float64",
    "theta": "float64",
    "vega": "float64",
    "Vwap": "float64",
    "Cons_Volume": "float64",
    "Next_Cons_Volume": "float64",
    "expiry_date": "int32",
    "option_type": "int16",
    "option": "int64",
    "strike": "float32",
    "sym_id": "int32",
    "slippage": "float32",
    "return": "float32",
    "nw": "float32",
    "dayT": "float32",
    "pcr": "float32",
    "actual_date": "datetime64[ns]",
    "expiry": "datetime64[ns]",
    "OI": "float64",
    "sector": "object",
    "Close_wgts": "float64",
    "vol_wgts": "float64",
    "Client": "float64",
    "DII": "float64",
    "FII": "float64",
    "Pro": "float64",
    "Total": "float64",
    "segment_type": "object",
    "long_margin": "float64",
    "short_margin": "float64",
    "segment": "object",
    "buy_contract": "float64",
    "buy_value": "float64",
    "sell_contract": "float64",
    "sell_value": "float64",
    "oi_contract": "float64",
    "oi_value": "float64",
    "span": "float32",
    "exposure": "float32",
    "fii_grosspurchase": "float64",
    "fii_grosssales": "float64",
    "fii_net": "float64",
    "dii_grosspurchase": "float64",
    "dii_grosssales": "float64",
    "dii_net": "float64",
    "contract_price": "float64",
    "delta": "float64",
    "R1": "float64",
    "R2": "float64",
    "R3": "float64",
    "R4": "float64",
    "R5": "float64",
    "R6": "float64",
    "R7": "float64",
    "R8": "float64",
    "R9": "float64",
    "R10": "float64",
    "R11": "float64",
    "R12": "float64",
    "R13": "float64",
    "R14": "float64",
    "R15": "float64",
    "R16": "float64",
    "near_month": "int32",
    "next_month": "int32",
    "near_week": "int32",
    "next_week": "int32",
}


def column_bucket(universe: str, frequency: int, dtype: str):
    print(f"Started column bucket for {universe} {frequency}")
    opt = Operator(config=ConfigNSE())
    col_dict_list = {}
    base_path = f"/media/hdd/jupyterdata/mantraraj"

    for sym in ["7011", "7012"]:
        if sym[0] == ".":
            continue

        trd_data = pd.read_parquet(f"{base_path}/{universe}_{sym}.parquet")

        if len(trd_data) == 0:
            continue

        if "Symbol" in trd_data.columns:
            trd_data = trd_data.drop(columns=["Symbol"])
            trd_data.to_parquet(f"{base_path}/{sym}")

        col_dict = opt.create_column_bucket(universe=universe, data=trd_data)

        for col, df in col_dict.items():
            if col in col_dict_list:
                col_dict_list[col].append(df)
            else:
                col_dict_list[col] = [df]

    for col in col_dict_list.keys():
        df = pd.concat(col_dict_list[col])
        df = df.sort_index()
        df.columns.name = None
        for c in df:
            df[c] = df[c].astype(DATA_TYPES[col])

        print(f"{col} column bucket done!!")
        df.to_parquet(f"{base_path}/{universe}_cb_{col}.parquet")


import pandas as pd
import datetime

from main.data.compiler import Compiler
from main.config.config_NSE import ConfigNSE

import numpy as np
from minio import Minio
from io import BytesIO


config = ConfigNSE()

compiler = Compiler(config=config)


print("Started fut raw compilation...")


# compiler.compile(
#     universe_list=["optstk_unadjusted_spot"],
#     frequency=1,
#     dtype="trd",
#     start_date=datetime.date(2007, 1, 1),
#     end_date=datetime.date(2025, 2, 27),
# )

print("Compilation done!")


print()


print()


# from main.daily_appends import DailyAppends

# dp = DailyAppends(exchange_type="nse")
# dp.apply_symbol_change_on_demerger_merger()


print()

# lib = store["nse/1_min/raw_fut/trd"]
# syms = lib.list_symbols()

# for sym in os.listdir(
#     "/home/<USER>/repos/data_auditing/raw_fut_from_tick_on_20240312"
# ):
#     if sym[0] == ".":
#         continue
#     df = pd.read_parquet(
#         f"/home/<USER>/repos/data_auditing/raw_fut_from_tick_on_20240312/{sym}"
#     )
#     df['expiry']=pd.to_datetime(df.expiry, format="%d-%b-%Y")
#     symbol = sym.split("_")[0]

#     if symbol not in syms:
#         lib.write(symbol, df)
#         print(f"Written {symbol}")

#     dfo = lib.read(symbol).data
#     dff = pd.concat([dfo, df])
#     dff = dff.reset_index()
#     dff = (
#         dff.drop_duplicates(subset=["timestamp", "expiry"], keep="first")
#         .set_index("timestamp")
#         .sort_index()
#     )
#     dff = dff[dff.index.date < datetime.date(2024, 9, 1)]
#     lib.write(symbol, dff)
#     print(f"Added {symbol}")


# syms1 = set(os.listdir("raw_fut_tick_20240901"))
# syms2 = set([sym.split("_")[0] for sym in os.listdir("/home/<USER>/repos/data_auditing/raw_fut_tick_20240901_20250213")])
# syms = syms1 | syms2

# for sym in syms:
#     if sym[0] == ".": continue

#     dfs = []

#     if sym in syms1:
#         dfs.append(pd.read_parquet(f"raw_fut_tick_20240901/{sym}"))

#     if sym in syms2:
#         dfs.append(pd.read_parquet(f"/home/<USER>/repos/data_auditing/raw_fut_tick_20240901_20250213/{sym}_combined.parquet"))

#     if dfs:
#         combined_df = pd.concat(dfs)

#         combined_df = combined_df.reset_index()

#         combined_df = combined_df.drop_duplicates(["timestamp", "expiry"]).set_index('timestamp').sort_index()

#         combined_df.to_parquet(f"raw_fut_tick_20250214/{sym.split('.')[0]}.parquet")

#         print(f"Combined and saved: {sym}")


# basis_df = pd.read_csv(
#             "http://*************:8000/mcx_cost.csv",
#             parse_dates=[
#                 "near_expiry",
#                 "next_expiry",
#                 "psuedo_expiry",
#                 "next_psuedo_expiry",
#             ],
#         )


# all_cont = os.listdir("/home/<USER>/scripts/sampler_dump_opt")
# lib=store['nse/1_min/raw_opt/trd']


# for sym in ["_NIFTY_", "_BANKNIFTY_", "_FINNIFTY_", "_NIFTYNXT50_", "_MIDCPNIFTY_"]:
#     cont = [cnt for cnt in all_cont if sym in cnt]
#     df_list = []
#     for cnt in cont:
#         df_list.append(pd.read_parquet(F"/home/<USER>/scripts/sampler_dump_opt/{cnt}"))
#     df=pd.concat(df_list)
#     df=df.drop(columns=['ID'])
#     df=df[['']]

#     symbol=sym.split("_")[1]
#     dfo=lib.read(symbol).data

#     dff=pd.concat([dfo,df]).reset_index()
#     dff=dff.drop_duplicates(subset=['timestamp','ID']).set_index('timestamp').sort_index()

#     # lib.write(symbol,dff)

#     print(F"Done for {symbol}")


# lib=store['nse/1_min/raw_optstk/ord']

# for sym in lib.list_symbols():
#     lib.delete(sym)

# with open("/home/<USER>/repos/data_auditing/cash_metadata_till_20250121/balte_id_to_symbol", "rb") as f:
#     bid=pickle.load(f)

# # bid={}
# # for k,v in sid.items():
# #     bid[v]=k

# failed=[]

# for sym in os.listdir("/media/hdd/jupyterdata/mantraraj/raw_optstk_ord_from_sampler_dump"):
#     if sym in ["5001", "5002", "5003", "5004", "5008"]: continue
#     try:
#         print(F"Started for {sym}")
#         if sym[0] == ".": continue
#         df_list = []
#         for cont in os.listdir(f"/media/hdd/jupyterdata/mantraraj/raw_optstk_ord_from_sampler_dump/{sym}"):
#             if cont[0]==".": continue
#             df_list.append(pd.read_parquet(f"/media/hdd/jupyterdata/mantraraj/raw_optstk_ord_from_sampler_dump/{sym}/{cont}"))

#         df=pd.concat(df_list).sort_index()
#         df['ID']=df['ID'].astype(str)

#         if len(sym) == 1:
#             df['ID'] = df["ID"].str[:-1] + "000" + sym
#         elif len(sym) == 2:
#             df['ID'] = df["ID"].str[:-2] + "00" + sym
#         elif len(sym) == 3:
#             df['ID'] = df["ID"].str[:-3] + "0" + sym
#         df["expiry"] = pd.to_datetime("20" + df["ID"].str[-11:-5], format="%Y%m%d")
#         df["option_type"] = df["ID"].str[-5:-4]
#         df["strike"] = df["ID"].str[:-11]
#         df = df[df.columns]
#         df["ID"] = df["ID"].str[-4:]
#         df["ID"] = df["ID"].astype(int).map(bid)
#         df=df[['ID','expiry','option_type','strike','Open','High','Low','Close']]
#         float_cols = ["Open", "High", "Low", "Close"]
#         df[float_cols] = df[float_cols].astype("float64")
#         df["strike"] = df["strike"].astype(int)
#         df["option_type"] = df["option_type"].astype(int)

#         symbol=bid[int(sym)]
#         lib.write(symbol, df)

#         print(F"Done for {symbol}")

#     except Exception as e:
#         print(F"Failed for {sym} due to {e}")
#         failed.append(sym)

# print(failed)
# print()


# libr = store["nse/1_min/opt/ord"]

# bid = {}
# with open(
#     "/home/<USER>/repos/data_auditing/cash_metadata_till_20250121/balte_id_to_symbol",
#     "rb",
# ) as f:
#     bid = pickle.load(f)

# for sym in ["5008", "5002", "5003", "5004", "5001"]:
#     print(F"Started for {sym}")
#     df_list = []
#     for date in os.listdir(
#         f"/media/hdd/jupyterdata/mantraraj/raw_optstk_ord_from_sampler_dump/{sym}"
#     ):
#         df_list.append(
#             pd.read_parquet(
#                 f"/media/hdd/jupyterdata/mantraraj/raw_optstk_ord_from_sampler_dump/{sym}/{date}"
#             )
#         )
#     df = pd.concat(df_list).sort_index()
#     df_list = []

#     for col in df.columns:
#         if col != "ID":
#             df[col] = df[col].astype("float64")
#     df["ID"] = ((((df["ID"] // int(1e13)) * 100) + 20) * int(1e11)) + (
#         df["ID"] % int(1e11)
#     )
#     df["ID"] = df["ID"].astype("uint64")

#     libr.write(sym, df)
#     df = pd.DataFrame()
#     print(F"Done for {sym}")


# lib = store["nse/1_min/snap_file/trd_ord"]
# syms_done = lib.list_symbols()
# failed = []

# for sym in os.listdir("/home/<USER>/repos/data_auditing/raw_data_from_snap_file_part2"):
#     try:
#         if ".parquet" not in sym:
#             continue
#         print(f"Started for {sym}")
#         # symbol = sym.split(".")[0]
#         symbol = sym.split("_")[0]
#         if "_FF" in symbol:
#             symbol = symbol.split("_")[0]
#         df = pd.read_parquet(f"/home/<USER>/repos/data_auditing/raw_data_from_snap_file_part2/{sym}")

#         lib.append(symbol, df)
#         os.remove(f"/home/<USER>/repos/data_auditing/raw_data_from_snap_file_part2/{sym}")
#         print(f"Done for {sym}")
#     except Exception as e:
#         try:
#             cols = lib.get_description(symbol).columns
#             cols = [col.name for col in cols]
#             df = df[cols]
#             lib.append(symbol, df)
#             os.remove(f"/home/<USER>/repos/data_auditing/raw_data_from_snap_file_part2/{sym}")
#             print(f"Done for {sym}")
#         except Exception as e:
#             failed.append(f"Failed for {sym} due to {e}\n")

# print(failed)


import os
import pandas as pd


def combine_snap(sym):
    if ".parquet" in sym:
        return

    try:
        print(f"Started for {sym}")
        df_list = []

        # Get the list of files in the directory
        files = os.listdir(
            f"/home/<USER>/repos/data_auditing/raw_data_from_snap_file_part2/{sym}"
        )
        tot_ct = len(files)

        # Calculate the number of files per segment
        segment_size = tot_ct // 1
        remainder = tot_ct % 1

        ct = 0
        segment_num = 1

        for cont in files:
            # Read the Parquet file and append to the list
            df_list.append(
                pd.read_parquet(
                    f"/home/<USER>/repos/data_auditing/raw_data_from_snap_file_part2/{sym}/{cont}"
                )
            )
            ct += 1

            # Check if the current segment is complete
            if ct == segment_size + (1 if segment_num <= remainder else 0):
                # Concatenate the DataFrames in the list
                df = pd.concat(df_list)
                df_list = []  # Reset the list for the next segment

                # Save the segment to a Parquet file
                df.to_parquet(
                    f"/home/<USER>/repos/data_auditing/raw_data_from_snap_file_part2/{sym}_{segment_num}.parquet"
                )
                df = pd.DataFrame()  # Reset the DataFrame

                print(f"Done segment {segment_num} for {sym}")

                # Reset counters for the next segment
                ct = 0
                segment_num += 1

        print(f"Done for {sym}")
    except Exception as e:
        print(f"Failed for {sym} due to {e}")
        return


# syms = set(os.listdir("/home/<USER>/repos/data_auditing/raw_data_from_snap_file_part2")) - {"NIFTY", "BANKNIFTY"}

# with Pool(7) as pool:
#     pool.map(combine_snap, syms)

# Call the function for the required symbols
# combine_snap("NIFTY")
# combine_snap("BANKNIFTY")

print()


from multiprocessing import Pool
import os
import shutil
import pandas as pd
from arcticdb import Arctic

store = Arctic("s3://*************:9000:arctic-db?access=super&secret=doopersecret")

# store.create_library("nse/1_min/snap_file_jan20_may20/trd_ord")
# lib = store["nse/1_min/snap_file_jan20_may20/trd_ord"]
lib = store["nse/1_min/snap_file_jan21_apr21/trd_ord"]


def combine_store_and_delete_snap(sym):
    if ".parquet" in sym:
        return
    try:
        print(f"Started for {sym}")
        df_list = []

        # Get the list of files in the directory
        files = os.listdir(
            f"/home/<USER>/repos/data_auditing/raw_data_from_snap_file_fix/{sym}"
        )
        tot_ct = len(files)

        # Calculate the number of files per segment
        segment_size = tot_ct // (10 if sym in ["NIFTY", "BANKNIFTY"] else 1)
        remainder = tot_ct % (10 if sym in ["NIFTY", "BANKNIFTY"] else 1)

        ct = 0
        segment_num = 1

        for cont in files:
            # Read the Parquet file and append to the list
            df_list.append(
                pd.read_parquet(
                    f"/home/<USER>/repos/data_auditing/raw_data_from_snap_file_fix/{sym}/{cont}"
                )
            )
            ct += 1

            # Check if the current segment is complete
            if ct == segment_size + (1 if segment_num <= remainder else 0):
                # Concatenate the DataFrames in the list
                df = pd.concat(df_list)
                df_list = []  # Reset the list for the next segment

                try:
                    lib.append(sym, df)
                    df = pd.DataFrame()

                    print(f"Done for {sym}")
                except Exception as e:
                    try:
                        cols = lib.get_description(sym).columns
                        cols = [col.name for col in cols]
                        df = df[cols]
                        lib.append(sym, df)
                        df = pd.DataFrame()

                        print(f"Done for {sym}")
                    except Exception as e:
                        df.to_parquet(
                            f"/home/<USER>/repos/data_auditing/raw_data_from_snap_file_fix/{sym}_{segment_num}.parquet"
                        )
                        df = pd.DataFrame()
                        print(f"failed for the {sym} due to {e}")

                print(f"Done segment {segment_num} for {sym}")

                # Reset counters for the next segment
                ct = 0
                segment_num += 1

        print(f"Done for {sym}")
    except Exception as e:
        print(f"Failed for {sym} due to {e}")
        return


# combine_store_and_delete_snap(sym='PEL')

# syms = set(
#     os.listdir("/home/<USER>/repos/data_auditing/raw_data_from_snap_file_fix")
# ) - {"NIFTY", "BANKNIFTY"}

# with Pool(7) as pool:
#     pool.map(combine_store_and_delete_snap, syms)

# # Call the function for the required symbols
# combine_store_and_delete_snap("NIFTY")
# combine_store_and_delete_snap("BANKNIFTY")

print()
