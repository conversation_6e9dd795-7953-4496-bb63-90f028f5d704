from multiprocessing import Pool
import pickle
import time
from arcticdb import Arctic
from minio import Minio
import pandas as pd
import numpy as np
import datetime
import os

store = Arctic("s3://*************:9000:arctic-db?access=super&secret=doopersecret")
librf = store["nse/1_min/raw_fut/trd"]

data_changed = {}

expiry_df = pd.read_parquet(
    "/home/<USER>/repos/data_auditing/new_optstk_expiry_dict.parquet"
)
expiry_df["near_month"] = expiry_df["near_month"].replace(
    pd.Timestamp(2008, 12, 25), pd.Timestamp(2008, 12, 24)
)
expiry_df["near_month"] = expiry_df["near_month"].replace(
    pd.Timestamp(2008, 4, 30), pd.Timestamp(2008, 4, 29)
)
expiry_df["next_month"] = expiry_df["next_month"].replace(
    pd.Timestamp(2008, 12, 25), pd.Timestamp(2008, 12, 24)
)
expiry_df["next_month"] = expiry_df["next_month"].replace(
    pd.Timestamp(2008, 4, 30), pd.Timestamp(2008, 4, 29)
)

expiry_df["nearym"] = expiry_df["near_month"].dt.strftime(date_format="%Y%m")
expiry_df["nextym"] = expiry_df["next_month"].dt.strftime(date_format="%Y%m")
expiry_df["neard"] = expiry_df["near_month"].dt.day
expiry_df["nextd"] = expiry_df["next_month"].dt.day

month_year_to_date = {}
for ind, row in expiry_df.iterrows():
    month_year_to_date[row["nearym"]] = row["neard"]
    month_year_to_date[row["nextym"]] = row["nextd"]

for sym in os.listdir("/home/<USER>/repos/data_auditing/raw_fut_combined_20250214"):
    print(f"Started for {sym}...")

    # df = librf.read(sym).
    df = pd.read_parquet(
        f"/home/<USER>/repos/data_auditing/raw_fut_combined_20250214/{sym}"
    )

    if "Cons_Volume" in df.columns:
        df["date"] = df.index.date
        df = df.reset_index()
        df["Volume"] = (
            df.groupby(["date", "expiry"])["Cons_Volume"]
            .diff()
            .fillna(df["Cons_Volume"])
        )
        df = df.drop(columns=["Cons_Volume", "date"])
        df = df.set_index("timestamp")

    df["exym"] = df["expiry"].dt.strftime(date_format="%Y%m")
    df["ex_old_d"] = df["expiry"].dt.day
    df["exd"] = df["exym"].map(month_year_to_date)
    df["exd"] = df["exd"].combine_first(df.ex_old_d)
    df["expiry_new"] = df["exym"] + df["exd"].astype(int).astype(str)
    df["expiry_new"] = pd.to_datetime(df.expiry_new, format="%Y%m%d")
    dfch = df[df.expiry != df.expiry_new]
    df = df[
        [
            "ID",
            "expiry_new",
            "Open",
            "High",
            "Low",
            "Close",
            "Vwap",
            "Volume",
        ]
    ]
    df = df.rename(columns={"expiry_new": "expiry"})
    df = df.reset_index()
    df = df.drop_duplicates(subset=["timestamp", "expiry"], keep="first")
    # df = df.set_index("timestamp").sort_index()

    # df['date'] = df.timstamp.dt.date
    # df = df.reset_index()
    df["Cons_Volume"] = df.groupby([df.timestamp.dt.date, "expiry"])["Volume"].cumsum()
    df = df.drop(columns=["Volume"])
    df = df.set_index("timestamp").sort_index()

    data_changed[sym] = [len(dfch), len(dfch) / len(df) * 100]

    librf.write(sym, df)
    # df.to_parquet(f'/home/<USER>/repos/data_auditing/raw_fut_combined_20250214/{sym}')

    print(f"Completed for {sym}")

print("Started uploading data_changed...")
with open("all_new_raw_fut_1min_expiry_update", "wb") as f:
    pickle.dump(data_changed, f)
print("Completed uploading data_changed")
