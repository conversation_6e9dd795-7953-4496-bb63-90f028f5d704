image: python:3.11-slim

stages:
  - linting
  - mypy
  - test
  - coverage

variables:
  UV_CACHE_DIR: "$CI_PROJECT_DIR/.uv_cache"
  UV_SYSTEM_PYTHON: "0"

.before_script_uv:
  before_script:
    - pip install uv
    - uv lock --check

.cache_venv:
  cache:
    key: "$CI_JOB_NAME-$CI_COMMIT_REF_SLUG"
    paths:
      - .uv_cache/

black_and_flake8:
  stage: linting
  extends:
    - .before_script_uv
    - .cache_venv
  script:
    - uv run black --check main
    - uv run flake8 main

mypy_check:
  stage: mypy
  extends:
    - .before_script_uv
    - .cache_venv
  script:
    - uv run mypy .

run_tests:
  stage: test
  extends:
    - .before_script_uv
    - .cache_venv
  script:
    - COVERAGE_FILE=.coverage.unit uv run coverage run --rcfile=.coveragerc -m pytest
  artifacts:
    paths:
      - .coverage.unit
    expire_in: 1 hour

coverage_report:
  stage: coverage
  extends:
    - .before_script_uv
    - .cache_venv
  script:
    - uv run coverage combine --rcfile=.coveragerc
    - uv run coverage report
    - uv run coverage xml -o coverage.xml
    - uv run coverage html
  coverage: '/^TOTAL\s+\d+\s+\d+\s+(\d+(?:\.\d+)?)%$/'
  artifacts:
    reports:
      coverage_report:
        coverage_format: cobertura
        path: coverage.xml
    paths:
      - htmlcov
  dependencies:
    - run_tests

  after_script:
    - rm -rf .venv $UV_CACHE_DIR