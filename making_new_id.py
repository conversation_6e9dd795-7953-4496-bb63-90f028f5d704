import pickle
import pandas as pd
from arcticdb import Arctic
import datetime
import os
from minio import Minio
import numpy as np

minio_client = Minio("192.168.0.198:11009", "minioreader", "reasersecret", secure=False)


def update_id(symbol):
    # getting only ID
    old = pd.read_parquet(
        f"/home/<USER>/repos/data_auditing/after_market_data_before_processing/{symbol}.parquet"
    )
    cols = old.columns

    # getting symbol_to_balte_id and balte_id_to_symbol
    symbol_to_balte_id_new = {}
    with open("/home/<USER>/repos/data_auditing/new_symbol_to_balte_id", "rb") as f:
        symbol_to_balte_id_new = pickle.load(f)

    symbol_to_balte_id = pickle.loads(
        minio_client.get_object("commondata", "balte_uploads/mapping_dict").data
    )

    for k in symbol_to_balte_id_new.keys():
        if k not in symbol_to_balte_id:
            symbol_to_balte_id[k] = symbol_to_balte_id_new[k]

    if len(old[old.ID == 0]):
        symbol_to_balte_id[np.nan] = 0

    balte_id_to_symbol = {}
    for k, v in symbol_to_balte_id.items():
        balte_id_to_symbol[v] = k

    # create mapping from ID to symbol from current balte_id_to_symbol
    old["symbol"] = old["ID"].map(balte_id_to_symbol)

    demerger_merger = pd.read_parquet(
        "/home/<USER>/repos/data_auditing/cash_metadata_till_20250121/demerger_merger.parquet"
    )

    if len(old[old.ID.isna()]) or len(old[old.symbol.isna() & old.ID != 0]):
        raise Exception("something off with ID conversion")

    # replacing symbol name for e.g. PEL_04OCT2002 to PEL
    old["symbol"] = old["symbol"].str.replace(
        r"_\d{2}[A-Za-z]{3}\d{4}$", "", regex=True
    )

    symbol_change = pd.read_parquet(
        "/home/<USER>/repos/data_auditing/cash_metadata_till_20250121/symbol_change.parquet"
    )

    symbol_change = symbol_change[symbol_change.symbol != "DTIL"]

    old = old.reset_index()
    symbol_change = symbol_change.reset_index()

    for _, row in symbol_change.iterrows():
        if (
            row["symbol"] in symbol_to_balte_id
            and row["current"] not in symbol_to_balte_id
        ):
            symbol_to_balte_id[row["current"]] = symbol_to_balte_id[row["symbol"]]
        if (
            row["current"] in symbol_to_balte_id
            and row["symbol"] not in symbol_to_balte_id
        ):
            symbol_to_balte_id[row["symbol"]] = symbol_to_balte_id[row["current"]]

    # we will use symbol change to get current symbol for some symbol having IDs changed with symbol also

    # merging isfno with symbol change data on symbol
    merged_df = pd.merge(old, symbol_change, on="symbol", how="left")
    # if mapping exist of symbol to some another current symbol then make it as current symbol
    merged_df["symbol"] = merged_df["current"].combine_first(merged_df["symbol"])
    # dropping extra columns
    merged_df = merged_df.drop(columns=["date_y", "current", "ID"])
    merged_df = merged_df.rename(columns={"date_x": "date"}).set_index("date")
    # now we are giving current ID to each symbol from symbol_to_balte_id

    merged_df["ID"] = merged_df["symbol"].map(symbol_to_balte_id)
    old = merged_df

    new = old.copy()

    # then again changing ID with demerger_merger info
    for sym, _ in new.groupby("symbol"):
        dmsym = demerger_merger[demerger_merger.Symbol == sym]
        for index in range(len(dmsym) - 1, -1, -1):
            new.loc[
                (new.symbol == sym) & (new.index < dmsym.index[index]), "ID"
            ] = dmsym.iloc[index]["ID"]

    new = new[cols]

    if len(new[new.ID.isna()]):
        raise Exception("something off with ID conversion")

    new["ID"] = new["ID"].astype("uint64")

    new.to_parquet(
        f"/home/<USER>/repos/data_auditing/after_market_data/{symbol}.parquet"
    )


for sym in os.listdir(
    "/home/<USER>/repos/data_auditing/after_market_data_before_processing"
):
    symbol = sym.split(".")[0]

    try:
        update_id(symbol=symbol)
        os.remove(
            f"/home/<USER>/repos/data_auditing/after_market_data_before_processing/{sym}"
        )
    except Exception as e:
        print(f"Failed for {symbol}: {e}")

print()
