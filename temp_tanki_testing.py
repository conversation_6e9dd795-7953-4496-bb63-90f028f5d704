import datetime
import numpy as np
from main.tanki import Tanki
import pandas as pd
from arcticdb import Arctic
from main.data.utility import get_iv


storek=Arctic("s3://192.168.0.121:9000:kivi-arcticdb?access=super&secret=doopersecret")


tanki = Tanki(exchange_type="nse", write=True)
tanki.login(username="mantraraj", password="mantraraj")



# for writing new symbol data
# tanki["nse/1_min/raw_eq_ob/orderbook"].write_metadata("symbol", data)
# tanki["nse/1_min/raw_eq_ob/orderbook"].write("symbol", data)


# for appending existing symbol data
# tanki["nse/1_min/raw_eq_ob/orderbook"].append("symbol", data)


# compilation through tanki (end_date is not inclusive)
# tanki.compile_data(universe_list=["futstk_ob"], frequency=1, dtype="orderbook", start_date=pd.Timestamp(2023, 12, 1), end_date=pd.Timestamp(2023, 12, 3)) 


print()
