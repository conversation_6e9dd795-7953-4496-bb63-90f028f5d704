import pickle
import pandas as pd
from arcticdb import Arctic
import datetime
from minio import Minio


minio_client = Minio("192.168.0.198:11009", "minioreader", "reasersecret", secure=False)

# getting only ID
old_isnifty = pd.read_parquet(
    "/home/<USER>/repos/data_auditing/old_isnifty.parquet"
)[["ID"]]
# old_isnifty = old_isnifty[old_isnifty.index.date < datetime.date(2025, 1, 25)]

# getting symbol_to_balte_id and balte_id_to_symbol
symbol_to_balte_id_new = {}
with open(
    "/home/<USER>/repos/data_auditing/cash_metadata_till_20250121/new_symbol_to_balte_id",
    "rb",
) as f:
    symbol_to_balte_id_new = pickle.load(f)

symbol_to_balte_id = pickle.loads(
    minio_client.get_object("commondata", "balte_uploads/mapping_dict").data
)

for k in symbol_to_balte_id_new.keys():
    if k not in symbol_to_balte_id:
        symbol_to_balte_id[k] = symbol_to_balte_id_new[k]

balte_id_to_symbol = {}
for k, v in symbol_to_balte_id.items():
    balte_id_to_symbol[v] = k

# create mapping from ID to symbol from current balte_id_to_symbol
old_isnifty["symbol"] = old_isnifty["ID"].map(balte_id_to_symbol)

demerger_merger = pd.read_parquet(
    "/home/<USER>/repos/data_auditing/cash_metadata_till_20250121/demerger_merger.parquet"
)

# replacing symbol name for e.g. PEL_04OCT2002 to PEL
old_isnifty["symbol"] = old_isnifty["symbol"].str.replace(
    r"_\d{2}[A-Za-z]{3}\d{4}$", "", regex=True
)

symbol_change = pd.read_parquet(
    "/home/<USER>/repos/data_auditing/cash_metadata_till_20250121/symbol_change.parquet"
)

symbol_change = symbol_change[symbol_change.symbol != "DTIL"]


old_isnifty = old_isnifty.reset_index()
symbol_change = symbol_change.reset_index()

for _, row in symbol_change.iterrows():
    if row["symbol"] in symbol_to_balte_id and row["current"] not in symbol_to_balte_id:
        symbol_to_balte_id[row["current"]] = symbol_to_balte_id[row["symbol"]]
    if row["current"] in symbol_to_balte_id and row["symbol"] not in symbol_to_balte_id:
        symbol_to_balte_id[row["symbol"]] = symbol_to_balte_id[row["current"]]

# we will use symbol change to get current symbol for some symbol having IDs changed with symbol also

# merging isnifty with symbol change data on symbol
merged_df = pd.merge(old_isnifty, symbol_change, on="symbol", how="left")
# if mapping exist of symbol to some another current symbol then make it as current symbol
merged_df["symbol"] = merged_df["current"].combine_first(merged_df["symbol"])
# dropping extra columns
merged_df = merged_df.drop(columns=["date_y", "current", "ID"])
merged_df = merged_df.rename(columns={"date_x": "date"}).set_index("date")
# now we are giving current ID to each symbol from symbol_to_balte_id
merged_df["ID"] = merged_df["symbol"].map(symbol_to_balte_id)
old_isnifty = merged_df

new_isnifty = old_isnifty.copy()

# then again changing ID with demerger_merger info
for sym, _ in new_isnifty.groupby("symbol"):
    dmsym = demerger_merger[demerger_merger.Symbol == sym]
    for index in range(len(dmsym) - 1, -1, -1):
        new_isnifty.loc[
            (new_isnifty.symbol == sym) & (new_isnifty.index < dmsym.index[index]), "ID"
        ] = dmsym.iloc[index]["ID"]

new_isnifty = new_isnifty[["ID", "symbol"]]
new_isnifty = new_isnifty.rename(columns={"symbol": "Symbol"})

new_isnifty.to_parquet(
    "/home/<USER>/repos/data_auditing/cash_metadata_till_20250121/isnifty.parquet"
)


print()
