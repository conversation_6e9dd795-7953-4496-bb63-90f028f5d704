import datetime
import os
import pickle
from typing import List

import numpy as np

# from main.tanki import Tanki
import pandas as pd
from multiprocessing import Pool
from main.data.auditor import Auditor
from main.data.operation import Operator
from main.config.config_NSE import ConfigNSE
from main.enums import StorageType, Operation
from main.data.compiler import Compiler

# from main.data.utility import generate_list
import time
from arcticdb import Arctic

store = Arctic("s3://*************:9000:arctic-db?access=super&secret=doopersecret")


def generate_times(start_time, end_time, interval):
    current_time = start_time
    times = []

    while current_time <= end_time:
        times.append(current_time.strftime("%H:%M"))
        current_time += interval

    return times


start_1min = datetime.datetime.strptime("09:16", "%H:%M")
end_1min = datetime.datetime.strptime("15:30", "%H:%M")
interval_1min = datetime.timedelta(minutes=1)
time_list_1min = generate_times(start_1min, end_1min, interval_1min)

start_5min = datetime.datetime.strptime("09:20", "%H:%M")
end_5min = datetime.datetime.strptime("15:30", "%H:%M")
interval_5min = datetime.timedelta(minutes=5)
time_list_5min = generate_times(start_5min, end_5min, interval_5min)


def previous_date(date, all_dates, lookback=1):
    """
    Lookback from current date

    Parameters
    ----------
    date : datetime.date()
        Date from which lookback is required
    lookback: int
        Number of days to lookback

    Returns
    -------
    date:  datetime.date()
    """
    index = np.where(all_dates >= date)
    if len(index[0]) == 0:
        raise IndexError("%s might be a holiday date...try a nearby date" % date)
    if index[0][0] - lookback < 0:
        return all_dates.loc[0].iloc[0]
    return all_dates.loc[index[0][0] - lookback].iloc[0]


def generate_list(
    dates: List[pd.Timestamp], lookback: int, all_dates
) -> List[List[pd.Timestamp]]:
    """
    Breaks the given date list into [start,end] dates if there's a
    continous gap of more than lookback no. of days

    Parameters:
    -----------
    dates: `list`
        A list of dates
    lookback: `int`
        Lookback period

    Returns:
    --------

    A list of [start, end] dates period
    """

    res_list = []
    temp_list = []  # Contains only two values -- start and end time

    if len(dates) == 0:
        return res_list

    prev_date = previous_date(dates[0], all_dates, lookback)
    temp_list.append(prev_date)

    for i in range(0, len(dates)):
        timediff = dates[i] - prev_date
        if timediff.days > lookback:
            lookback_date = previous_date(dates[i], all_dates, lookback)
            if (prev_date is not None) and (
                previous_date(prev_date, all_dates, -1) < lookback_date
            ):
                temp_list.append(prev_date)
                res_list.append(temp_list)
                temp_list = list()
                temp_list.append(lookback_date)
        if i == len(dates) - 1:
            temp_list.append(dates[i])
            res_list.append(temp_list)
            temp_list = []
        prev_date = dates[i]
    return res_list


def resample(universe: str, dtype: str):
    opt = Operator(config=ConfigNSE())
    # aud = Auditor(config=ConfigNSE())

    base_path = f"/home/<USER>/jupyter/all_new_{universe}_1min_{dtype}"
    baselib = store[f"nse/1_min/{universe}/{dtype}"]
    lib5 = store[f"nse/5_min/{universe}/{dtype}"]
    # if ("1min_cash_trd" in symbol) and ("raw" not in symbol):
    # for sym in aud.list_file_names(storage_type=StorageType.Db, file_location="nse/1_min/cash/trd"):
    # for sym in os.listdir(base_path):
    for sym in baselib.list_symbols():
        if sym[0] == ".":
            continue
        # one_min_data = pd.read_parquet(f"{base_path}/{sym}")
        one_min_data = baselib.read(sym).data
        if len(one_min_data) > 0:
            five_min_data = opt.resample_data_1_min_to_5_min(universe, one_min_data)
        else:
            five_min_data = one_min_data

        print(f"{sym} resampling done!!")
        # five_min_data.to_parquet(
        #     f"/home/<USER>/jupyter/all_new_{universe}_5min_{dtype}/{sym}"
        # )
        lib5.write(sym, five_min_data)

    return


def column_bucket(universe: str, frequency: int, dtype: str, start_date, end_date):
    print(f"Started column bucket for {universe} {frequency}")
    opt = Operator(config=ConfigNSE())
    col_dict_list = {}
    base_path = f"/home/<USER>/jupyter/all_new_{universe}_{frequency}min_{dtype}"
    baselib = store[f"nse/{frequency}_min/{universe}/trd"]
    lib = store[f"nse/{frequency}_min/{universe}/column_bucket"]

    # for sym in os.listdir(base_path):
    for sym in baselib.list_symbols():
        # if sym[0] == ".":
        #     continue

        # trd_data = pd.read_parquet(f"{base_path}/{sym}")
        trd_data = baselib.read(sym).data
        trd_data = trd_data[
            (trd_data.index.date >= start_date) & (trd_data.index.date < end_date)
        ]

        if len(trd_data) == 0:
            continue

        if "Symbol" in trd_data.columns:
            trd_data = trd_data.drop(columns=["Symbol"])
            trd_data.to_parquet(f"{base_path}/{sym}")

        col_dict = opt.create_column_bucket(universe=universe, data=trd_data)

        for col, df in col_dict.items():
            if col in col_dict_list:
                col_dict_list[col].append(df)
            else:
                col_dict_list[col] = [df]

    for col in col_dict_list.keys():
        df = pd.concat(col_dict_list[col])
        df = df.sort_index()

        df = df[time_list_1min if frequency == 1 else time_list_5min]

        # df.to_parquet(F"{universe}_{frequency}_column_bucket/{col}.parquet")

        print(f"{col} column bucket done!!")
        if start_date == datetime.date(2007, 1, 1):
            lib.write(col, df)
        else:
            lib.append(col, df)
        df.to_parquet(f"{base_path[:-4]}_column_bucket/{col}.parquet")


def column_bucket_mcx(universe: str, frequency: int, dtype: str):
    print(f"Started column bucket for {universe} {frequency}")
    opt = Operator(config=ConfigNSE())
    col_dict_list = {}
    base_path = f"/home/<USER>/jupyter/mcx_fut_near_5min_trd"

    data_list = []
    # for sym in os.listdir(base_path):
    #     if sym[0] == ".":
    #         continue
    #     data = pd.read_parquet(f"{base_path}/{sym}")
    #     data = data[
    #         (data.index.time >= datetime.time(9, 5))
    #         & (data.index.time <= datetime.time(23, 55))
    #     ]
    #     data = data.reset_index()
    #     data = data.drop_duplicates(subset=["timestamp", "ID"])
    #     data = data.set_index(["timestamp"]).sort_index()
    #     data_list.append(data)

    # trd_data = pd.concat(data_list)
    trd_data = pd.read_parquet(f"/media/hdd/jupyterdata/ashish/mcx_fut_near.parquet")
    if len(trd_data) == 0:
        return

    col_dict = opt.create_column_bucket(universe=universe, data=trd_data)

    for col, df in col_dict.items():
        if col in col_dict_list:
            col_dict_list[col].append(df)
        else:
            col_dict_list[col] = [df]

    for col in col_dict_list.keys():
        df = pd.concat(col_dict_list[col])
        df = df.sort_index()

        df.columns.name = None
        print(f"{col} column bucket done!!")

        df.to_parquet(f"{universe}_5min_cb/{col}.parquet")


def compile_data_range(args):
    start_date = args[0]
    end_date = args[1]

    tanki = Tanki(exchange_type="nse")
    tanki.login(username="ashish", password="ashish")

    tanki.compile_data(
        universe_list=["cash"],
        frequency=1,
        dtype="trd",
        start_date=start_date,
        end_date=end_date,
    )
    print(f"Done for {start_date} to {end_date}")


def compilation():
    year_ranges = []

    # for year in range(1,12):
    #     year_ranges.append((pd.Timestamp(2023,year,1),pd.Timestamp(2023,year+1,1)))

    # year_ranges.append(())

    # print(year_ranges)

    start_time = time.perf_counter()
    compile_data_range((pd.Timestamp(2007, 1, 1), pd.Timestamp(2024, 3, 31)))

    # with Pool(8) as p:
    #     p.map(compile_data_range,year_ranges)

    finish_time = time.perf_counter()

    print(f"Program finished in {finish_time-start_time} seconds")


def derived_universes_compilation(universe, lookback, frequency, start_date, end_date):
    print(f"Started for derived universe: {universe}")
    config = ConfigNSE()
    compiler = Compiler(config)
    aud = Auditor(config)
    # opt = Operator(config=ConfigNSE())

    lib = store[f"nse/{frequency}_min/{universe}/trd"]
    # libcb = store[f"nse/{frequency}_min/{universe}/column_bucket"]

    start_date = start_date
    end_date = end_date

    ALL_DATES = compiler.read(
        storage_type=StorageType.FILE,
        file_location=compiler._config.FILE_DICT["ALL_DATES"][0],
        file_name=compiler._config.FILE_DICT["ALL_DATES"][1],
    )
    base_universe = "cash"
    symbol_list = compiler.list_file_names(
        storage_type=StorageType.DB,
        file_location=f"nse/{frequency}_min/{base_universe}/trd",
    )

    ## List loading
    symbol_list_universe = "isliquid" if universe == "eq" else "isfno"
    symbol_list_df = pd.read_parquet(
        f"/home/<USER>/repos/data_auditing/cash_metadata_till_20250121/{symbol_list_universe}.parquet"
    )
    symbol_list_df = symbol_list_df[symbol_list_df.index < end_date]
    symbol_list_df = symbol_list_df.reset_index()

    # col_dict_list = {}

    for ID, df in symbol_list_df.groupby(["ID"]):
        ID = int(ID[0])
        if str(ID) not in symbol_list:
            print("Warning : SYMBOL_ID = ", ID, " is not present in the cash universe")
            continue

        index_column = "date" if "date" in df else "index"
        date_list = list(df[index_column])
        date_list = generate_list(date_list, lookback, ALL_DATES)
        df_list = []

        for start_time, end_time in date_list:
            data_df = compiler.read(
                storage_type=StorageType.DB,
                file_location=f"nse/{frequency}_min/{base_universe}/trd",
                file_name=str(ID),
                start_date=start_time,
                end_date=end_time + pd.Timedelta(days=1),
            )
            df_list.append(data_df)

        df = pd.concat(df_list)
        df = df[
            (df.index.time > datetime.time(9, 15))
            & (df.index.time <= datetime.time(15, 30))
        ]
        if len(df) == 0:
            continue

        lib.write(str(ID), df)

    #     df_col_dict = opt.create_column_bucket(universe=universe, data=df)
    #     for col, dfcbt in df_col_dict.items():
    #         if col in col_dict_list:
    #             col_dict_list[col].append(dfcbt)
    #         else:
    #             col_dict_list[col] = [dfcbt]

    # for col in col_dict_list.keys():
    #     dfcb = pd.concat(col_dict_list[col])
    #     dfcb = dfcb.sort_index()
    #     dfcb = dfcb[time_list_1min if frequency == 1 else time_list_5min]

    #     print(f"{col} column bucket done!!")
    #     if start_date == datetime.date(2007, 1, 1):
    #         libcb.write(col, dfcb)
    #     else:
    #         libcb.append(col, dfcb)


def move_from_compilation_to_lib(librarycom, library):
    print(f"Started moving from {librarycom} to {library}...")
    libcom = store[librarycom]
    lib = store[library]
    exch = library.split("/")[0]
    freq = library.split("/")[1][0]
    universe = library.split("/")[2]
    dtype = library.split("/")[3]

    for sym in libcom.list_symbols():
        if f"{exch}_{freq}min_{universe}_{dtype}" not in sym:
            continue
        df = libcom.read(sym).data
        df = df[
            (df.index.time > datetime.time(9, 15))
            & (df.index.time <= datetime.time(15, 30))
        ]
        df["ID"] = df["ID"].astype("uint64")
        df["ID"] = df["ID"] % 10000
        df = df.drop(columns="Symbol")
        symbol = sym.split(f"{exch}_{freq}min_{universe}_{dtype}_")[1].split("_")[0]
        lib.write(symbol, df)
        print(f"Moving done for {symbol}")

    print(f"Completed moving from {librarycom} to {library} :)")


def upload_to_jupyter(library, dest_path):
    lib = store[library]
    for sym in lib.list_symbols():
        df = lib.read(sym).data
        df.to_parquet(f"{dest_path}/{sym}.parquet")


def making_fut_raw():
    libfr = store["nse/1_min/fut_raw/trd"]
    expiry_df = pd.read_parquet(
        "/home/<USER>/repos/data_auditing_old/new_optstk_expiry_dict.parquet"
    )
    expiry_df = expiry_df.reset_index()

    for sym in libfr.list_symbols():
        df = libfr.read(sym).data
        df["expiry"] = pd.to_datetime(df["ID"].astype(str).str[:8], format="%Y%m%d")
        df["ID"] = df["ID"] % 10000
        df["date"] = pd.to_datetime(df.index.date)
        dfm = pd.merge(
            df.reset_index(), expiry_df[["date", "near_month"]], on=["date"], how="left"
        )
        dfm = dfm[dfm.expiry == dfm.near_month]
        dfm = dfm.set_index("timestamp").sort_index()
        dfm = dfm.drop(columns=["expiry", "date", "near_month"])

        dfm["ID"] = dfm["ID"].astype("uint64")
        dfm = dfm.drop(columns=["Symbol"])

        libfr.write(sym, dfm)


def make_fut_close():
    lib = store["nse/1_min/fno/trd"]
    libf = store["nse/1_min/fut/trd"]
    libfc = store["nse/1_min/fut_close/trd"]
    symbolsf = libf.list_symbols()

    for sym in lib.list_symbols():
        try:
            if sym not in symbolsf:
                continue
            df = lib.read(sym).data
            dff = libf.read(sym).data
            if len(df) == 0 or len(dff) == 0:
                continue
            dff = dff.rename(columns={"Close": "fut_close"})
            dfm = pd.merge(df, dff[["fut_close"]], on=["timestamp"], how="left")
            dfm = dfm[["ID", "fut_close"]]
            libfc.write(sym, dfm)
        except Exception as e:
            print(f"Failed making fut_close for {sym}: {e}")
            continue


def delete_from_lib(library):
    lib = store[library]
    for sym in lib.list_symbols():
        lib.delete(sym)


def compile(universe, freq, dtype, start_date, end_date):
    config = ConfigNSE()
    compiler = Compiler(config=config)
    print(f"Started {universe} compilation...")
    compiler.compile(
        universe_list=[universe],
        frequency=freq,
        dtype=dtype,
        start_date=start_date,
        end_date=end_date,
    )
    print(f"Started {universe} compilation...")

    lib = store[f"nse/{freq}_min/{universe}/{dtype}"]
    libcom = store[f"nse/compilation_{universe}"]

    for sym in libcom.list_symbols():
        if f"nse_{freq}min_{universe}_{dtype}" not in sym:
            continue
        symbol = sym.split("_")[4]
        df = libcom.read(sym).data
        lib.write(symbol, df)


def upload_to_lib(library, source):
    lib = store[library]
    for sym in os.listdir(source):
        df = pd.read_parquet(f"{source}/{sym}")
        symbol = sym.split(".")[0]
        lib.write(symbol, df)


def move_from_bucket_to_bucket(bucket1, bucket2, library):
    store1 = Arctic(
        f"s3://*************:9000:{bucket1}?access=super&secret=doopersecret"
    )
    store2 = Arctic(
        f"s3://*************:9000:{bucket2}?access=super&secret=doopersecret"
    )

    lib1 = store1[library]
    lib2 = store2[library]

    for sym in lib1.list_symbols():
        lib2.write(sym, lib1.read(sym).data)


if __name__ == "__main__":
    args = []

    print("Started creating column bucket...")
    column_bucket_mcx(universe="mcx_fut_near", frequency=5, dtype="trd")
    print("Completed creating column bucket :)")

    # print(f"Started uploading to jupyter...")
    # upload_to_jupyter(
    #     "nse/1_min/raw_fut/trd", "/home/<USER>/jupyter/all_new_raw_fut_1min_trd"
    # )
    # print(f"Completed uploading to jupyter...")

    # print(f"Started deleting 1min...")
    # delete_from_lib(library="nse/compilation_fut")
    # print(f"Completed deleting 1min...")

    # print(f"Started deleting 1min...")
    # delete_from_lib(library="nse/1_min/fut_raw/trd")
    # print(f"Completed deleting 1min...")

    # print(f"Started deleting 1min...")
    # delete_from_lib(library="nse/5_min/fut_raw/trd")
    # print(f"Completed deleting 1min...")

    # print(f"Started deleting 1min...")
    # delete_from_lib(library="nse/1_min/fut_raw/column_bucket")
    # print(f"Completed deleting 1min...")

    # print(f"Started deleting 1min...")
    # delete_from_lib(library="nse/5_min/fut_raw/column_bucket")
    # print(f"Completed deleting 1min...")

    # print(f"Started deleting 1min...")
    # delete_from_lib(library="nse/1_min/fut_raw/trd")
    # print(f"Completed deleting 1min...")

    # print(f"Started deleting 1min...")
    # delete_from_lib(library="nse/5_min/fut_raw/trd")
    # print(f"Completed deleting 1min...")

    # print(f"Started deleting 1min...")
    # delete_from_lib(library="nse/1_min/fut_raw/column_bucket")
    # print(f"Completed deleting 1min...")

    # print(f"Started deleting 1min...")
    # delete_from_lib(library="nse/5_min/fut_raw/column_bucket")
    # print(f"Completed deleting 1min...")

    # print(f"Started moving...")
    # move_from_compilation_to_lib("nse/compilation_fut", "nse/1_min/fut_raw/trd")
    # print(f"Completed moving...")

    # print(f"Started deleting 1min...")
    # delete_from_lib(library="nse/compilation_fut")
    # print(f"Completed deleting 1min...")

    # print(f"Started deleting 1min...")
    # delete_from_lib(library="nse/5_min/cash/trd")
    # print(f"Completed deleting 1min...")

    # print(f"Started deleting 1min...")
    # delete_from_lib(library="nse/1_min/fno/trd")
    # print(f"Completed deleting 1min...")

    # print(f"Started deleting 1min...")
    # delete_from_lib(library="nse/5_min/fno/trd")
    # print(f"Completed deleting 1min...")

    # print(f"Started deleting 1min...")
    # delete_from_lib(library="nse/1_min/eq/trd")
    # print(f"Completed deleting 1min...")

    # print(f"Started deleting 1min...")
    # delete_from_lib(library="nse/5_min/eq/trd")
    # print(f"Completed deleting 1min...")

    # print(f"Started deleting 1min...")
    # delete_from_lib(library="nse/1_min/fno/column_bucket")
    # print(f"Completed deleting 1min...")

    # print(f"Started deleting 1min...")
    # delete_from_lib(library="nse/5_min/fno/column_bucket")
    # print(f"Completed deleting 1min...")

    # print(f"Started deleting 1min...")
    # delete_from_lib(library="nse/1_min/eq/column_bucket")
    # print(f"Completed deleting 1min...")

    # print(f"Started deleting 1min...")
    # delete_from_lib(library="nse/5_min/eq/column_bucket")
    # print(f"Completed deleting 1min...")

    # print(f"Started deleting 1min...")
    # delete_from_lib(library="nse/1_min/raw_fut/trd")
    # print(f"Completed deleting 1min...")

    # print("Started uploading to lib...")
    # upload_to_lib(library="nse/1_min/raw_fut/trd", source="/home/<USER>/repos/data_auditing_old/raw_fut_all_combined")
    # print("Completed uploading to lib...")

    # print("Started compilation...")
    # compile(universe="fut", freq=1, dtype="trd", start_date=pd.Timestamp(2007, 1, 1), end_date=pd.Timestamp(2024, 9, 1))
    # print("Compilation done :)")

    # print(f"Started resampling...")
    # resample(universe="cash", dtype="trd")
    # print(f"Completed resampling...")

    # print(f"Started 1_min column_bucket...")
    # column_bucket(universe="fut", frequency=1, dtype="trd")
    # print(f"Completed 1_min column_bucket...")

    # print(f"Started 5_min column_bucket...")
    # column_bucket(universe="fut", frequency=1, dtype="trd")
    # print(f"Completed 5_min column_bucket...")

    # print(f"Started making fut_raw...")
    # making_fut_raw()
    # print(f"Completed making fut_raw :)")

    # print(f"Started resampling...")
    # resample(universe="futidx_fut", dtype="trd")
    # print(f"Completed resampling...")

    # print(f"Started 1_min column_bucket...")
    # column_bucket(
    #     universe="futidx_fut",
    #     frequency=1,
    #     dtype="trd",
    #     start_date=datetime.date(2007, 1, 1),
    #     end_date=datetime.date(2025, 2, 14),
    # )
    # print(f"Completed 1_min column_bucket...")

    # print(f"Started 5_min column_bucket...")
    # column_bucket(
    #     universe="futidx_fut",
    #     frequency=5,
    #     dtype="trd",
    #     start_date=datetime.date(2007, 1, 1),
    #     end_date=datetime.date(2025, 2, 14),
    # )
    # print(f"Completed 5_min column_bucket...")

    # column_bucket_mcx(universe="optcom_20250212", frequency=5, dtype="column_bucket")

    # print(f"Started 1_min column_bucket...")
    # column_bucket(universe="fut_raw", frequency=1, dtype="trd", start_date=datetime.date(2007, 1, 1), end_date=datetime.date(2025, 2, 15))
    # print(f"Completed 1_min column_bucket...")

    # print(f"Started 5_min column_bucket...")
    # column_bucket(
    #     universe="futidx",
    #     frequency=1,
    #     dtype="trd",
    #     start_date=datetime.date(2007, 1, 1),
    #     end_date=datetime.date(2025, 5, 28),
    # )
    # print(f"Completed 5_min column_bucket...")

    # print(f"Started deleting 1min...")
    # delete_from_lib(library="nse/1_min/fno/trd")
    # print(f"Completed deleting 1min...")

    # print(f"Started fno 1min...")
    # derived_universes_compilation(
    #     universe="fno",
    #     lookback=200,
    #     frequency=1,
    #     start_date=pd.Timestamp(2007, 1, 1),
    #     end_date=pd.Timestamp(2025, 2, 11),
    # )
    # print(f"Completed fno 1min...")

    # print(f"Started fno 5min...")
    # derived_universes_compilation(
    #     universe="fno",
    #     lookback=200,
    #     frequency=5,
    #     start_date=pd.Timestamp(2007, 1, 1),
    #     end_date=pd.Timestamp(2025, 1, 22),
    # )
    # print(f"Completed fno 5min...")

    # print(f"Started eq 1min...")
    # derived_universes_compilation(
    #     universe="eq",
    #     lookback=100,
    #     frequency=1,
    #     start_date=pd.Timestamp(2007, 1, 1),
    #     end_date=pd.Timestamp(2025, 1, 23),
    # )
    # print(f"Completed eq 1min...")

    # print(f"Started eq 5min...")
    # derived_universes_compilation(
    #     universe="eq",
    #     lookback=200,
    #     frequency=5,
    #     start_date=pd.Timestamp(2007, 1, 1),
    #     end_date=pd.Timestamp(2025, 1, 23),
    # )
    # print(f"Completed eq 5min...")

    # print(f"Started 1_min fno column_bucket...")
    # column_bucket(
    #     universe="fno",
    #     frequency=1,
    #     dtype="trd",
    #     start_date=datetime.date(2007, 1, 1),
    #     end_date=datetime.date(2025, 1, 22),
    # )
    # print(f"Completed 1_min fno column_bucket...")

    # print(f"Started 5_min fno column_bucket...")
    # column_bucket(
    #     universe="fno",
    #     frequency=5,
    #     dtype="trd",
    #     start_date=datetime.date(2007, 1, 1),
    #     end_date=datetime.date(2025, 1, 22),
    # )
    # print(f"Completed 5_min fno column_bucket...")

    # print(f"Started 5_min eq column_bucket...")
    # column_bucket(
    #     universe="eq",
    #     frequency=5,
    #     dtype="trd",
    #     start_date=datetime.date(2007, 1, 1),
    #     end_date=datetime.date(2025, 1, 23),
    # )
    # print(f"Completed 5_min eq column_bucket...")

    # print(f"Started 1_min eq column_bucket...")
    # column_bucket(
    #     universe="eq",
    #     frequency=1,
    #     dtype="trd",
    #     start_date=datetime.date(2007, 1, 1),
    #     end_date=datetime.date(2015, 1, 1),
    # )
    # print(f"Completed 1_min eq column_bucket...")

    # print(f"Started 1_min eq column_bucket...")
    # column_bucket(
    #     universe="eq",
    #     frequency=1,
    #     dtype="trd",
    #     start_date=datetime.date(2015, 1, 1),
    #     end_date=datetime.date(2020, 1, 1),
    # )
    # print(f"Completed 1_min eq column_bucket...")

    # print(f"Started 1_min eq column_bucket...")
    # column_bucket(
    #     universe="eq",
    #     frequency=1,
    #     dtype="trd",
    #     start_date=datetime.date(2020, 1, 1),
    #     end_date=datetime.date(2023, 1, 1),
    # )
    # print(f"Completed 1_min eq column_bucket...")

    # print(f"Started 1_min eq column_bucket...")
    # column_bucket(
    #     universe="eq",
    #     frequency=1,
    #     dtype="trd",
    #     start_date=datetime.date(2023, 1, 1),
    #     end_date=datetime.date(2025, 1, 23),
    # )
    # print(f"Completed 1_min eq column_bucket...")

    print("Done :-)")
