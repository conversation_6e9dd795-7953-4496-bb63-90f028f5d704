from typing import Optional, <PERSON>ple
import pandas as pd
import numpy as np
import pickle


class VersionedObject:
    data = b""


class MinioMocker:
    def get_object(
        self,
        file_location: str,
        file_name: str,
        date_range: Optional[
            Tuple[Optional[pd.Timestamp], Optional[pd.Timestamp]]
        ] = None,
    ):
        if file_location == "bucket" and file_name == "object_ALL_DATES":
            test_data = VersionedObject()

            try:
                with open(
                    "./test/utility/test_data/minio_all_dates_test_data.npy", "rb"
                ) as file:
                    test_data.data = file.read()
            except Exception:
                raise Exception("Error in reading file")

            return test_data
        elif file_location == "bucket" and file_name == "object_dict":
            test_data = VersionedObject()
            df = pd.DataFrame([{"value": 42}])
            test_data.data = pickle.dumps(df)
            return test_data
        else:
            raise Exception(f"Error in getting {file_name} from {file_location}")
