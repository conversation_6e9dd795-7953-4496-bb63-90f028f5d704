from main.config.config_factory import ConfigFactory
from main.config.config_NSE import ConfigNSE
import pytest


def test_configfactory_configNSE_success():
    """
    Testing ConfigNSE in ConfigFactory
    """
    db_store = ConfigFactory(exchange_type="nse")
    assert isinstance(db_store, ConfigNSE)


def test_configfactory_raise_exception():
    expected_message = "ConfigError: Config class not found for nsse"
    with pytest.raises(Exception) as exception_info:
        ConfigFactory(exchange_type="nsse")
    assert str(exception_info.value) == expected_message
