site_name: <PERSON><PERSON> Documentation

theme: 
  name: "material"
  features:
  - search.suggest
  - search.highlight
  - content.tabs.link
  - header.autohide

markdown_extensions:
- toc:
    permalink: true
- markdown.extensions.codehilite:
    guess_lang: false
- admonition
- codehilite
- extra
- pymdownx.superfences:
    custom_fences:
    - name: mermaid
      class: mermaid
      format: !!python/name:pymdownx.superfences.fence_code_format ''
- pymdownx.tabbed:
    alternate_style: true

plugins:
  - search
  - autorefs
  - mkdocstrings
  - table-reader

nav:
  - Tutorials:
    - tutorial/tanki.md
    - tutorial/checker.md
    - tutorial/compiler.md

  - Tanki Architecture:
    - architecture/introduction.md
    
  - Reference:
    - reference/tanki.md
    - reference/library.md
    - Data:
      - reference/data/auditor.md
      - reference/data/checker.md
      - reference/data/operation.md
      - reference/data/compiler.md
    - Storage:
      - reference/storage/db_store.md
      - reference/storage/file_store.md
      - reference/storage/local_store.md