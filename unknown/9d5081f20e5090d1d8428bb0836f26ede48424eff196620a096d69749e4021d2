import sys

sys.path.append("/home/<USER>")
sys.path.append("/home/<USER>")

from airflow.models import DAG
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python_operator import BranchPythonOperator
from airflow.operators.python_operator import Python<PERSON>perator
import datetime
import pendulum
import numpy as np
from io import BytesIO
from minio import Minio
from airflow.hooks.base_hook import BaseHook
from ms_teams_notifications import send_message_to_ms_teams
from daily_appends.NSE.daily_appends_nse import DailyAppendsNSE

dailyAppends = DailyAppendsNSE(exchange_type="nse")

minioToken = BaseHook.get_connection("minio")
minioClient = Minio(
    f"{minioToken.host}:{minioToken.port}",
    access_key=minioToken.login,
    secret_key=minioToken.password,
    secure=False,
)

ALL_DATES = np.load(
    BytesIO(minioClient.get_object("commondata", "balte_uploads/ALL_DATES.npy").data),
    allow_pickle=True,
)

args = {
    "owner": "Airflow",
    "start_date": pendulum.datetime(year=2024, month=7, day=2, tz="Asia/Kolkata"),
    "depends_on_past": False,
    "email": [
        "<EMAIL>",
        "<EMAIL>",
    ],
    "email_on_retry": True,
    "retries": 0,
    "retry_delay": datetime.timedelta(minutes=5),
    "on_failure_callback": send_message_to_ms_teams,
}

dag = DAG(
    dag_id="NSE_DAILY_DATA_EVENING",
    schedule_interval="35 16 * * 1-5",
    default_args=args,
    catchup=False,
)

AFTER_MARKET = PythonOperator(
    task_id="AFTER_MARKET",
    python_callable=dailyAppends.append_daily_data_1440,
    op_kwargs={"universe": "after_market", "timing": "evening"},
    dag=dag,
)


def check_holiday(**kwargs):
    if (
        datetime.datetime.combine(datetime.date.today(), datetime.time(0, 0))
        not in ALL_DATES
    ):
        return "HOLIDAY"
    else:
        return "WORKING_DAY"


HOLIDAY = DummyOperator(
    task_id="HOLIDAY",
    dag=dag,
)

WORKING_DAY = DummyOperator(
    task_id="WORKING_DAY",
    dag=dag,
)

CHECK_HOLIDAY_BRANCHING = BranchPythonOperator(
    task_id="CHECK_HOLIDAY",
    python_callable=check_holiday,
    dag=dag,
)

SAMPLER_DUMP = DummyOperator(
    task_id="SAMPLER_DUMP",
    dag=dag,
)


CHECK_HOLIDAY_BRANCHING >> HOLIDAY
CHECK_HOLIDAY_BRANCHING >> WORKING_DAY
WORKING_DAY >> AFTER_MARKET

segment_mapping = dailyAppends._config.SEGMENT_APPEND_LIST
WORKING_DAY >> SAMPLER_DUMP

for segment, frequency, dtype in segment_mapping:
    TASK = PythonOperator(
        task_id=f"{segment}_{frequency}min_{dtype}",
        python_callable=dailyAppends.append_segment_data,
        op_kwargs={
            "exchange": "nse",
            "segment": segment,
            "frequency": frequency,
            "dtype": dtype,
        },
        dag=dag,
    )
    SAMPLER_DUMP >> TASK
