import sys
import pandas as pd

sys.path.append("/home/<USER>")
sys.path.append("/home/<USER>")
from airflow.models import DAG
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python_operator import BranchPythonOperator
from airflow.operators.python_operator import Python<PERSON>perator
import datetime
import pendulum
import numpy as np
from io import BytesIO
from minio import Minio
from airflow.hooks.base_hook import BaseHook
from ms_teams_notifications import send_message_to_ms_teams
from daily_appends.GIFT.daily_appends_gift import DailyAppendsGIFT

dailyAppends = DailyAppendsGIFT(exchange_type="gift")

minioToken = BaseHook.get_connection("minio")
minioClient = Minio(
    f"{minioToken.host}:{minioToken.port}",
    access_key=minioToken.login,
    secret_key=minioToken.password,
    secure=False,
)

ALL_DATES_GIFT = np.load(
    BytesIO(
        minioClient.get_object("commondata", "balte_uploads/ALL_DATES_GIFT.npy").data
    ),
    allow_pickle=True,
)

args = {
    "owner": "Airflow",
    "start_date": pendulum.datetime(year=2025, month=4, day=1, tz="Asia/Kolkata"),
    "depends_on_past": False,
    "email": [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    ],
    "email_on_retry": True,
    "retries": 0,
    "retry_delay": datetime.timedelta(minutes=5),
    "on_failure_callback": send_message_to_ms_teams,
}

dag = DAG(
    dag_id="GIFT_DAILY_DATA",
    schedule_interval="15 3,5 * * *",
    default_args=args,
    catchup=False,
)


today = pd.Timestamp.now().normalize()
yesterday = today - pd.Timedelta(days=1)


def check_holiday(**kwargs):
    if yesterday not in ALL_DATES_GIFT:
        return "HOLIDAY"
    elif pd.Timestamp.now().hour >= 5:
        return "PRE_MARKET_APPENDS"
    else:
        return "POST_MARKET_APPENDS"


HOLIDAY = DummyOperator(
    task_id="HOLIDAY",
    dag=dag,
)

PRE_MARKET_APPENDS = DummyOperator(
    task_id="PRE_MARKET_APPENDS",
    dag=dag,
)

POST_MARKET_APPENDS = DummyOperator(
    task_id="POST_MARKET_APPENDS",
    dag=dag,
)

CHECK_HOLIDAY_AND_MARKET_BRANCHING = BranchPythonOperator(
    task_id="CHECK_HOLIDAY_AND_MARKET_BRANCHING",
    python_callable=check_holiday,
    dag=dag,
)

CHECK_HOLIDAY_AND_MARKET_BRANCHING >> HOLIDAY
CHECK_HOLIDAY_AND_MARKET_BRANCHING >> PRE_MARKET_APPENDS
CHECK_HOLIDAY_AND_MARKET_BRANCHING >> POST_MARKET_APPENDS


RAW_FUTIDX_FUT = PythonOperator(
    task_id="RAW_FUTIDX_FUT",
    python_callable=dailyAppends.append_daily_data_raw,
    op_kwargs={"universe": "futidx_fut", "date": yesterday},
    dag=dag,
)

FUTIDX_FUT_ONEMIN = PythonOperator(
    task_id="FUTIDX_FUT_ONEMIN",
    python_callable=dailyAppends.append_daily_data_1,
    op_kwargs={"universe": "futidx_fut", "date": yesterday},
    dag=dag,
)

FUTIDX_FUT_ONEMIN_COLUMN_BUCKET = PythonOperator(
    task_id="FUTIDX_FUT_ONEMIN_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_data_column_bucket,
    op_kwargs={"universe": "futidx_fut", "frequency": 1, "date": yesterday},
    dag=dag,
)

FUTIDX_FUT = PythonOperator(
    task_id="FUTIDX_FUT",
    python_callable=dailyAppends.append_daily_data_5,
    op_kwargs={"universe": "futidx_fut", "date": yesterday},
    dag=dag,
)

FUTIDX_FUT_COLUMN_BUCKET = PythonOperator(
    task_id="FUTIDX_FUT_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_data_column_bucket,
    op_kwargs={"universe": "futidx_fut", "frequency": 5, "date": yesterday},
    dag=dag,
)

FUTIDX_RAW_ONEMIN = PythonOperator(
    task_id="FUTIDX_RAW_ONEMIN",
    python_callable=dailyAppends.append_daily_data_1,
    op_kwargs={"universe": "futidx_raw", "date": yesterday},
    dag=dag,
)

FUTIDX_RAW_ONEMIN_COLUMN_BUCKET = PythonOperator(
    task_id="FUTIDX_RAW_ONEMIN_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_data_column_bucket,
    op_kwargs={"universe": "futidx_raw", "frequency": 1, "date": yesterday},
    dag=dag,
)

FUTIDX_RAW = PythonOperator(
    task_id="FUTIDX_RAW",
    python_callable=dailyAppends.append_daily_data_5,
    op_kwargs={"universe": "futidx_raw", "date": yesterday},
    dag=dag,
)

FUTIDX_RAW_COLUMN_BUCKET = PythonOperator(
    task_id="FUTIDX_RAW_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_data_column_bucket,
    op_kwargs={"universe": "futidx_raw", "frequency": 5, "date": yesterday},
    dag=dag,
)

RAW_FUTIDX_FUT_GIFT_BHAV = PythonOperator(
    task_id="RAW_FUTIDX_FUT_GIFT_BHAV",
    python_callable=dailyAppends.append_raw_bhav_copy,
    op_kwargs={"symbol": "futidx_fut_gift_bhav"},
    dag=dag,
)

FUTIDX_FUT_GIFT_BHAV = PythonOperator(
    task_id="FUTIDX_FUT_GIFT_BHAV",
    python_callable=dailyAppends.append_daily_data_1440,
    op_kwargs={
        "universe": "after_market",
        "timing": "morning",
        "symbol": "futidx_fut_gift_bhav",
    },
    dag=dag,
)

FUT_MARGIN_GIFT = PythonOperator(
    task_id="FUT_MARGIN_GIFT",
    python_callable=dailyAppends.append_daily_data_1440,
    op_kwargs={
        "universe": "after_market",
        "timing": "evening",
        "symbol": "fut_margin_gift",
    },
    dag=dag,
)

POST_MARKET_APPENDS >> RAW_FUTIDX_FUT

POST_MARKET_APPENDS >> FUTIDX_FUT_ONEMIN
FUTIDX_FUT_ONEMIN >> FUTIDX_FUT_ONEMIN_COLUMN_BUCKET
FUTIDX_FUT_ONEMIN >> FUTIDX_FUT
FUTIDX_FUT >> FUTIDX_FUT_COLUMN_BUCKET

POST_MARKET_APPENDS >> FUTIDX_RAW_ONEMIN
FUTIDX_RAW_ONEMIN >> FUTIDX_RAW_ONEMIN_COLUMN_BUCKET
FUTIDX_RAW_ONEMIN >> FUTIDX_RAW
FUTIDX_RAW >> FUTIDX_RAW_COLUMN_BUCKET

PRE_MARKET_APPENDS >> RAW_FUTIDX_FUT_GIFT_BHAV
PRE_MARKET_APPENDS >> FUTIDX_FUT_GIFT_BHAV

POST_MARKET_APPENDS >> FUT_MARGIN_GIFT
