# Introduction
Tanki is a python library for performing different operations on market related data. Tanki object is initialised with the help of exchange config.


# Components of Tanki
Tanki has the following components

- Library
- Auditor
- Checker
- Compiler
- Operation
- Storage

## `Library`
Library is Tanki's module to communicate with external APIs/DBs for data access. This provides APIs to Tanki to perform data reads, writes and other calls by hiding the underlying DB or disk reads. 

## `Auditor`
Library has auditor which is responsible for data reading and writing to different types of storage.

## `Checker`
Tank<PERSON> has checker which is responsible for doing all the checks on data as per the exchange config.

## `Compiler`


## `Operation`


## `Storage`
Storage module comprises of different types of storages required by functions of Tanki.
