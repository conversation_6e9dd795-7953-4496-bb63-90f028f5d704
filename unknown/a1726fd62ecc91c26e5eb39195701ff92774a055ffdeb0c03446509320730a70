# Checker API 

## Tanki initialization

```py
from tanki import Tanki
tanki_obj = Tank<PERSON>(exchange_type="nse")

```

## Checking Data

```py
def check_data(
        self,
        storage_type: StorageType,
        file_location: str,
        file_name: str,
        check_list: Optional[List[str]] = None,
        ignore_checks: Optional[List] = None,
    ) -> str:
        """Loads data and do all the data checks on universe_df
        Args:
            storage_type (StorageType): contains the type of storage of the file
            file_location (str): contains file location/universe name
            file_name (str): conatins file name
            check_list (Optional[List[str]], optional): List of checks to be performed on data. Defaults to None.
            ignore_checks (Optional[List], optional): List of checks to be ignored on data. Defaults to None.

        Returns:
            str: Aggregated string of messages after all peformed checks on data
        """
```

## **`Following checks are included`** :

- `check_columns_set` : checks the set of columns is correct and are in expected order.
- `check_columns_dtype` : checks the dtype of columns are as expected.
- `check_intraday_sudden_jumps` : checks for intraday sudden jumps in ["Open", "Close", "High", "Low"].
- `check_duplicate_entries` : checks for duplicates entries in data as well as ensures that no two different timestamp have exactly same data.
- `check_forward_fill` : checks for forward filling in - 
    - `OI` : forward filled per option contract
- `check_monotonic_nature` : checks for monotonic nature in - 
    - `Cons_Volume` : increasing nature per option contract per day
- `check_nan_entries` : checks for % nan_entries in data are within certain limit
- `check_overnight_sudden_jumps` : checks for overnight sudden jumps in ["Open", "Close", "High", "Low"] between last values in the previous day to initial and last values in the current day.
- `check_all_dates` : checks for missing and extra dates in the given data compared to all_dates, and also verifies the continuity of the data from the LastDate of the metadata
- `check_all_timestamps` : check for missing and extra time entries for particular ID and date in the data
- `check_OHLC` : checks to ensure "High" is the highest and "Low" is the lowest value in each row

`NOTE`- By default, check_data does all the checks on data but for explicitly considering all the checks use ["all"] in check_list or ignore_checks argument. Also, need to specify storage_type ("db", "local", "file").

### Examples - 

```py
# returns status of all the checks on data
# data is on storage_type at file_location/library_name for file_name/symbol
checks = tanki_obj.check_data(
    storage_type="db",
    file_location="nse/1_min/optstk/trd",
    file_name="1075",
)
```

```py
# Checking for all checks except check_nan_entries
checks = tanki_obj.check_data(
    storage_type="db",
    file_location="nse/1_min/optstk/trd",
    file_name="1075",
    check_list = ["all"],
    ignore_checks = ["check_nan_entries"],
)
```

```py
# Checking for check_nan_entries and check_duplicate_entries
checks = tanki_obj.check_data(
    storage_type="db",
    file_location="nse/1_min/optstk/trd",
    file_name="1075",
    check_list = ["check_nan_entries", "check_duplicate_entries"],
)
```