from typing import Optional
import pandas as pd
from daily_appends.daily_appends_base import DailyAppendsBase


class DailyAppendsKRX(DailyAppendsBase):
    def __init__(self, exchange_type: str) -> None:
        super().__init__(exchange_type=exchange_type)
        # For faster getfresh calls when data is not present in sampler. Make sure getfresh full day
        # completes before sampler resets.
        self._toti_obj.config.MAX_GET_LATEST_TIMESTAMP_ATTEMPTS = 1

    def append_raw_bhav_copy(self, symbol: str, date: Optional[pd.Timestamp] = None):
        raise NotImplementedError

    def append_daily_data_1440(self, universe, timing, symbol=None):
        raise NotImplementedError
