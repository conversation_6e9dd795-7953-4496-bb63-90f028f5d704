import pickle
import pandas as pd
from arcticdb import Arctic
from minio import Minio

store = Arctic("s3://192.168.0.121:9000:arctic-db?access=super&secret=doopersecret")
# lib = store["nse/1440_min/after_market/trd"]

minio_client = Minio("192.168.0.198:11009", "minioreader", "reasersecret", secure=False)

# getting only ID
old_isliquid = pd.read_parquet(
    "/media/hdd/jupyterdata/mantraraj/old_isliquid_optstk.parquet"
)
old_isliquid = old_isliquid[old_isliquid.index < pd.Timestamp(2024, 9, 1)]

# getting symbol_to_balte_id and balte_id_to_symbol
symbol_to_balte_id = pickle.loads(
    minio_client.get_object("commondata", "balte_uploads/mapping_dict").data
)
balte_id_to_symbol = {}
for k, v in symbol_to_balte_id.items():
    balte_id_to_symbol[v] = k

# create mapping from ID to symbol from current balte_id_to_symbol
old_isliquid["symbol"] = old_isliquid["ID"].map(balte_id_to_symbol)

demerger_merger = pd.read_parquet("new_demerger_merger.parquet")

# replacing symbol name for e.g. PEL_04OCT2002 to PEL
old_isliquid["symbol"] = old_isliquid["symbol"].str.replace(
    r"_\d{2}[A-Za-z]{3}\d{4}$", "", regex=True
)

symbol_change = pd.read_parquet("new_symbol_change.parquet")

for _, row in symbol_change.iterrows():
    if row["symbol"] in symbol_to_balte_id and row["current"] not in symbol_to_balte_id:
        symbol_to_balte_id[row["current"]] = symbol_to_balte_id[row["symbol"]]


old_isliquid = old_isliquid.reset_index()
symbol_change = symbol_change.reset_index()

# we will use symbol change map to get current symbol for some symbol having IDs changed with symbol also

# merging isfno with symbol change data on symbol
merged_df = pd.merge(old_isliquid, symbol_change, on="symbol", how="left")
# if mapping exist of symbol to some another current symbol then make it as current symbol
merged_df["symbol"] = merged_df["current"].combine_first(merged_df["symbol"])
# dropping extra columns
merged_df = merged_df.drop(columns=["date_y", "current", "ID"])
merged_df = merged_df.rename(columns={"date_x": "date"}).set_index("date")
# now we are giving current ID to each symbol from symbol_to_balte_id
merged_df["ID"] = merged_df["symbol"].map(symbol_to_balte_id)
# merged_df["ID"] = merged_df["ID"].astype("uint64")

old_isliquid = merged_df

new_isliquid = old_isliquid.copy()

# then again changing ID with demerger_merger info
for sym, _ in new_isliquid.groupby("symbol"):
    dmsym = demerger_merger[demerger_merger.Symbol == sym]
    for index in range(len(dmsym) - 1, -1, -1):
        new_isliquid.loc[
            (new_isliquid.symbol == sym) & (new_isliquid.index < dmsym.index[index]),
            "ID",
        ] = dmsym.iloc[index]["ID"]

new_isliquid = new_isliquid[["ID", "symbol"]]
new_isliquid = new_isliquid.rename(columns={"symbol": "Symbol"})


new_isliquid["ID"] = new_isliquid["ID"].astype("uint64")
new_isliquid.to_parquet(
    "/home/<USER>/repos/data_auditing/new_isliquid_optstk.parquet"
)

print()
