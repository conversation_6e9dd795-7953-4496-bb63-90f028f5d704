name: env_balte_0.0.3_data_auditing_appends
channels:
  - defaults
  - conda-forge
dependencies:
  - python=3.11.4
  - ipykernel=6.28.0
  - ipywidgets=7.6.5
  - joblib=1.1.0
  - line_profiler=4.1.1
  - matplotlib-inline=0.1.2
  - pip=21.2.4
  - plotly=5.5.0
  - pytables=3.8.0
  - pytest=6.2.5
  - scipy=1.12.0
  - setuptools=68.2.2
  - cython=3.0.8
  - pip:
    - arcticdb==4.2.1
    - aiohttp==3.7.0
    - grpcio-tools==1.62.1
    - kafka-python==2.0.2
    - alphalens==0.3.6
    - matplotlib==3.3.1
    - minio==7.1.3
    - numba==0.59.0
    - pillow==10.2.0
    - pre-commit==2.17.0
    - py-lets-be-rational==1.0.1
    - py_vollib
    - pymongo
    - pymysql
    - pytest-cov==3.0.0
    - requests
    - retrying==1.3.3
    - schedule==1.1.0
    - simplejson==3.17.6
    - ta==0.9.0
    - tomli==2.0.0
    - typing-extensions>=4.4.0
    - cryptography==2.3.1
    - pyarrow==15.0.1
    - mpld3==0.5.7
    - sqlalchemy==1.4.31
    - black==22.1.0
    - typed_ast==1.5.5
    - regex==2022.1.18
    - appdirs==1.4.4
    - pathspec==0.9.0
    - cachetools==5.0.0
    - cloudpickle==2.1.0
    - celery==5.2.6
    - redis==4.1.4
    - multitasking==0.0.11
    - influxdb==5.3.1
    - pydantic==1.10.7
    - pandas-stubs==2.0.3.230814 # 23.1.2 This version doesn't exists in pip
    - aiokafka==0.8.0
    - uvloop==0.19.0
    - nest-asyncio==1.5.6
    - msgspec==0.18.6