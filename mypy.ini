[mypy]
exclude=^(test|docs|DAG_scripts|daily_appends)(/|$)|^main/data/checker_fixer\.py$

; Ensure full coverage
disallow_untyped_calls = False
disallow_untyped_defs = True
disallow_incomplete_defs = True
disallow_untyped_decorators = True
check_untyped_defs = True

; Restrict dynamic typing
disallow_any_generics = True
disallow_subclassing_any = False
warn_return_any = True

; Know exactly what you're doing
warn_redundant_casts = True
warn_unused_ignores = True
warn_unused_configs = True
warn_unreachable = True
show_error_codes = True

; Explicit is better than implicit
no_implicit_optional = True

follow_imports = skip
