import datetime
import pickle
import pandas as pd
from arcticdb import Arctic
from minio import Minio


minio_client = Minio("192.168.0.198:11009", "minioreader", "reasersecret", secure=False)

# getting only ID
old_isliquid = pd.read_parquet(
    "/home/<USER>/repos/data_auditing/old_isliquid.parquet"
)[["ID"]]

old_isliquid = old_isliquid[old_isliquid.index.date < datetime.date(2025, 1, 25)]

# getting symbol_to_balte_id and balte_id_to_symbol
symbol_to_balte_id_new = {}
with open(
    "/home/<USER>/repos/data_auditing/cash_metadata_till_20250121/new_symbol_to_balte_id",
    "rb",
) as f:
    symbol_to_balte_id_new = pickle.load(f)

symbol_to_balte_id = pickle.loads(
    minio_client.get_object("commondata", "balte_uploads/mapping_dict").data
)

for k in symbol_to_balte_id_new.keys():
    if k not in symbol_to_balte_id:
        symbol_to_balte_id[k] = symbol_to_balte_id_new[k]

balte_id_to_symbol = {}
for k, v in symbol_to_balte_id.items():
    balte_id_to_symbol[v] = k

# create mapping from ID to symbol from current balte_id_to_symbol
old_isliquid["symbol"] = old_isliquid["ID"].map(balte_id_to_symbol)

demerger_merger = pd.read_parquet(
    "/home/<USER>/repos/data_auditing/cash_metadata_till_20250121/demerger_merger.parquet"
)

# replacing symbol name for e.g. PEL_04OCT2002 to PEL
old_isliquid["symbol"] = old_isliquid["symbol"].str.replace(
    r"_\d{2}[A-Za-z]{3}\d{4}$", "", regex=True
)

symbol_change = pd.read_parquet(
    "/home/<USER>/repos/data_auditing/cash_metadata_till_20250121/symbol_change.parquet"
)

symbol_change = symbol_change[symbol_change.symbol != "DTIL"]


for _, row in symbol_change.iterrows():
    if row["symbol"] in symbol_to_balte_id and row["current"] not in symbol_to_balte_id:
        symbol_to_balte_id[row["current"]] = symbol_to_balte_id[row["symbol"]]


old_isliquid = old_isliquid.reset_index()
symbol_change = symbol_change.reset_index()

# we will use symbol change map to get current symbol for some symbol having IDs changed with symbol also

# merging isfno with symbol change data on symbol
merged_df = pd.merge(old_isliquid, symbol_change, on="symbol", how="left")
# if mapping exist of symbol to some another current symbol then make it as current symbol
merged_df["symbol"] = merged_df["current"].combine_first(merged_df["symbol"])
# dropping extra columns
merged_df = merged_df.drop(columns=["date_y", "current", "ID"])
merged_df = merged_df.rename(columns={"date_x": "date"}).set_index("date")
# now we are giving current ID to each symbol from symbol_to_balte_id
merged_df["ID"] = merged_df["symbol"].map(symbol_to_balte_id)
# merged_df["ID"] = merged_df["ID"].astype("uint64")

## Did handling for MAXINDIA and MEGH alag se
merged_df.loc[merged_df.symbol == "MAXINDIA", "ID"] = 1947
merged_df.loc[merged_df.symbol == "MEGH", "ID"] = 771

old_isliquid = merged_df

new_isliquid = old_isliquid.copy()

# then again changing ID with demerger_merger info
for sym, _ in new_isliquid.groupby("symbol"):
    dmsym = demerger_merger[demerger_merger.Symbol == sym]
    for index in range(len(dmsym) - 1, -1, -1):
        new_isliquid.loc[
            (new_isliquid.symbol == sym) & (new_isliquid.index < dmsym.index[index]),
            "ID",
        ] = dmsym.iloc[index]["ID"]

new_isliquid = new_isliquid[["ID", "symbol"]]
new_isliquid = new_isliquid.rename(columns={"symbol": "Symbol"})

new_isliquid["ID"] = new_isliquid["ID"].astype("uint64")
new_isliquid.to_parquet(
    "/home/<USER>/repos/data_auditing/cash_metadata_till_20250121/isliquid.parquet"
)

print()
