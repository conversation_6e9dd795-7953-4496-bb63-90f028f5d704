import datetime
import pickle
import pandas as pd
import os
from arcticdb import Arctic

store = Arctic("s3://192.168.0.121:9000:arctic-db?access=super&secret=doopersecret")


lib = store["nse/1_min/fut/trd"]
osyms = os.listdir(
    "/media/hdd/jupyterdata/mantraraj/old_fut_1min_trd_removed_volume_zero"
)

for sym in lib.list_symbols():
    sym = "1075"
    if f"{sym}.parquet" not in osyms:
        continue

    dfn = lib.read(sym).data
    dfo = pd.read_parquet(
        f"/media/hdd/jupyterdata/mantraraj/old_fut_1min_trd_removed_volume_zero/{sym}.parquet"
    )
    dfo = dfo[(dfo.index.date < datetime.date(2024, 9, 1))]
    dfn = dfn[(dfn.index.date < datetime.date(2024, 9, 1))]

    dfmo = pd.merge(dfo.reset_index(), dfn.reset_index(), how="left", on=["timestamp"])
    dfmn = pd.merge(dfn.reset_index(), dfo.reset_index(), how="left", on=["timestamp"])

    print()
