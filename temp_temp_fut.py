from arcticdb import Arctic
from arctic.date import DateRange
from datetime import datetime, timedelta
import pandas as pd
import re

# Connect to ArcticDB
store = Arctic("s3://192.168.0.121:9000:arctic-db?access=super&secret=doopersecret")
lib = store["nse/1_min/raw_opt_spot/trd"]
output_lib = store["nse/1_min/opt_spot_oi/trd"]

# Parameters
symbols = lib.list_symbols()
start_date = datetime(2020, 1, 1)
end_date = datetime(2025, 1, 1)

# Loop over each day
for day_start in pd.date_range(start=start_date, end=end_date - timedelta(days=1), freq='1D'):
    day_end = day_start + timedelta(days=1)
    date_range = DateRange(day_start, day_end)

    print(f"\n📆 Processing date: {day_start.date()}")

    day_data = {}  # base_sym → list of dataframes

    for sym in symbols:
        base_sym = re.sub(r'\d+$', '', sym)

        try:
            df = lib.read(sym, date_range=date_range).data
            if df.empty:
                continue
            if base_sym == "NIFTY":
                print()
            if base_sym not in day_data:
                day_data[base_sym] = []
            day_data[base_sym].append(df)

            print(f"  ✅ {sym} → {base_sym}: {len(df)} rows")
        except Exception as e:
            print(f"  ❌ {sym} on {day_start.date()}: {e}")

    # Append once per base symbol for the day
    for base_sym, dfs in day_data.items():
        combined_df = pd.concat(dfs).sort_values("timestamp")
        group_cols = ["symbol", "expiry"]

        resampled_list = []

        for group_values, group_df in combined_df.groupby(group_cols):
            oi_resampled = group_df['oi'].resample('1T').last()


            group_df_resampled = oi_resampled.to_frame()

            if isinstance(group_values, tuple):
                for col, val in zip(group_cols, group_values):
                    group_df_resampled[col] = val
            else:
                group_df_resampled[group_cols[0]] = group_values

            resampled_list.append(group_df_resampled)

        resampled_df = pd.concat(resampled_list)
        resampled_df.sort_index(inplace=True)
        output_lib.append(base_sym, resampled_df)
        print(f"  📤 Appended {len(combined_df)} rows to {base_sym}")
