import pandas as pd


def update_symbols_csv():
    symbols_csv = pd.read_csv(
        "/home/<USER>/repos/data_auditing/symbols.csv", header=None
    )
    symbols_csv.columns = ["symbol", "ID"]
    symbol_to_balte_id = dict(zip(symbols_csv.symbol, symbols_csv.ID))

    demerger_merger = pd.read_parquet(
        "/home/<USER>/repos/data_auditing/cash_metadata_till_20250121/demerger_merger.parquet"
    )
    symbol_change = pd.read_parquet(
        "/home/<USER>/repos/data_auditing/cash_metadata_till_20250121/symbol_change.parquet"
    )

    demerger_merger["symbol_date"] = (
        demerger_merger["Symbol"] + "_" + demerger_merger.index.strftime("%d%b%Y")
    )

    new_symbols_csv_from_demerger_merger = pd.DataFrame(
        {"symbol": demerger_merger.symbol_date.values, "ID": demerger_merger.ID.values}
    )

    symbol_change = symbol_change[symbol_change.symbol != symbol_change.current]

    old_symbol_to_balte_id = {}
    current_symbol_to_balte_id = {}

    for ind, row in symbol_change.iterrows():
        if (
            row.symbol in symbol_to_balte_id.keys()
            and row.current not in symbol_to_balte_id.keys()
        ):
            current_symbol_to_balte_id[row.current] = symbol_to_balte_id[row.symbol]
        if (
            row.current in symbol_to_balte_id.keys()
            and row.symbol not in symbol_to_balte_id.keys()
        ):
            old_symbol_to_balte_id[row.symbol] = symbol_to_balte_id[row.current]

    old_symbol_symbols_csv_from_symbol_change = pd.DataFrame(
        {"symbol": old_symbol_to_balte_id.keys(), "ID": old_symbol_to_balte_id.values()}
    )
    current_symbol_symbols_csv_from_symbol_chagne = pd.DataFrame(
        {
            "symbol": current_symbol_to_balte_id.keys(),
            "ID": current_symbol_to_balte_id.values(),
        }
    )

    new_symbols_csv = pd.concat(
        [
            new_symbols_csv_from_demerger_merger,
            old_symbol_symbols_csv_from_symbol_change,
            symbols_csv,
            current_symbol_symbols_csv_from_symbol_chagne,
        ]
    )

    new_symbols_csv = new_symbols_csv.reset_index()
    new_symbols_csv = new_symbols_csv.drop(columns="index")
    new_symbols_csv["ID"] = new_symbols_csv["ID"].astype("uint64")

    new_symbols_csv.to_csv("new_symbols.csv", index=False)

    print()


update_symbols_csv()
