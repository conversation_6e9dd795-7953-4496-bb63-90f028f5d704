import sys


sys.path.append("/home/<USER>")
sys.path.append("/home/<USER>")

from airflow.models import DAG
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python_operator import BranchPythonOperator
from airflow.operators.python_operator import PythonOperator
import datetime
import pendulum
import pandas as pd
import numpy as np
from io import BytesIO
from minio import Minio
from airflow.hooks.base_hook import BaseHook
from ms_teams_notifications import send_message_to_ms_teams
from daily_appends.NSE.daily_appends_nse import DailyAppendsNSE

dailyAppends = DailyAppendsNSE(exchange_type="nse")

minioToken = BaseHook.get_connection("minio")
minioClient = Minio(
    f"{minioToken.host}:{minioToken.port}",
    access_key=minioToken.login,
    secret_key=minioToken.password,
    secure=False,
)

ALL_DATES = np.load(
    BytesIO(minioClient.get_object("commondata", "balte_uploads/ALL_DATES.npy").data),
    allow_pickle=True,
)

args = {
    "owner": "Airflow",
    "start_date": pendulum.datetime(year=2024, month=7, day=1, tz="Asia/Kolkata"),
    "depends_on_past": False,
    "email": [
        "<EMAIL>",
        "<EMAIL>",
    ],
    "email_on_retry": True,
    "retries": 0,
    "retry_delay": datetime.timedelta(minutes=5),
    "on_failure_callback": send_message_to_ms_teams,
}


BHAV_LIST = dailyAppends._config.AFTER_MARKET_DICT_MINIO.keys()


def check_holiday(**kwargs):
    if (
        datetime.datetime.combine(datetime.date.today(), datetime.time(0, 0))
        not in ALL_DATES
    ):
        return "HOLIDAY"
    else:
        return "WORKING_DAY"


dag = DAG(
    dag_id="NSE_DAILY_DATA_MORNING",
    schedule_interval="35 7 * * 1-5",
    default_args=args,
    catchup=False,
)

HOLIDAY = DummyOperator(
    task_id="HOLIDAY",
    dag=dag,
)

WORKING_DAY = DummyOperator(
    task_id="WORKING_DAY",
    dag=dag,
)

BHAVCOPY_APPENDS = DummyOperator(
    task_id="BHAVCOPY_APPENDS",
    dag=dag,
)

RAW_BHAVCOPY_APPENDS = DummyOperator(
    task_id="RAW_BHAVCOPY_APPENDS",
    dag=dag,
)

CORPACT_PROCESS = DummyOperator(
    task_id="CORPACT_PROCESS",
    dag=dag,
)

CHECK_HOLIDAY_BRANCHING = BranchPythonOperator(
    task_id="CHECK_HOLIDAY",
    python_callable=check_holiday,
    dag=dag,
)

CHECK_HOLIDAY_BRANCHING >> HOLIDAY
CHECK_HOLIDAY_BRANCHING >> WORKING_DAY

WORKING_DAY >> RAW_BHAVCOPY_APPENDS
WORKING_DAY >> BHAVCOPY_APPENDS

for bhav_universe in BHAV_LIST:
    RAW_BHAV_TASK = PythonOperator(
        task_id=f"raw_{bhav_universe}",
        python_callable=dailyAppends.append_raw_bhav_copy,
        op_kwargs={"symbol": f"{bhav_universe}"},
        dag=dag,
    )
    RAW_BHAVCOPY_APPENDS >> RAW_BHAV_TASK

    if bhav_universe == "fo_bhav":
        continue

    BHAV_TASK = PythonOperator(
        task_id=f"{bhav_universe}",
        python_callable=dailyAppends.append_daily_data_1440,
        op_kwargs={
            "universe": "after_market",
            "timing": "morning",
            "symbol": f"{bhav_universe}",
        },
        dag=dag,
    )
    BHAVCOPY_APPENDS >> BHAV_TASK
    BHAV_TASK >> CORPACT_PROCESS

PARSE_CORPACT = PythonOperator(
    task_id="PARSE_CORPACT",
    python_callable=dailyAppends.parse_and_upload_corpact,
    op_kwargs={
        "date": pd.Timestamp.now().normalize(),
        "metafile_path": "/home/<USER>/main/corpact_files/",
    },
    dag=dag,
)

CHECK_BAD_CORPACT = PythonOperator(
    task_id="CHECK_BAD_CORPACT",
    python_callable=dailyAppends.check_bad_corpact,
    dag=dag,
)

SYMBOL_CHANGE = PythonOperator(
    task_id="SYMBOL_CHANGE",
    python_callable=dailyAppends.update_symbol_change,
    dag=dag,
)

DEMERGER_MERGER_CHECK = PythonOperator(
    task_id="DEMERGER_MERGER_CHECK",
    python_callable=dailyAppends.parse_demerger_merger,
    op_kwargs={
        "date": pd.Timestamp.now().normalize(),
        "metafile_path": "/home/<USER>/main/corpact_files/",
    },
    dag=dag,
)

SYMBOL_CHANGE_ON_DEMERGER_MERGER = PythonOperator(
    task_id="SYMBOL_CHANGE_ON_DEMERGER_MERGER",
    python_callable=dailyAppends.apply_symbol_change_on_demerger_merger,
    dag=dag,
)

WORKING_DAY >> SYMBOL_CHANGE
WORKING_DAY >> DEMERGER_MERGER_CHECK
WORKING_DAY >> SYMBOL_CHANGE_ON_DEMERGER_MERGER

CORPACT_PROCESS >> PARSE_CORPACT

CORPACT_LIBRARY_LIST = dailyAppends._config.CORPACT_LIBRARIES

APPLY_CORPACT = DummyOperator(
    task_id="APPLY_CORPACT",
    dag=dag,
)
TRD = DummyOperator(
    task_id="TRD",
    dag=dag,
)
COLUMN_BUCKET = DummyOperator(
    task_id="COLUMN_BUCKET",
    dag=dag,
)

PARSE_CORPACT >> APPLY_CORPACT
PARSE_CORPACT >> CHECK_BAD_CORPACT
APPLY_CORPACT >> TRD
APPLY_CORPACT >> COLUMN_BUCKET

for library in CORPACT_LIBRARY_LIST:
    exchange, frequency, universe, dtype = library.split("/")

    TASK = PythonOperator(
        task_id=f"{universe}_{frequency}_{dtype}",
        python_callable=dailyAppends.apply_corpact,
        op_kwargs={
            "universe": universe,
            "dtype": dtype,
            "frequency": int(frequency[0]),
            "metafile_path": "/home/<USER>/main/corpact_files/",
        },
        dag=dag,
    )

    if dtype == "trd":
        TRD >> TASK
    elif dtype == "column_bucket":
        COLUMN_BUCKET >> TASK

CORPACT_AFTER_MARKET = PythonOperator(
    task_id="corpact_after_market",
    python_callable=dailyAppends.apply_corpact,
    op_kwargs={
        "universe": "after_market",
        "dtype": "trd",
        "frequency": 1440,
        "metafile_path": "/home/<USER>/main/corpact_files/",
    },
    dag=dag,
)
APPLY_CORPACT >> CORPACT_AFTER_MARKET

CORPACT_SECTOR_WTS = PythonOperator(
    task_id="corpact_sector_wts",
    python_callable=dailyAppends.apply_corpact,
    op_kwargs={
        "universe": "sector_wts",
        "dtype": "trd",
        "frequency": 1440,
        "metafile_path": "/home/<USER>/main/corpact_files/",
    },
    dag=dag,
)
APPLY_CORPACT >> CORPACT_SECTOR_WTS
