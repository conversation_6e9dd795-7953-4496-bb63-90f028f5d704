import sys
import pandas as pd

sys.path.append("/home/<USER>")
sys.path.append("/home/<USER>")
from airflow.models import DAG
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python_operator import BranchPythonOperator
from airflow.operators.python_operator import Python<PERSON>perator
import datetime
import pendulum
import numpy as np
from io import BytesIO
from minio import Minio
from airflow.hooks.base_hook import BaseHook
from ms_teams_notifications import send_message_to_ms_teams
from daily_appends.BSE.daily_appends_bse import DailyAppendsBSE

dailyAppends = DailyAppendsBSE(exchange_type="bse")

minioToken = BaseHook.get_connection("minio")
minioClient = Minio(
    f"{minioToken.host}:{minioToken.port}",
    access_key=minioToken.login,
    secret_key=minioToken.password,
    secure=False,
)

ALL_DATES_BSE = np.load(
    BytesIO(minioClient.get_object("commondata", "balte_uploads/ALL_DATES.npy").data),
    allow_pickle=True,
)

args = {
    "owner": "Airflow",
    "start_date": pendulum.datetime(year=2025, month=4, day=1, tz="Asia/Kolkata"),
    "depends_on_past": False,
    "email": [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    ],
    "email_on_retry": True,
    "retries": 0,
    "retry_delay": datetime.timedelta(minutes=5),
    "on_failure_callback": send_message_to_ms_teams,
}

dag = DAG(
    dag_id="BSE_DAILY_DATA",
    schedule_interval="30 8,16 * * *",
    default_args=args,
    catchup=False,
)


today = pd.Timestamp.now().normalize()


def check_holiday(**kwargs):
    if today not in ALL_DATES_BSE:
        return "HOLIDAY"
    elif pd.Timestamp.now().hour <= 13:
        return "PRE_MARKET_APPENDS"
    else:
        return "POST_MARKET_APPENDS"


HOLIDAY = DummyOperator(
    task_id="HOLIDAY",
    dag=dag,
)

PRE_MARKET_APPENDS = DummyOperator(
    task_id="PRE_MARKET_APPENDS",
    dag=dag,
)

POST_MARKET_APPENDS = DummyOperator(
    task_id="POST_MARKET_APPENDS",
    dag=dag,
)

CHECK_HOLIDAY_AND_MARKET_BRANCHING = BranchPythonOperator(
    task_id="CHECK_HOLIDAY_AND_MARKET_BRANCHING",
    python_callable=check_holiday,
    dag=dag,
)

CHECK_HOLIDAY_AND_MARKET_BRANCHING >> HOLIDAY
CHECK_HOLIDAY_AND_MARKET_BRANCHING >> PRE_MARKET_APPENDS
CHECK_HOLIDAY_AND_MARKET_BRANCHING >> POST_MARKET_APPENDS


# RAW_FUTIDX = PythonOperator(
#     task_id="RAW_FUTIDX",
#     python_callable=dailyAppends.append_daily_data_raw,
#     op_kwargs={"universe": "futidx", "date": today},
#     dag=dag,
# )

FUTIDX_ONEMIN = PythonOperator(
    task_id="FUTIDX_ONEMIN",
    python_callable=dailyAppends.append_daily_data_1,
    op_kwargs={"universe": "futidx", "date": today},
    dag=dag,
)

FUTIDX = PythonOperator(
    task_id="FUTIDX",
    python_callable=dailyAppends.append_daily_data_5,
    op_kwargs={"universe": "futidx", "date": today},
    dag=dag,
)

FUTIDX_ONEMIN_COLUMN_BUCKET = PythonOperator(
    task_id="FUTIDX_ONEMIN_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_data_column_bucket,
    op_kwargs={"universe": "futidx", "frequency": 1},
    dag=dag,
)

FUTIDX_COLUMN_BUCKET = PythonOperator(
    task_id="FUTIDX_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_data_column_bucket,
    op_kwargs={"universe": "futidx", "frequency": 5},
    dag=dag,
)

# RAW_OPT = PythonOperator(
#     task_id="RAW_OPT",
#     python_callable=dailyAppends.append_daily_data_raw,
#     op_kwargs={"universe": "opt", "date": today},
#     dag=dag,
# )

OPT_ONEMIN = PythonOperator(
    task_id="OPT_ONEMIN",
    python_callable=dailyAppends.append_daily_data_1,
    op_kwargs={"universe": "opt", "date": today},
    dag=dag,
)

OPT = PythonOperator(
    task_id="OPT",
    python_callable=dailyAppends.append_daily_data_5,
    op_kwargs={"universe": "opt", "date": today},
    dag=dag,
)

OPT_ONEMIN_COLUMN_BUCKET = PythonOperator(
    task_id="OPT_ONEMIN_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_data_column_bucket,
    op_kwargs={"universe": "opt", "frequency": 1},
    dag=dag,
)

OPT_COLUMN_BUCKET = PythonOperator(
    task_id="OPT_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_data_column_bucket,
    op_kwargs={"universe": "opt", "frequency": 5},
    dag=dag,
)

AFTER_MARKET_EVENING = PythonOperator(
    task_id="AFTER_MARKET_EVENING",
    python_callable=dailyAppends.append_daily_data_1440,
    op_kwargs={"universe": "after_market", "timing": "evening"},
    dag=dag,
)

# RAW_OPT_OI = PythonOperator(
#     task_id="RAW_OPT_OI",
#     python_callable=dailyAppends.append_daily_data_raw,
#     op_kwargs={"universe": "opt_oi", "date": today},
#     dag=dag,
# )


# POST_MARKET_APPENDS >> RAW_FUTIDX
POST_MARKET_APPENDS >> FUTIDX_ONEMIN
FUTIDX_ONEMIN >> FUTIDX
FUTIDX_ONEMIN >> FUTIDX_ONEMIN_COLUMN_BUCKET
FUTIDX >> FUTIDX_COLUMN_BUCKET
# POST_MARKET_APPENDS >> RAW_OPT
POST_MARKET_APPENDS >> OPT_ONEMIN
OPT_ONEMIN >> OPT
OPT_ONEMIN >> OPT_ONEMIN_COLUMN_BUCKET
OPT >> OPT_COLUMN_BUCKET
# POST_MARKET_APPENDS >> RAW_OPT_OI
POST_MARKET_APPENDS >> AFTER_MARKET_EVENING

RAW_CM_BHAV_BSE = PythonOperator(
    task_id="RAW_CM_BHAV_BSE",
    python_callable=dailyAppends.append_raw_bhav_copy,
    op_kwargs={"symbol": "cm_bhav"},
    dag=dag,
)

CM_BHAV_BSE = PythonOperator(
    task_id="CM_BHAV_BSE",
    python_callable=dailyAppends.append_daily_data_1440,
    op_kwargs={
        "universe": "after_market",
        "timing": "morning",
        "symbol": "cm_bhav",
    },
    dag=dag,
)


FUTSTK_BHAV_BSE = PythonOperator(
    task_id="FUTSTK_BHAV_BSE",
    python_callable=dailyAppends.append_daily_data_1440,
    op_kwargs={
        "universe": "after_market",
        "timing": "morning",
        "symbol": "futstk_bhav",
    },
    dag=dag,
)


OPTSTK_BHAV_BSE = PythonOperator(
    task_id="OPTSTK_BHAV_BSE",
    python_callable=dailyAppends.append_daily_data_1440,
    op_kwargs={
        "universe": "after_market",
        "timing": "morning",
        "symbol": "optstk_bhav",
    },
    dag=dag,
)


FUTIDX_BHAV_BSE = PythonOperator(
    task_id="FUTIDX_BHAV_BSE",
    python_callable=dailyAppends.append_daily_data_1440,
    op_kwargs={
        "universe": "after_market",
        "timing": "morning",
        "symbol": "futidx_bhav",
    },
    dag=dag,
)


OPTIDX_BHAV_BSE = PythonOperator(
    task_id="OPTIDX_BHAV_BSE",
    python_callable=dailyAppends.append_daily_data_1440,
    op_kwargs={
        "universe": "after_market",
        "timing": "morning",
        "symbol": "optidx_bhav",
    },
    dag=dag,
)

RAW_FO_BHAV_BSE = PythonOperator(
    task_id="RAW_FO_BHAV_BSE",
    python_callable=dailyAppends.append_raw_bhav_copy,
    op_kwargs={"symbol": "fo_bhav"},
    dag=dag,
)

PRE_MARKET_APPENDS >> RAW_CM_BHAV_BSE
PRE_MARKET_APPENDS >> CM_BHAV_BSE
PRE_MARKET_APPENDS >> FUTIDX_BHAV_BSE
PRE_MARKET_APPENDS >> OPTIDX_BHAV_BSE
PRE_MARKET_APPENDS >> FUTSTK_BHAV_BSE
PRE_MARKET_APPENDS >> OPTSTK_BHAV_BSE
PRE_MARKET_APPENDS >> RAW_FO_BHAV_BSE
