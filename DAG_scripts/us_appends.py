import sys
import pandas as pd
import pytz


sys.path.append("/root/airflow/balte")
sys.path.append("/home/<USER>")
from airflow.models import DAG
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python_operator import Branch<PERSON>ythonOperator
from airflow.operators.python_operator import Python<PERSON>perator
import datetime
import pendulum
from io import BytesIO
from minio import Minio
from airflow.hooks.base_hook import BaseHook
from ms_teams_notifications import send_message_to_ms_teams
from daily_appends.US.daily_appends_us import DailyAppendsUS
from main.data.utility import find_timezone_offset

dailyAppends = DailyAppendsUS(exchange_type="us")

minioToken = BaseHook.get_connection("minio")
minioClient = Minio(
    f"{minioToken.host}:{minioToken.port}",
    access_key=minioToken.login,
    secret_key=minioToken.password,
    secure=False,
)


args = {
    "owner": "Airflow",
    "start_date": pendulum.datetime(year=2025, month=4, day=1, tz="Asia/Kolkata"),
    "depends_on_past": False,
    "email": [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    ],
    "email_on_retry": True,
    "retries": 0,
    "retry_delay": datetime.timedelta(minutes=5),
    "on_failure_callback": send_message_to_ms_teams,
}

dag = DAG(
    dag_id="US_DAILY_DATA",
    schedule_interval="35 2 * * *",
    default_args=args,
    catchup=False,
)
global spot_closed_flag
spot_closed_flag = False

TIMEZONE: pytz.FixedOffset = find_timezone_offset(
    base_timezone=pytz.timezone("US/Eastern"), offset=360
)

today = pd.Timestamp.now(tz=TIMEZONE).replace(tzinfo=None).normalize()


def check_holiday():
    holiday_list = pd.to_datetime(
        pd.read_csv(
            BytesIO(
                minioClient.get_object(
                    "commondata", "holiday_lists/us_spot_holiday_list.csv"
                ).data
            ),
            header=None,
        )[0]
    ).to_list()
    if (today not in dailyAppends._all_dates) and (today not in holiday_list):
        return "HOLIDAY"
    if today in holiday_list:
        global spot_closed_flag
        spot_closed_flag = True
    return "WORKING_DAY"


HOLIDAY = DummyOperator(
    task_id="HOLIDAY",
    dag=dag,
)

WORKING_DAY = DummyOperator(
    task_id="WORKING_DAY",
    dag=dag,
)


CHECK_HOLIDAY = BranchPythonOperator(
    task_id="CHECK_HOLIDAY",
    python_callable=check_holiday,
    dag=dag,
)

CHECK_HOLIDAY >> HOLIDAY
CHECK_HOLIDAY >> WORKING_DAY

OPT_ONEMIN = PythonOperator(
    task_id="OPT_ONEMIN",
    python_callable=dailyAppends.append_daily_data_1,
    op_kwargs={"universe": "opt", "date": today},
    dag=dag,
)  # done

# OPT = PythonOperator(
#     task_id="OPT",
#     python_callable=dailyAppends.append_daily_data_5,
#     op_kwargs={"universe": "opt_us", "date": today},
#     dag=dag,
# ) # done

# FUTIDX = PythonOperator(
#     task_id="FUTIDX",
#     python_callable=dailyAppends.append_daily_data_5,
#     op_kwargs={"universe": "futidx_us", "date": today},
#     dag=dag,
# )

# FUTIDX_EXTENDED = PythonOperator(
#     task_id="FUTIDX_EXTENDED",
#     python_callable=dailyAppends.append_daily_data_5,
#     op_kwargs={"universe": "futidx_extended_us", "date": today},
#     dag=dag,
# ) # done

FUTIDX_ONEMIN = PythonOperator(
    task_id="FUTIDX_ONEMIN",
    python_callable=dailyAppends.append_daily_data_1,
    op_kwargs={"universe": "futidx", "date": today},
    dag=dag,
)  # done

FUTIDX_EXTENDED_ONEMIN = PythonOperator(
    task_id="FUTIDX_EXTENDED_ONEMIN",
    python_callable=dailyAppends.append_daily_data_1,
    op_kwargs={"universe": "futidx_extended", "date": today},
    dag=dag,
)  # done

# RAW_FUTIDX_FUT = PythonOperator(
#     task_id="RAW_FUTIDX_FUT",
#     python_callable=dailyAppends.append_daily_data_raw,
#     op_kwargs={"universe": "fut_raw_us", "date": today},
#     dag=dag,
# ) # done

# FUTIDX_RAW_ONEMIN = PythonOperator(
#     task_id="FUTIDX_RAW_ONEMIN",
#     python_callable=dailyAppends.append_daily_data_1,
#     op_kwargs={"universe": "futidx_raw", "date": today},
#     dag=dag,
# )

FUTIDX_FUT_ONEMIN = PythonOperator(
    task_id="FUTIDX_FUT_ONEMIN",
    python_callable=dailyAppends.append_daily_data_1,
    op_kwargs={"universe": "futidx_fut", "date": today},
    dag=dag,
)  # done

# FUTIDX_FUT = PythonOperator(
#     task_id="FUTIDX_FUT",
#     python_callable=dailyAppends.append_daily_data_5,
#     op_kwargs={"universe": "futidx_fut_us", "date": today},
#     dag=dag,
# ) # done

FUTIDX_ONEMIN_COLUMN_BUCKET = PythonOperator(
    task_id="FUTIDX_ONEMIN_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_data_column_bucket,
    op_kwargs={"universe": "futidx", "frequency": 1, "date": today},
    dag=dag,
)

FUTIDX_EXTENDED_ONEMIN_COLUMN_BUCKET = PythonOperator(
    task_id="FUTIDX_EXTENDED_ONEMIN_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_data_column_bucket,
    op_kwargs={"universe": "futidx_extended", "frequency": 1, "date": today},
    dag=dag,
)

# FUTIDX_COLUMN_BUCKET = PythonOperator(
#     task_id="FUTIDX_COLUMN_BUCKET",
#     python_callable=dailyAppends.append_daily_data_column_bucket,
#     op_kwargs={"universe": "futidx_us", "frequency": 5, "date": today},
#     dag=dag,
# )

# OPT_COLUMN_BUCKET = PythonOperator(
#     task_id="OPTIDX_COLUMN_BUCKET",
#     python_callable=dailyAppends.append_daily_data_column_bucket,
#     op_kwargs={"universe": "opt_us", "frequency": 5, "date": today},
#     dag=dag,
# )

OPT_ONEMIN_COLUMN_BUCKET = PythonOperator(
    task_id="OPT_ONEMIN_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_data_column_bucket,
    op_kwargs={"universe": "opt", "frequency": 1, "date": today},
    dag=dag,
)


FUTIDX_FUT_ONEMIN_COLUMN_BUCKET = PythonOperator(
    task_id="FUTIDX_FUT_ONEMIN_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_data_column_bucket,
    op_kwargs={"universe": "futidx_fut", "frequency": 1, "date": today},
    dag=dag,
)


# FUTIDX_FUT_COLUMN_BUCKET = PythonOperator(
#     task_id="FUTIDX_FUT_COLUMN_BUCKET",
#     python_callable=dailyAppends.append_daily_data_column_bucket,
#     op_kwargs={"universe": "futidx_fut_us", "frequency": 5, "date": today},
#     dag=dag,
# )

FUTIDX_BHAV = PythonOperator(
    task_id="FUTIDX_BHAV",
    python_callable=dailyAppends.append_daily_data_1440,
    op_kwargs={"universe": "futidx", "date": today},
    dag=dag,
)

FUTIDX_EXTENDED_BHAV = PythonOperator(
    task_id="FUTIDX_EXTENDED_BHAV",
    python_callable=dailyAppends.append_daily_data_1440,
    op_kwargs={"universe": "futidx_extended", "date": today},
    dag=dag,
)

FUTIDX_FUT_BHAV = PythonOperator(
    task_id="FUTIDX_FUT_BHAV",
    python_callable=dailyAppends.append_daily_data_1440,
    op_kwargs={"universe": "futidx_fut", "date": today},
    dag=dag,
)

OPT_BHAV = PythonOperator(
    task_id="OPT_BHAV",
    python_callable=dailyAppends.append_daily_data_1440,
    op_kwargs={"universe": "opt", "date": today},
    dag=dag,
)

WORKING_DAY >> OPT_ONEMIN
OPT_ONEMIN >> OPT_ONEMIN_COLUMN_BUCKET
OPT_ONEMIN >> OPT_BHAV

if not spot_closed_flag:
    WORKING_DAY >> FUTIDX_ONEMIN
    FUTIDX_ONEMIN >> FUTIDX_ONEMIN_COLUMN_BUCKET
    FUTIDX_ONEMIN >> FUTIDX_BHAV

    WORKING_DAY >> FUTIDX_EXTENDED_ONEMIN
    FUTIDX_EXTENDED_ONEMIN >> FUTIDX_EXTENDED_ONEMIN_COLUMN_BUCKET
    FUTIDX_EXTENDED_ONEMIN >> FUTIDX_EXTENDED_BHAV

WORKING_DAY >> FUTIDX_FUT_ONEMIN
FUTIDX_FUT_ONEMIN >> FUTIDX_FUT_ONEMIN_COLUMN_BUCKET
FUTIDX_FUT_ONEMIN >> FUTIDX_FUT_BHAV
