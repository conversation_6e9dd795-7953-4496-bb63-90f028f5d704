import sys
import pandas as pd

sys.path.append("/root/airflow/balte")
sys.path.append("/home/<USER>")
from airflow.models import DAG
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python_operator import BranchPythonOperator
from airflow.operators.python_operator import PythonOperator
import datetime
import pendulum
import numpy as np
from io import BytesIO
from minio import Minio
from airflow.hooks.base_hook import BaseHook
from ms_teams_notifications import send_message_to_ms_teams
from daily_appends.KRX.daily_appends_krx import DailyAppendsKRX

dailyAppends = DailyAppendsKRX(exchange_type="krx")

minioToken = BaseHook.get_connection("minio")
minioClient = Minio(
    f"{minioToken.host}:{minioToken.port}",
    access_key=minioToken.login,
    secret_key=minioToken.password,
    secure=False,
)

ALL_DATES_KRX = np.load(
    BytesIO(
        minioClient.get_object("international", "all_dates/ALL_DATES_KRX.npy").data
    ),
    allow_pickle=True,
)

args = {
    "owner": "Airflow",
    "start_date": pendulum.datetime(year=2025, month=4, day=1, tz="Asia/Kolkata"),
    "depends_on_past": False,
    "email": [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    ],
    "email_on_retry": True,
    "retries": 0,
    "retry_delay": datetime.timedelta(minutes=5),
    "on_failure_callback": send_message_to_ms_teams,
}

dag = DAG(
    dag_id="KRX_DAILY_DATA",
    schedule_interval="0 14 * * *",
    default_args=args,
    catchup=False,
)


today = pd.Timestamp.now().normalize()


def check_holiday(**kwargs):
    if today not in ALL_DATES_KRX:
        return "HOLIDAY"
    return "WORKING_DAY"


HOLIDAY = DummyOperator(
    task_id="HOLIDAY",
    dag=dag,
)

WORKING_DAY = DummyOperator(
    task_id="WORKING_DAY",
    dag=dag,
)


CHECK_HOLIDAY = BranchPythonOperator(
    task_id="CHECK_HOLIDAY",
    python_callable=check_holiday,
    dag=dag,
)

CHECK_HOLIDAY >> HOLIDAY
CHECK_HOLIDAY >> WORKING_DAY

OPTIDX_ONEMIN = PythonOperator(
    task_id="OPTIDX_ONEMIN",
    python_callable=dailyAppends.append_daily_data_1,
    op_kwargs={"universe": "optidx", "date": today},
    dag=dag,
)

FUTIDX_ONEMIN = PythonOperator(
    task_id="FUTIDX_ONEMIN",
    python_callable=dailyAppends.append_daily_data_1,
    op_kwargs={"universe": "futidx", "date": today},
    dag=dag,
)

# Uncomment this after adding propper support in balte
# FUTIDX_RAW_ONEMIN = PythonOperator(
#     task_id="FUTIDX_RAW_ONEMIN",
#     python_callable=dailyAppends.append_daily_data_1,
#     op_kwargs={"universe": "futidx_raw", "date": today},
#     dag=dag,
# )

# RAW_FUTIDX_FUT = PythonOperator(
#     task_id="RAW_FUTIDX_FUT",
#     python_callable=dailyAppends.append_daily_data_raw,
#     op_kwargs={"universe": "fut_raw", "date": today},
#     dag=dag,
# )

FUTIDX_FUT_ONEMIN = PythonOperator(
    task_id="FUTIDX_FUT_ONEMIN",
    python_callable=dailyAppends.append_daily_data_1,
    op_kwargs={"universe": "futidx_fut", "date": today},
    dag=dag,
)

OPTIDX = PythonOperator(
    task_id="OPTIDX",
    python_callable=dailyAppends.append_daily_data_5,
    op_kwargs={"universe": "optidx", "date": today},
    dag=dag,
)

FUTIDX = PythonOperator(
    task_id="FUTIDX",
    python_callable=dailyAppends.append_daily_data_5,
    op_kwargs={"universe": "futidx", "date": today},
    dag=dag,
)


FUTIDX_FUT = PythonOperator(
    task_id="FUTIDX_FUT",
    python_callable=dailyAppends.append_daily_data_5,
    op_kwargs={"universe": "futidx_fut", "date": today},
    dag=dag,
)


FUTIDX_ONEMIN_COLUMN_BUCKET = PythonOperator(
    task_id="FUTIDX_ONEMIN_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_data_column_bucket,
    op_kwargs={"universe": "futidx", "frequency": 1, "date": today},
    dag=dag,
)

FUTIDX_COLUMN_BUCKET = PythonOperator(
    task_id="FUTIDX_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_data_column_bucket,
    op_kwargs={"universe": "futidx", "frequency": 5, "date": today},
    dag=dag,
)

OPTIDX_COLUMN_BUCKET = PythonOperator(
    task_id="OPTIDX_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_data_column_bucket,
    op_kwargs={"universe": "optidx", "frequency": 5, "date": today},
    dag=dag,
)

OPTIDX_ONEMIN_COLUMN_BUCKET = PythonOperator(
    task_id="OPTIDX_ONEMIN_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_data_column_bucket,
    op_kwargs={"universe": "optidx", "frequency": 1, "date": today},
    dag=dag,
)


FUTIDX_FUT_ONEMIN_COLUMN_BUCKET = PythonOperator(
    task_id="FUTIDX_FUT_ONEMIN_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_data_column_bucket,
    op_kwargs={"universe": "futidx_fut", "frequency": 1, "date": today},
    dag=dag,
)


FUTIDX_FUT_COLUMN_BUCKET = PythonOperator(
    task_id="FUTIDX_FUT_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_data_column_bucket,
    op_kwargs={"universe": "futidx_fut", "frequency": 5, "date": today},
    dag=dag,
)

WORKING_DAY >> OPTIDX_ONEMIN >> OPTIDX
OPTIDX_ONEMIN >> OPTIDX_ONEMIN_COLUMN_BUCKET
OPTIDX >> OPTIDX_COLUMN_BUCKET

WORKING_DAY >> FUTIDX_ONEMIN >> FUTIDX
FUTIDX_ONEMIN >> FUTIDX_ONEMIN_COLUMN_BUCKET
FUTIDX >> FUTIDX_COLUMN_BUCKET

WORKING_DAY >> FUTIDX_FUT_ONEMIN >> FUTIDX_FUT
FUTIDX_FUT_ONEMIN >> FUTIDX_FUT_ONEMIN_COLUMN_BUCKET
FUTIDX_FUT >> FUTIDX_FUT_COLUMN_BUCKET
