import sys
import pandas as pd

sys.path.append("/home/<USER>")
sys.path.append("/home/<USER>")
from airflow.models import DAG
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python_operator import BranchPythonOperator
from airflow.operators.python_operator import PythonOperator
import datetime
import pendulum
import numpy as np
from io import BytesIO
from minio import Minio
from airflow.hooks.base_hook import BaseHook
from ms_teams_notifications import send_message_to_ms_teams
from daily_appends.NSE.daily_appends_nse import DailyAppendsNSE

dailyAppends = DailyAppendsNSE(exchange_type="nse")

minioToken = BaseHook.get_connection("minio")
minioClient = Minio(
    f"{minioToken.host}:{minioToken.port}",
    access_key=minioToken.login,
    secret_key=minioToken.password,
    secure=False,
)

ALL_DATES = np.load(
    BytesIO(minioClient.get_object("commondata", "balte_uploads/ALL_DATES.npy").data),
    allow_pickle=True,
)

args = {
    "owner": "Airflow",
    "start_date": pendulum.datetime(year=2024, month=6, day=6, tz="Asia/Kolkata"),
    "depends_on_past": False,
    "email": [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    ],
    "email_on_retry": True,
    "retries": 0,
    "retry_delay": datetime.timedelta(minutes=5),
    "on_failure_callback": send_message_to_ms_teams,
}

dag = DAG(
    dag_id="NSE_DAILY_DATA",
    schedule_interval="35 16 * * 1-5",
    default_args=args,
    catchup=False,
)

RAW_CASH = PythonOperator(
    task_id="RAW_CASH",
    python_callable=dailyAppends.append_daily_data_raw,
    op_kwargs={"universe": "cash"},
    dag=dag,
)

CASH_ONEMIN = PythonOperator(
    task_id="CASH_ONEMIN",
    python_callable=dailyAppends.append_daily_data_1,
    op_kwargs={"universe": "cash"},
    dag=dag,
)

CASH = PythonOperator(
    task_id="CASH",
    python_callable=dailyAppends.append_daily_data_5,
    op_kwargs={"universe": "cash"},
    dag=dag,
)

FNO_ONEMIN_COLUMN_BUCKET = PythonOperator(
    task_id="FNO_ONEMIN_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_derived_universe,
    op_kwargs={
        "universe": "fno",
        "frequency": 1,
        "today": pd.Timestamp.now(),
        "lookback": 200,
        "base_universe": "cash",
        "filtering_list_name": "isfno",
    },
    dag=dag,
)

FNO_COLUMN_BUCKET = PythonOperator(
    task_id="FNO_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_derived_universe,
    op_kwargs={
        "universe": "fno",
        "frequency": 5,
        "today": pd.Timestamp.now(),
        "lookback": 200,
        "base_universe": "cash",
        "filtering_list_name": "isfno",
    },
    dag=dag,
)

EQ_ONEMIN_COLUMN_BUCKET = PythonOperator(
    task_id="EQ_ONEMIN_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_derived_universe,
    op_kwargs={
        "universe": "eq",
        "frequency": 1,
        "today": pd.Timestamp.now(),
        "lookback": 100,
        "base_universe": "cash",
        "filtering_list_name": "isliquid",
    },
    dag=dag,
)

EQ_COLUMN_BUCKET = PythonOperator(
    task_id="EQ_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_derived_universe,
    op_kwargs={
        "universe": "eq",
        "frequency": 5,
        "today": pd.Timestamp.now(),
        "lookback": 200,
        "base_universe": "cash",
        "filtering_list_name": "isliquid",
    },
    dag=dag,
)

RAW_OPT = PythonOperator(
    task_id="RAW_OPT",
    python_callable=dailyAppends.append_daily_data_raw,
    op_kwargs={"universe": "opt"},
    dag=dag,
)

RAW_OPT_OI = PythonOperator(
    task_id="RAW_OPT_OI",
    python_callable=dailyAppends.append_daily_data_raw,
    op_kwargs={"universe": "opt_oi"},
    dag=dag,
)

OPT_ONEMIN = PythonOperator(
    task_id="OPT_ONEMIN",
    python_callable=dailyAppends.append_daily_data_1,
    op_kwargs={"universe": "opt"},
    dag=dag,
)

OPT = PythonOperator(
    task_id="OPT",
    python_callable=dailyAppends.append_daily_data_5,
    op_kwargs={"universe": "opt"},
    dag=dag,
)

OPT_COLUMN_BUCKET = PythonOperator(
    task_id="OPT_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_data_column_bucket,
    op_kwargs={"universe": "opt", "frequency": 5},
    dag=dag,
)

RAW_OPT_ORD = PythonOperator(
    task_id="RAW_OPT_ORD",
    python_callable=dailyAppends.append_daily_data_raw,
    op_kwargs={"universe": "opt", "dtype": "ord"},
    dag=dag,
)

OPT_ONEMIN_ORD = PythonOperator(
    task_id="OPT_ONEMIN_ORD",
    python_callable=dailyAppends.append_daily_data_1,
    op_kwargs={"universe": "opt", "dtype": "ord"},
    dag=dag,
)

RAW_FUTIDX = PythonOperator(
    task_id="RAW_FUTIDX",
    python_callable=dailyAppends.append_daily_data_raw,
    op_kwargs={"universe": "futidx"},
    dag=dag,
)

RAW_FUTIDX_FUT_OI = PythonOperator(
    task_id="RAW_FUTIDX_FUT_OI",
    python_callable=dailyAppends.append_daily_data_raw,
    op_kwargs={"universe": "futidx_fut_oi"},
    dag=dag,
)

FUTIDX_ONEMIN = PythonOperator(
    task_id="FUTIDX_ONEMIN",
    python_callable=dailyAppends.append_daily_data_1,
    op_kwargs={"universe": "futidx"},
    dag=dag,
)

FUTIDX_ONEMIN_COLUMN_BUCKET = PythonOperator(
    task_id="FUTIDX_ONEMIN_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_data_column_bucket,
    op_kwargs={"universe": "futidx", "frequency": 1},
    dag=dag,
)

FUTIDX = PythonOperator(
    task_id="FUTIDX",
    python_callable=dailyAppends.append_daily_data_5,
    op_kwargs={"universe": "futidx"},
    dag=dag,
)

FUTIDX_COLUMN_BUCKET = PythonOperator(
    task_id="FUTIDX_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_data_column_bucket,
    op_kwargs={"universe": "futidx", "frequency": 5},
    dag=dag,
)

RAW_OPTSTK = PythonOperator(
    task_id="RAW_OPTSTK",
    python_callable=dailyAppends.append_daily_data_raw,
    op_kwargs={"universe": "optstk"},
    dag=dag,
)

RAW_OPTSTK_OI = PythonOperator(
    task_id="RAW_OPTSTK_OI",
    python_callable=dailyAppends.append_daily_data_raw,
    op_kwargs={"universe": "optstk_oi"},
    dag=dag,
)

OPTSTK_ONEMIN = PythonOperator(
    task_id="OPTSTK_ONEMIN",
    python_callable=dailyAppends.append_daily_data_1,
    op_kwargs={"universe": "optstk"},
    dag=dag,
)

OPTSTK = PythonOperator(
    task_id="OPTSTK",
    python_callable=dailyAppends.append_daily_data_5,
    op_kwargs={"universe": "optstk"},
    dag=dag,
)

RAW_OPTSTK_ORD = PythonOperator(
    task_id="RAW_OPTSTK_ORD",
    python_callable=dailyAppends.append_daily_data_raw,
    op_kwargs={"universe": "optstk", "dtype": "ord"},
    dag=dag,
)

OPTSTK_ONEMIN_ORD = PythonOperator(
    task_id="OPTSTK_ONEMIN_ORD",
    python_callable=dailyAppends.append_daily_data_1,
    op_kwargs={"universe": "optstk", "dtype": "ord"},
    dag=dag,
)

OPTSTK_ORD = PythonOperator(
    task_id="OPTSTK_ORD",
    python_callable=dailyAppends.append_daily_data_5,
    op_kwargs={"universe": "optstk", "dtype": "ord"},
    dag=dag,
)

RAW_OPTSTK_UNADJUSTED_SPOT = PythonOperator(
    task_id="RAW_OPTSTK_UNADJUSTED_SPOT",
    python_callable=dailyAppends.append_daily_data_raw,
    op_kwargs={"universe": "optstk_unadjusted_spot"},
    dag=dag,
)

OPTSTK_UNADJUSTED_SPOT_ONEMIN = PythonOperator(
    task_id="OPTSTK_UNADJUSTED_SPOT_ONEMIN",
    python_callable=dailyAppends.append_daily_data_1,
    op_kwargs={"universe": "optstk_unadjusted_spot"},
    dag=dag,
)

OPTSTK_UNADJUSTED_SPOT_ONEMIN_COLUMN_BUCKET = PythonOperator(
    task_id="OPTSTK_UNADJUSTED_SPOT_ONEMIN_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_data_column_bucket,
    op_kwargs={"universe": "optstk_unadjusted_spot", "frequency": 1},
    dag=dag,
)

OPTSTK_UNADJUSTED_SPOT = PythonOperator(
    task_id="OPTSTK_UNADJUSTED_SPOT",
    python_callable=dailyAppends.append_daily_data_5,
    op_kwargs={"universe": "optstk_unadjusted_spot"},
    dag=dag,
)

OPTSTK_UNADJUSTED_SPOT_COLUMN_BUCKET = PythonOperator(
    task_id="OPTSTK_UNADJUSTED_SPOT_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_data_column_bucket,
    op_kwargs={"universe": "optstk_unadjusted_spot", "frequency": 5},
    dag=dag,
)

RAW_FUT = PythonOperator(
    task_id="RAW_FUT",
    python_callable=dailyAppends.append_daily_data_raw,
    op_kwargs={"universe": "fut"},
    dag=dag,
)

RAW_FUT_OI = PythonOperator(
    task_id="RAW_FUT_OI",
    python_callable=dailyAppends.append_daily_data_raw,
    op_kwargs={"universe": "futstk_oi"},
    dag=dag,
)

FUT_OI_ONEMIN = PythonOperator(
    task_id="FUT_OI_ONEMIN",
    python_callable=dailyAppends.append_daily_data_1,
    op_kwargs={"universe": "futstk_oi"},
    dag=dag,
)

FUT_OI_ONEMIN_COLUMN_BUCKET = PythonOperator(
    task_id="FUT_OI_ONEMIN_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_data_column_bucket,
    op_kwargs={"universe": "futstk_oi", "frequency": 1},
    dag=dag,
)

FUTIDX_FUT_OI_ONEMIN = PythonOperator(
    task_id="FUTIDX_FUT_OI_ONEMIN",
    python_callable=dailyAppends.append_daily_data_1,
    op_kwargs={"universe": "futidx_oi"},
    dag=dag,
)

FUTIDX_FUT_OI_ONEMIN_COLUMN_BUCKET = PythonOperator(
    task_id="FUTIDX_FUT_OI_ONEMIN_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_data_column_bucket,
    op_kwargs={"universe": "futidx_oi", "frequency": 1},
    dag=dag,
)

FUT_ONEMIN = PythonOperator(
    task_id="FUT_ONEMIN",
    python_callable=dailyAppends.append_daily_data_1,
    op_kwargs={"universe": "fut"},
    dag=dag,
)

FUT_ONEMIN_COLUMN_BUCKET = PythonOperator(
    task_id="FUT_ONEMIN_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_data_column_bucket,
    op_kwargs={"universe": "fut", "frequency": 1},
    dag=dag,
)

FUT = PythonOperator(
    task_id="FUT",
    python_callable=dailyAppends.append_daily_data_5,
    op_kwargs={"universe": "fut"},
    dag=dag,
)

FUT_COLUMN_BUCKET = PythonOperator(
    task_id="FUT_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_data_column_bucket,
    op_kwargs={"universe": "fut", "frequency": 5},
    dag=dag,
)

FUT_RAW_ONEMIN = PythonOperator(
    task_id="FUT_RAW_ONEMIN",
    python_callable=dailyAppends.append_daily_data_1,
    op_kwargs={"universe": "fut_raw"},
    dag=dag,
)

FUT_RAW_ONEMIN_COLUMN_BUCKET = PythonOperator(
    task_id="FUT_RAW_ONEMIN_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_data_column_bucket,
    op_kwargs={"universe": "fut_raw", "frequency": 1},
    dag=dag,
)

FUT_RAW = PythonOperator(
    task_id="FUT_RAW",
    python_callable=dailyAppends.append_daily_data_5,
    op_kwargs={"universe": "fut_raw"},
    dag=dag,
)

FUT_RAW_COLUMN_BUCKET = PythonOperator(
    task_id="FUT_RAW_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_data_column_bucket,
    op_kwargs={"universe": "fut_raw", "frequency": 5},
    dag=dag,
)

RAW_FUTIDX_FUT = PythonOperator(
    task_id="RAW_FUTIDX_FUT",
    python_callable=dailyAppends.append_daily_data_raw,
    op_kwargs={"universe": "futidx_fut"},
    dag=dag,
)

FUTIDX_FUT_ONEMIN = PythonOperator(
    task_id="FUTIDX_FUT_ONEMIN",
    python_callable=dailyAppends.append_daily_data_1,
    op_kwargs={"universe": "futidx_fut"},
    dag=dag,
)

FUTIDX_FUT_ONEMIN_COLUMN_BUCKET = PythonOperator(
    task_id="FUTIDX_FUT_ONEMIN_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_data_column_bucket,
    op_kwargs={"universe": "futidx_fut", "frequency": 1},
    dag=dag,
)

FUTIDX_FUT = PythonOperator(
    task_id="FUTIDX_FUT",
    python_callable=dailyAppends.append_daily_data_5,
    op_kwargs={"universe": "futidx_fut"},
    dag=dag,
)

FUTIDX_FUT_COLUMN_BUCKET = PythonOperator(
    task_id="FUTIDX_FUT_COLUMN_BUCKET",
    python_callable=dailyAppends.append_daily_data_column_bucket,
    op_kwargs={"universe": "futidx_fut", "frequency": 5},
    dag=dag,
)

NEW_FNO_SLIPPAGE = PythonOperator(
    task_id="NEW_FNO_SLIPPAGE",
    python_callable=dailyAppends.create_slippage_data,
    op_kwargs={
        "universe": "new_fno_slippage",
        "base_universe": "fno",
        "date": pd.Timestamp.today().normalize(),
        "lookback": 300,
    },
    dag=dag,
)

NEW_EQ_SLIPPAGE = PythonOperator(
    task_id="NEW_EQ_SLIPPAGE",
    python_callable=dailyAppends.create_slippage_data,
    op_kwargs={
        "universe": "new_eq_slippage",
        "base_universe": "eq",
        "date": pd.Timestamp.today().normalize(),
        "lookback": 300,
    },
    dag=dag,
)

RAW_CIRCUIT = PythonOperator(
    task_id="RAW_CIRCUIT",
    python_callable=dailyAppends.append_daily_data_raw_1440,
    op_kwargs={
        "date": pd.Timestamp.today().normalize(),
    },
    dag=dag,
)


def check_holiday(**kwargs):
    if (
        datetime.datetime.combine(datetime.date.today(), datetime.time(0, 0))
        not in ALL_DATES
    ):
        return "HOLIDAY"
    else:
        return "WORKING_DAY"


HOLIDAY = DummyOperator(
    task_id="HOLIDAY",
    dag=dag,
)

WORKING_DAY = DummyOperator(
    task_id="WORKING_DAY",
    dag=dag,
)

CHECK_HOLIDAY_BRANCHING = BranchPythonOperator(
    task_id="CHECK_HOLIDAY",
    python_callable=check_holiday,
    dag=dag,
)


CHECK_HOLIDAY_BRANCHING >> WORKING_DAY
CHECK_HOLIDAY_BRANCHING >> HOLIDAY

WORKING_DAY >> RAW_CIRCUIT

WORKING_DAY >> RAW_CASH
WORKING_DAY >> RAW_FUT
WORKING_DAY >> RAW_FUT_OI
WORKING_DAY >> FUT_OI_ONEMIN
FUT_OI_ONEMIN >> FUT_OI_ONEMIN_COLUMN_BUCKET
WORKING_DAY >> FUTIDX_FUT_OI_ONEMIN
FUTIDX_FUT_OI_ONEMIN >> FUTIDX_FUT_OI_ONEMIN_COLUMN_BUCKET
WORKING_DAY >> RAW_FUTIDX
WORKING_DAY >> RAW_FUTIDX_FUT_OI
WORKING_DAY >> RAW_FUTIDX_FUT
WORKING_DAY >> RAW_OPT
WORKING_DAY >> RAW_OPT_OI
WORKING_DAY >> RAW_OPT_ORD
WORKING_DAY >> RAW_OPTSTK
WORKING_DAY >> RAW_OPTSTK_OI
WORKING_DAY >> RAW_OPTSTK_ORD
WORKING_DAY >> RAW_OPTSTK_UNADJUSTED_SPOT

WORKING_DAY >> CASH_ONEMIN
CASH_ONEMIN >> CASH
CASH_ONEMIN >> FNO_ONEMIN_COLUMN_BUCKET
CASH_ONEMIN >> EQ_ONEMIN_COLUMN_BUCKET
CASH >> FNO_COLUMN_BUCKET
CASH >> EQ_COLUMN_BUCKET
EQ_COLUMN_BUCKET >> NEW_EQ_SLIPPAGE
FNO_COLUMN_BUCKET >> NEW_FNO_SLIPPAGE

WORKING_DAY >> FUTIDX_ONEMIN
FUTIDX_ONEMIN >> FUTIDX_ONEMIN_COLUMN_BUCKET
FUTIDX_ONEMIN >> FUTIDX
FUTIDX >> FUTIDX_COLUMN_BUCKET

WORKING_DAY >> OPTSTK_UNADJUSTED_SPOT_ONEMIN
OPTSTK_UNADJUSTED_SPOT_ONEMIN >> OPTSTK_UNADJUSTED_SPOT_ONEMIN_COLUMN_BUCKET
OPTSTK_UNADJUSTED_SPOT_ONEMIN >> OPTSTK_UNADJUSTED_SPOT
OPTSTK_UNADJUSTED_SPOT >> OPTSTK_UNADJUSTED_SPOT_COLUMN_BUCKET

WORKING_DAY >> OPT_ONEMIN
OPT_ONEMIN >> OPT
OPT >> OPT_COLUMN_BUCKET

WORKING_DAY >> OPT_ONEMIN_ORD

WORKING_DAY >> OPTSTK_ONEMIN
OPTSTK_ONEMIN >> OPTSTK

WORKING_DAY >> OPTSTK_ONEMIN_ORD
OPTSTK_ONEMIN_ORD >> OPTSTK_ORD

WORKING_DAY >> FUT_ONEMIN
FUT_ONEMIN >> FUT_ONEMIN_COLUMN_BUCKET
FUT_ONEMIN >> FUT
FUT >> FUT_COLUMN_BUCKET

WORKING_DAY >> FUT_RAW_ONEMIN
FUT_RAW_ONEMIN >> FUT_RAW_ONEMIN_COLUMN_BUCKET
FUT_RAW_ONEMIN >> FUT_RAW
FUT_RAW >> FUT_RAW_COLUMN_BUCKET

WORKING_DAY >> FUTIDX_FUT_ONEMIN
FUTIDX_FUT_ONEMIN >> FUTIDX_FUT_ONEMIN_COLUMN_BUCKET
FUTIDX_FUT_ONEMIN >> FUTIDX_FUT
FUTIDX_FUT >> FUTIDX_FUT_COLUMN_BUCKET
