import pendulum
import requests


def send_message_to_ms_teams(context):
    dag_id = context["dag_run"].dag_id
    task_id = context["task_instance"].task_id

    payload = {
        "@type": "MessageCard",
        "@context": "http://schema.org/extensions",
        "themeColor": "FF0000",
        "summary": f"DAG_FAILURE!!! 121 DAG - {dag_id}",
        "sections": [
            {
                "activityTitle": "ALERT!!!",
                "activitySubtitle": "121 DAG FAILED",
                "text": f"**ALERT!!! 121 DAG - ```{dag_id}``` has FAILED on task: ```{task_id}``` at time ```{pendulum.now().format('YYYY-MM-DD hh:mm:ss')}```**",
            }
        ],
        "potentialAction": [
            {
                "@type": "OpenUri",
                "name": "View DAG",
                "targets": [
                    {
                        "os": "default",
                        "uri": f"http://192.168.0.121:8083/graph?dag_id={dag_id}",
                    }
                ],
            }
        ],
    }
    headers = {"content-type": "application/json"}
    requests.post(
        "https://kivicapitalin.webhook.office.com/webhookb2/0a85e92b-759a-4915-9c28-4b699f39f371@b5b1a78b-9721-421b-a3e5-b56d1f6ea9e2/IncomingWebhook/5e213b99e2494810b18a3bca62d7a2cf/1a080efb-521c-45ca-9f59-8c793debbbd8/V2Sc-mvrx3MJVnFu9aZx9zZbj9Ku_Ywo2rXbE93HJSN8o1",
        json=payload,
        headers=headers,
    )
