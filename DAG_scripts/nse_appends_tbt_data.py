import sys

sys.path.append("/home/<USER>")
sys.path.append("/home/<USER>")
from airflow.models import DAG
from airflow.operators.dummy_operator import DummyOperator
from airflow.operators.python_operator import BranchPythonOperator
from airflow.operators.python_operator import PythonOperator
import datetime
import pendulum
import numpy as np
from io import BytesIO
from minio import Minio
from airflow.hooks.base_hook import BaseHook
from ms_teams_notifications import send_message_to_ms_teams
from daily_appends.NSE.daily_appends_nse import DailyAppendsNSE

dailyAppends = DailyAppendsNSE(exchange_type="nse")

minioToken = BaseHook.get_connection("minio")
minioClient = Minio(
    f"{minioToken.host}:{minioToken.port}",
    access_key=minioToken.login,
    secret_key=minioToken.password,
    secure=False,
)

ALL_DATES = np.load(
    BytesIO(minioClient.get_object("commondata", "balte_uploads/ALL_DATES.npy").data),
    allow_pickle=True,
)

args = {
    "owner": "Airflow",
    "start_date": pendulum.datetime(year=2024, month=6, day=6, tz="Asia/Kolkata"),
    "depends_on_past": False,
    "email": [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>",
    ],
    "email_on_retry": True,
    "retries": 0,
    "retry_delay": datetime.timedelta(minutes=5),
    "on_failure_callback": send_message_to_ms_teams,
}

dag = DAG(
    dag_id="NSE_DAILY_DATA_TBT",
    schedule_interval="35 16 * * 1-5",
    default_args=args,
    catchup=False,
)


def check_holiday(**kwargs):
    if (
        datetime.datetime.combine(datetime.date.today(), datetime.time(0, 0))
        not in ALL_DATES
    ):
        return "HOLIDAY"
    else:
        return "WORKING_DAY"


HOLIDAY = DummyOperator(
    task_id="HOLIDAY",
    dag=dag,
)

WORKING_DAY = DummyOperator(
    task_id="WORKING_DAY",
    dag=dag,
)

CHECK_HOLIDAY_BRANCHING = BranchPythonOperator(
    task_id="CHECK_HOLIDAY",
    python_callable=check_holiday,
    dag=dag,
)

FUTSTK_MI_ONEMIN = PythonOperator(
    task_id="FUTSTK_MI_ONEMIN",
    python_callable=dailyAppends.append_daily_data_1,
    op_kwargs={"universe": "futstk_mi", "dtype": "indicator"},
    dag=dag,
)

FUTIDX_MI_ONEMIN = PythonOperator(
    task_id="FUTIDX_MI_ONEMIN",
    python_callable=dailyAppends.append_daily_data_1,
    op_kwargs={"universe": "futidx_mi", "dtype": "indicator"},
    dag=dag,
)

FUTSTK_MI = PythonOperator(
    task_id="FUTSTK_MI",
    python_callable=dailyAppends.append_daily_data,
    op_kwargs={"universe": "futstk_mi", "dtype": "indicator", "freq": 5},
    dag=dag,
)

FUTIDX_MI = PythonOperator(
    task_id="FUTIDX_MI",
    python_callable=dailyAppends.append_daily_data,
    op_kwargs={"universe": "futidx_mi", "dtype": "indicator", "freq": 5},
    dag=dag,
)

RAW_FUTSTK_OB_ONEMIN = PythonOperator(
    task_id="RAW_FUTSTK_OB_ONEMIN",
    python_callable=dailyAppends.append_daily_data_raw,
    op_kwargs={"universe": "futstk_ob", "dtype": "orderbook"},
    dag=dag,
)

RAW_FUTIDX_OB_ONEMIN = PythonOperator(
    task_id="RAW_FUTIDX_OB_ONEMIN",
    python_callable=dailyAppends.append_daily_data_raw,
    op_kwargs={"universe": "futidx_ob", "dtype": "orderbook"},
    dag=dag,
)

FUTSTK_OB_ONEMIN = PythonOperator(
    task_id="FUTSTK_OB_ONEMIN",
    python_callable=dailyAppends.append_daily_data_1,
    op_kwargs={"universe": "futstk_ob", "dtype": "orderbook"},
    dag=dag,
)

FUTIDX_OB_ONEMIN = PythonOperator(
    task_id="FUTIDX_OB_ONEMIN",
    python_callable=dailyAppends.append_daily_data_1,
    op_kwargs={"universe": "futidx_ob", "dtype": "orderbook"},
    dag=dag,
)

for indicator_dtype in [
    "SymbolAnalytics",
    "OrderFlowImbalance",
    "OrdOHLC",
    "TrdOHLC",
    "TradeOrders",
    "BuyerInitiatedTradeOrders",
    "SellerInitiatedTradeOrders",
    "SpreadTradeOrders",
    "MarketBuyTradeOrders",
    "MarketSellTradeOrders",
    "CancelledOrders",
    "CancelledBuyOrders",
    "CancelledSellOrders",
    "NewOrders",
    "NewBuyOrders",
    "NewSellOrders",
]:
    last_node = WORKING_DAY
    for universe in ["futstk_mi", "futidx_mi"]:
        for freq in [1, 5]:
            RAW_INDICATOR = PythonOperator(
                task_id=f"RAW_{universe.upper()}_{indicator_dtype}_{freq}MIN",
                python_callable=dailyAppends.append_daily_data_raw,
                op_kwargs={
                    "universe": universe,
                    "dtype": indicator_dtype,
                    "freq": freq,
                },
                dag=dag,
            )
            last_node >> RAW_INDICATOR
            last_node = RAW_INDICATOR

CHECK_HOLIDAY_BRANCHING >> WORKING_DAY
CHECK_HOLIDAY_BRANCHING >> HOLIDAY

WORKING_DAY >> FUTSTK_MI_ONEMIN
WORKING_DAY >> FUTIDX_MI_ONEMIN
WORKING_DAY >> FUTSTK_MI
WORKING_DAY >> FUTIDX_MI

WORKING_DAY >> RAW_FUTSTK_OB_ONEMIN
WORKING_DAY >> RAW_FUTIDX_OB_ONEMIN
WORKING_DAY >> FUTSTK_OB_ONEMIN
WORKING_DAY >> FUTIDX_OB_ONEMIN
