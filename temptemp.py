import os
import glob
import pandas as pd
from collections import defaultdict
from arcticdb import Arctic
store = Arctic("s3://192.168.0.121:9000:arctic-db?access=super&secret=doopersecret")
opt_lib = store["nse/1_min/raw_opt_spot_oi/trd"]
fut_lib = store["nse/1_min/raw_fut_spot_oi/trd"]

# Base directories to search
base_dirs = [
    "/home/<USER>/repos/data_auditing/raw_oi_data_from_snap_file",
    "/home/<USER>/repos/data_auditing/raw_oi_data_from_snap_file_fix",
    "/home/<USER>/repos/data_auditing/raw_oi_data_from_snap_file_part2"
]

# # Collect parquet files grouped by symbol
symbol_file_map = defaultdict(list)
for base_dir in base_dirs:
    files = glob.glob(os.path.join(base_dir, "**", "*.parquet"), recursive=True)
    for file in files:
        try:
            # Assumes structure: .../SYMBOL/filename.parquet
            symbol = os.path.basename(os.path.dirname(file))
            symbol_file_map[symbol].append(file)
        except Exception as e:
            print(f"Skipping file {file}: {e}")
existing_opt_symbols = set(opt_lib.list_symbols())
existing_fut_symbols = set(fut_lib.list_symbols())
existing_opt_symbols.add("NIFTYIT") 
existing_fut_symbols.add("NIFTYIT") 
# Process each symbol individually
for symbol, file_list in symbol_file_map.items():
    skip_opt = f"{symbol}" in existing_opt_symbols
    skip_fut = f"{symbol}" in existing_fut_symbols

    if skip_opt and skip_fut:
        print(f"Skipping {symbol} (already in both option and future libs)")
        continue

    num_files = len(file_list)
    print(f"\nProcessing symbol: {symbol} ({num_files} files)")

    # Split into chunks of max 5000 files
    batch_size = 5000
    for batch_idx in range(0, num_files, batch_size):
        batch_files = file_list[batch_idx:batch_idx + batch_size]
        batch_suffix = batch_idx // batch_size
        batched_symbol = f"{symbol}{batch_suffix}" if num_files > batch_size else f"{symbol}"

        # Skip check for split symbols
        if batched_symbol in existing_opt_symbols and batched_symbol in existing_fut_symbols:
            print(f"Skipping {batched_symbol} (already exists)")
            continue

        dfs = []
        for file in batch_files:
            try:
                df = pd.read_parquet(file)
                dfs.append(df)
            except Exception as e:
                print(f"Failed to read {file}: {e}")

        if not dfs:
            continue

        combined_df = pd.concat(dfs, ignore_index=True)
        combined_df["timestamp"] = pd.to_datetime(combined_df["timestamp"])

        opt_df = combined_df[combined_df["strike_price"].notna()].copy()
        fut_df = combined_df[combined_df["strike_price"].isna()].copy()
        fut_df = fut_df.drop(columns=["option_type", "strike_price"], errors="ignore")

        opt_df.set_index("timestamp", inplace=True)
        fut_df.set_index("timestamp", inplace=True)
        opt_df.sort_index(inplace=True)
        fut_df.sort_index(inplace=True)

        if not opt_df.empty:
            print(f"  Writing options for: {batched_symbol}")
            opt_lib.write(batched_symbol, opt_df)

        if not fut_df.empty:
            print(f"  Writing futures for: {batched_symbol}")
            fut_lib.write(batched_symbol, fut_df)
