[project]
name = "data-auditing"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.11"
dependencies = [
    "arcticdb==4.2.1",
    "bcrypt==4.1.2",
    "black==23.9.1",
    "coverage==7.3.0",
    "fastparquet==2023.7.0",
    "flake8==6.1.0",
    "hvac==1.2.1",
    "minio==7.2.3",
    "mock==5.1.0",
    "mypy==1.5.1",
    "numpy==1.26.4",
    "pandas==2.0.0",
    "pandas-stubs==2.0.3.230814",
    "protobuf==4.25.3",
    "py-vollib==1.0.1",
    "pyarrow==12.0.1",
    "pydantic==1.10.7",
    "pytest==7.4.0",
    "pytest-cov==4.1.0",
    "pytest-mock==3.12.0",
    "python-dotenv==1.1.0",
    "scipy==1.11.4",
    "sqlalchemy==1.4.31",
    "statsmodels==0.14.1",
    "time-machine==2.15.0",
    "types-cachetools==5.2.1",
    "types-python-dateutil>=2.9.0.20250708",
    "typing-extensions==4.4.0",
]

[tool.setuptools.packages.find]
where   = ["."]
include = ["data_auditing"]