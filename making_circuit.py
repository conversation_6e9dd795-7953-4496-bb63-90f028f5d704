import pickle
import pandas as pd
from arcticdb import Arctic
from minio import Minio

minio_client = Minio("192.168.0.198:11009", "minioreader", "reasersecret", secure=False)

symbol_to_balte_id = pickle.loads(
    minio_client.get_object("commondata", "balte_uploads/mapping_dict").data
)
balte_id_to_symbol = {}
for k, v in symbol_to_balte_id.items():
    balte_id_to_symbol[v] = k

# getting only ID
old_circuit = pd.read_parquet("/home/<USER>/repos/data_auditing/old_circuit.parquet")
old_circuit = old_circuit[~old_circuit.symbol.isna()]

demerger_merger = pd.read_parquet("new_demerger_merger.parquet")

# replacing symbol name for e.g. PEL_04OCT2002 to PEL
old_circuit["symbol"] = old_circuit["symbol"].str.replace(
    r"_\d{2}[A-Za-z]{3}\d{4}$", "", regex=True
)

symbol_change = pd.read_parquet("new_symbol_change.parquet")

for _, row in symbol_change.iterrows():
    if row["symbol"] in symbol_to_balte_id and row["current"] not in symbol_to_balte_id:
        symbol_to_balte_id[row["current"]] = symbol_to_balte_id[row["symbol"]]
    if row["current"] in symbol_to_balte_id and row["symbol"] not in symbol_to_balte_id:
        symbol_to_balte_id[row["symbol"]] = symbol_to_balte_id[row["current"]]


symbol_change = symbol_change.reset_index()

# we will use symbol change map to get current symbol for some symbol having IDs changed with symbol also

# merging old circuit with symbol change data on symbol
merged_df = pd.merge(old_circuit, symbol_change, on="symbol", how="left")
# if mapping exist of symbol to some another current symbol then make it as current symbol
merged_df["symbol"] = merged_df["current"].combine_first(merged_df["symbol"])
# dropping extra columns
merged_df = merged_df.drop(columns=["date_y", "current"])
merged_df = merged_df.rename(columns={"date_x": "date"}).set_index("date")
# now we are giving current ID to each symbol from symbol_to_balte_id
merged_df["ID"] = merged_df["symbol"].map(symbol_to_balte_id)
# merged_df["ID"] = merged_df["ID"].astype("uint64")

old_circuit = merged_df

new_circuit = old_circuit.copy()

# then again changing ID with demerger_merger info
for sym, _ in new_circuit.groupby("symbol"):
    dmsym = demerger_merger[demerger_merger.Symbol == sym]
    for index in range(len(dmsym) - 1, -1, -1):
        new_circuit.loc[
            (new_circuit.symbol == sym) & (new_circuit.index < dmsym.index[index]),
            "ID",
        ] = dmsym.iloc[index]["ID"]

# new_circuit = new_circuit[["ID", "symbol"]]
# new_circuit = new_circuit.rename(columns={"symbol": "Symbol"})

## Did handling for MAXINDIA and MEGH alag se

new_circuit.loc[new_circuit.Symbol == "MAXINDIA", "ID"] = 1947
new_circuit.loc[new_circuit.Symbol == "MEGH", "ID"] = 771
new_circuit["ID"] = new_circuit["ID"].astype("uint64")
new_circuit.to_parquet("/home/<USER>/repos/data_auditing/new_circuit.parquet")

print()
