import pandas as pd
import pymongo
from arctic import Arctic
from arctic.date import DateRange
import concurrent.futures
import sys
import time

sys.path.append("/home/<USER>/repos/balte")
import balte.balte_config


def send_message_to_teams(text, OTP_WEBHOOK):
    import json
    from urllib import request

    post = {
        "@context": "https://schema.org/extensions",
        "@type": "MessageCard",
        "themeColor": "0072C6",
        "title": "",
        "text": text,
    }
    try:
        json_data = json.dumps(post)
        req = request.Request(
            OTP_WEBHOOK,
            data=json_data.encode("ascii"),
            headers={"Content-Type": "application/json"},
        )
        resp = request.urlopen(req)
    except Exception as em:
        print("EXCEPTION: " + str(em))


def read_data():
    """
    Reads data from MongoDB through Arctic for the given date range.
    """
    mongoClient = pymongo.MongoClient(
        f"mongodb://{balte.balte_config.MONGO_CLIENT_IP}:{balte.balte_config.MONGO_CLIENT_PORT}/",
        username=balte.balte_config.MONGO_CLIENT_USERNAME,
        password=balte.balte_config.MONGO_CLIENT_PASSWORD,
    )
    store = Arctic(mongoClient)
    lib = store["nse/1_min/opt/ord"]
    lib.read(
        "5001",
        date_range=DateRange(pd.Timestamp(2024, 1, 1), pd.Timestamp(2024, 1, 26)),
    ).data


def main():
    timeout_threshold = 5  # Timeout in seconds

    with concurrent.futures.ThreadPoolExecutor() as executor:
        future = executor.submit(read_data)
        try:
            future.result(timeout=timeout_threshold)
        except concurrent.futures.TimeoutError:
            exception_message = f"Alert: Arctic data read running slow, took longer than threshold: {timeout_threshold} seconds"
            OTP_WEBHOOK = "https://kivicapitalin.webhook.office.com/webhookb2/390840e6-4007-4061-a163-c94892432a7b@b5b1a78b-9721-421b-a3e5-b56d1f6ea9e2/IncomingWebhook/a725aa9369a743d6a31ac07c1926c884/1a080efb-521c-45ca-9f59-8c793debbbd8"

            send_message_to_teams(exception_message, OTP_WEBHOOK)


if __name__ == "__main__":
    while pd.Timestamp.now().time() < pd.to_datetime("23:00:00").time():
        main()
        # Wait for 5 minutes (300 seconds)
        time.sleep(3)
