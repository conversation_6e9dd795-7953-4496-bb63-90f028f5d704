from arcticdb import Arctic as Arcticnew
from arctic import Arctic as Arcticold
from arctic.date import DateRange
import pandas as pd
from pymongo import MongoClient


mongo_connection_path = "xx"
MONGO_CLIENT_USERNAME = "xx"
MONGO_CLIENT_PASSWORD = "xx"

store_new = Arcticnew("xx")
mongo_client = MongoClient(
    mongo_connection_path,
    username=MONGO_CLIENT_USERNAME,
    password=MONGO_CLIENT_PASSWORD,
)
store_old = Arcticold(mongo_client)


def write_data(lib_new, symbol, data, metadata):
    """Writes data to the specified library and symbol, handling potential errors."""
    try:
        lib_new.write(symbol, data, metadata=metadata, prune_previous_versions=True)
        return None  # Indicate success
    except Exception as e:
        return f"Writing failed for {symbol} due to {e}"  # Return error message


libs = store_new.list_libraries()
exchange = "bse"
libs = [x for x in libs if f"{exchange}_old_v2" in x]
libs = [x for x in libs if "SAMPLER_DUMP" not in x]

for lib in libs:
    print(f"started for lib: {lib}")

    old_lib = lib.replace("_old_v2", "")
    lib_new = store_new[lib]
    lib_old = store_old[old_lib]

    if set(lib_new.list_symbols()) != set(lib_old.list_symbols()):
        print(f"Symbol mismatch in {lib}")
        continue

    error_list = []
    write_fail = []
    for symbol in lib_new.list_symbols():
        new = lib_new.read(symbol, date_range=(pd.Timestamp(2023, 4, 1), None))
        old = lib_old.read(symbol, date_range=DateRange(pd.Timestamp(2023, 4, 1), None))

        new_data = new.data
        old_data = old.data
        nmeta = new.metadata
        ometa = old.metadata

        if len(new_data) != len(old_data):
            error_message = f"Length mismatch for {symbol} - Old: {len(old_data)}, New: {len(new_data)}"
            error_list.append(error_message)
            write_error = write_data(lib_new, symbol, old_data, nmeta)
            if write_error:
                write_fail.append(write_error)

        old = None
        new = None
        old_data = pd.DataFrame()
        new_data = pd.DataFrame()

    if len(error_list) > 0:
        print(f"{lib} errors - ")
        for error in error_list:
            print(error)
        print("-------------------------")
