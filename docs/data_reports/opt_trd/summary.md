# Data Quality report

## Introduction

This report provides an overview of the data quality checks conducted on the dataset. It includes checks for:
- Columns set
- Data types
- NaNs
- Duplicates
- Monotonicity
- Forward filling
- Jumps of OHLC (Open, High, Low, Close): across them, intraday, interday
- Completeness of dates and timestamps

## Detailed Analysis:

**Columns Set:** All columns are present in the order ["ID", "Open", "High", "Low", "Close", "Vwap", "Cons_Volume"] having "timestamp" as an index.

**Columns Data Type:** All columns have the data types ["uint64", "float64", "float64", "float64", "float64", "float64", "float64"] and "datetime64[ns]" in index

**NaNs Entries:** There are no instances where all OHLC (Open, High, Low, Close) prices are NaN

**Duplicate Entries:** No duplicate entries in the dataset

**Monotonic Nature:** The data follows a monotonic trend as expected for the column "Cons_Volume"

**Forward Fill:** All missing values have been forward-filled for the column "Open_int" or "OI"

**Jumps of OHLC:** We have tried to add the data for which intraday, interday and across OHLC jumps exceeded a certain multiplier of their respective averages.

## Missing dates and times 

These are the missing dates and times in the data after integrating it with the external data sources available and the tick data.

### Missing dates list 

- [5001](download_files/5001_missing_dates.csv){:download}
- [5002](download_files/5002_missing_dates.csv){:download}
- [5003](download_files/5003_missing_dates.csv){:download}
- [5008](download_files/5008_missing_dates.csv){:download}

### Missing times list 

- [5001](download_files/5001_missing_times.csv){:download}
- [5002](download_files/5002_missing_times.csv){:download}
- [5003](download_files/5003_missing_times.csv){:download}
- [5008](download_files/5008_missing_times.csv){:download}

