# Compiler API 

## Tanki initialization

```py
from tanki import Tanki
tanki_obj = Tanki(exchange_type="nse")
```

```py
def compile_data(
    self, universe_list: List[str], frequency: int = 1, dtype: str = "trd"
) -> str:
    """Compile all the universes in universe_list as well as their dependencies

    Args:
        universe_list (List[str]): list of universes for compilation
        frequency (int, optional): frequency for compilation. Defaults to 1.
        dtype (str, optional): dtype for compilation. Defaults to "trd".

    Returns:
        str: response from compiler denoting success
        """
```

`NOTE`- Currently, we have support for optstk and optstk_unadjusted_spot only.

### Example - 

* Compile Request for optstk - 
```py
## This will compile the optstk universe as well as all the other universes
## with which it share some dependency
response = tanki_obj.compile_data(universe_list=["optstk"], frequency = 1, dtype = "trd")

## response will be Compilation Success!! if everything goes fine
```

* Accessing data compiled - 
```py
## compiled data will be stored in temporary library which is specific to 
## exchange with name = {exchange_name}/compilation
## i.e. for nse, it will be nse/compilation.
library = tanki_obj['nse/compilation']

## symbol follows {exchange}_{frequency}min_{universe}_{dtype} convention
optstk_compiled_data = library.read("nse_1min_optstk_trd")
optstk_unadjusted_spot_compiled_data = library.read("nse_1min_optstk_unadjusted_spot_trd")
```