# Tanki Tutorial

Tanki is a data management system for market data with authentication and access control. It provides a unified interface for accessing, validating, and compiling market data across different libraries.

## Creating a Tanki Object

To start using Tanki, you need to create a Tanki object:

```python
from main.tanki import Tanki

# Create a Tanki instance with default exchange type (NSE)
tanki = Tanki()

# Or specify a different exchange type
tanki = Tanki(exchange_type="nse")
```

## Authentication

Before accessing most functionalities, you need to log in:

```python
# Login with username and password
tanki.login(username="your_username", password="your_password")
```

## Working with Libraries

### Accessing Libraries

You can access libraries using dictionary-style notation:

```python
# Access a library
library = tanki["nse/5_min/opt/trd"]
```

### Listing Available Libraries

```python
# Get a list of all available libraries
libraries = tanki.list_libraries()
print(libraries)  # ['nse/5_min/opt/trd', 'nse/5_min/opt/ord', ...]
```

### Checking Permissions

```python
# Get libraries you have read access to
read_permissions = tanki.get_read_permissions()
print(read_permissions)

# Get libraries you have write access to
write_permissions = tanki.get_write_permissions()
print(write_permissions)
```

## Working with Data

### Reading Data

```python
# Access a library
library = tanki["nse/5_min/opt/trd"]

# Read all data for a symbol
data = library.read(symbol="5008")

# Read data for a specific date range
import pandas as pd
start_date = pd.Timestamp("2023-01-01")
end_date = pd.Timestamp("2023-01-31")
data = library.read(symbol="5008", start_date=start_date, end_date=end_date)
```

### Listing Symbols

```python
# Get all symbols in a library
symbols = tanki["nse/5_min/opt/trd"].list_symbols()
print(symbols)
```

### Writing and Updating Data

```python
# Write new data
library.write(symbol="5008", data=df)

# Update existing data
library.update(symbol="5008", data=df)

# Append data to existing symbol
library.append(symbol="5008", data=df)
```

## Data Validation

Tanki provides comprehensive data validation capabilities:

```python
# Run all checks on data
check_result = tanki.check_data(
    storage_type="db",
    file_location="nse/5_min/opt/trd",
    file_name="5008"
)
print(check_result)

# Run specific checks
check_result = tanki.check_data(
    storage_type="db",
    file_location="nse/5_min/opt/trd",
    file_name="5008",
    check_list=["check_columns_set", "check_nan_entries"]
)

# Run all checks except specific ones
check_result = tanki.check_data(
    storage_type="db",
    file_location="nse/5_min/opt/trd",
    file_name="5008",
    ignore_checks=["check_OHLC"]
)

# Check data that's not yet stored
check_result = tanki.check_data(
    storage_type="db",
    file_location="nse/5_min/opt/trd",
    file_name="5008",
    data=my_dataframe
)
```

## Data Compilation

Tanki can compile data for specified universes:

```python
# Compile data for specific universes
result = tanki.compile_data(
    universe_list=["opt"],
    frequency=1,
    dtype="trd",
    start_date=pd.Timestamp("2023-01-01"),
    end_date=pd.Timestamp("2023-12-31")
)
```

## Complete Example

Here's a complete example showing a typical workflow:

```python
from main.tanki import Tanki
import pandas as pd

# Create Tanki instance
tanki = Tanki(exchange_type="nse")

# Login
tanki.login(username="your_username", password="your_password")

# List available libraries
libraries = tanki.list_libraries()
print(f"Available libraries: {libraries}")

# Check permissions
read_permissions = tanki.get_read_permissions()
print(f"Read permissions: {read_permissions}")

# Access a library
library = tanki["nse/5_min/opt/trd"]

# List symbols in the library
symbols = library.list_symbols()
print(f"Symbols in library: {symbols}")

# Read data for a symbol
data = library.read(symbol="5008")
print(f"Data shape: {data.shape}")

# Validate the data
check_result = tanki.check_data(
    storage_type="db",
    file_location="nse/5_min/opt/trd",
    file_name="5008"
)
print(f"Validation result: {check_result}")

# Compile data
compile_result = tanki.compile_data(
    universe_list=["nse/5_min/opt/trd"],
    frequency=5,
    dtype="trd",
    start_date=pd.Timestamp("2023-01-01"),
    end_date=pd.Timestamp("2023-12-31")
)
print(f"Compilation result: {compile_result}")
```