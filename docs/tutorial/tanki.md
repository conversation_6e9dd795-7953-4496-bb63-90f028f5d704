# Tanki APIs

## Tanki initialization

```py
from tanki import Tanki
tanki_obj = Tanki(exchange_type="nse")

```

## User Logging

```py
# This will login to the Tanki after providing username and password as string
# This will raise an exception in case of any failure
tanki_obj.login(username="username", password="password")   
```

## User read permissions

```py
# Returns a list of libraries with read permissions for the logged-in user
# Raises an exception if the user is not logged in or if user data is not found
read_permissions = tanki_obj.get_read_permissions()
```

## User write permissions

```py
# Returns a list of libraries with write permissions for the logged-in user
# Raises an exception if the user is not logged in or if user data is not found
write_permissions = tanki_obj.get_write_permissions()
```

## Lists libraries

```py
# List all libraries available
# Raises an exception if the user is not logged in
libraries = tanki_obj.list_libraries()
```

## Read Data for symbol

```py
library = tanki_obj["library_name"]                 # accessing already initiliased library 
data = library.read(symbol = "symbol_name")         # reads data for symbol = symbol_name 
data = library.read(symbol = "symbol_name", start_date = start_date, end_date = end_date) # for range query from start_date to end_date. Note here start_date and end_date are optional arguments of type: Optional[pd.Timestamp]. If the start and end dates are not specified then whole data is queried. If both the start and end dates are specifed then data is queried between this date range. However, specifying just the start date will query the data from this start date till the end.
        
```

## List symbols of library

```py
symbols = library.list_symbols()    # returns all symbols (list) of the library = library_name
```

## Append Data to symbol

```py
library.append(symbol = "symbol_name", data = df)   # Appends data = df (dataframe) in symbol = symbol_name 
```

## Update Data for symbol

```py
library.update(symbol = "symbol_name", data = df)   # Updates data = df (dataframe) in symbol = symbol_name
```

## Write Data in symbol

```py
library.write(symbol = "symbol_name", data = df)   # write data = df (dataframe) in symbol = symbol_name
```

## Check Data for symbol

```py
# returns status of all the checks on data
checks = tanki_obj.check_data(storage_type="storage_type",file_location="library_name",file_name="symbol_name")

# More details in Checker API Tutorial
```