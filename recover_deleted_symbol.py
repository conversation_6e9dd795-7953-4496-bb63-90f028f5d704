import boto3

# Setup your MinIO credentials and endpoint
s3 = boto3.client(
    "s3",
    endpoint_url="http://192.168.0.121:9000",  # your MinIO URL
    aws_access_key_id="super",
    aws_secret_access_key="doopersecret",
)

bucket = "kivi-arcticdb"  # your bucket name
prefix = "nse/1_min/cash/trd"  # ArcticDB usually stores everything under this

symbol_name = "*1808*"

# Restore all non-latest versions matching symbol
for page in s3.get_paginator('list_object_versions').paginate(Bucket=bucket, Prefix=prefix):
    for obj in page.get("Versions", []):
        key = obj["Key"]
        version_id = obj["VersionId"]

        if symbol_name in key:
            print(f"Restoring: {key} (version {version_id})")
            s3.copy_object(
                Bucket=bucket,
                CopySource={'Bucket': bucket, 'Key': key, 'VersionId': version_id},
                Key=key
            )