import datetime
import os
import pickle
import numpy as np
from main.tanki import Tanki
import pandas as pd
from arcticdb import Arctic
from multiprocessing import Pool, Manager
import time


def process_symbol(sym, failed_symbols_list):
    try:
        sym_name = sym.split(".")[0]
        data = pd.read_parquet(f"backup_cash_1min_trd/{sym}")

        if os.path.exists(f"backup_cash_1min_trd_metadata/{sym_name}"):
            meta = {}
            with open(f"backup_cash_1min_trd_metadata/{sym_name}", "rb") as f:
                meta = pickle.load(f)

            storek = Arctic(
                "s3://*************:9000:kivi-arcticdb?access=super&secret=doopersecret"
            )
            libc = storek["nse/1_min/cash/trd"]

            libc.write(sym_name, data, metadata=meta)
            print(f"Successfully processed {sym_name} with metadata")
        else:
            tanki = Tanki(exchange_type="nse")
            tanki.login(username="mantraraj", password="mantraraj")

            tanki["nse/1_min/cash/trd"].write_metadata(sym_name, data)
            tanki["nse/1_min/cash/trd"].write(sym_name, data)
            print(f"Successfully processed {sym_name} without metadata")

    except Exception as e:
        print(f"Failed to process {sym}: {e}")
        failed_symbols_list.append((sym, str(e)))


if __name__ == "__main__":
    
    storek = Arctic(
        "s3://*************:9000:kivi-arcticdb?access=super&secret=doopersecret"
    )
    print()



import pandas as pd
import numpy as np
import timeit

def benchmark_operations(dtype, n=1_000_000, repeats=5):
    # Create sample DataFrame
    df = pd.DataFrame(np.random.rand(n, 4), columns=list('OHLC')).astype(dtype)
    
    # Helper function to measure memory and time for specific operations
    def measure_operation(operation):
        times = []
        for _ in range(repeats):
            start_time = timeit.default_timer()
            operation() # Execute the operation
            times.append(timeit.default_timer() - start_time)
            avg_time = np.mean(times) # Take average time
            return avg_time

    # List of operations to benchmark
    operations = {
        'addition': lambda: df["O"] + df["H"],
        'subtraction': lambda: df["O"] - df["H"],
        'multiplication': lambda: df["O"] * df["H"],
        'division': lambda: df["O"] / df["H"],
        'square_root': lambda: np.sqrt(df["O"]),
        'exponentiation': lambda: np.exp(df["O"]),
        'sum': lambda: df["O"].sum(),
        'mean': lambda: df["O"].mean(),
        'min': lambda: df["O"].min(),
        'max': lambda: df["O"].max(),
        'indexing': lambda: df["O"][::2], # Slice every 2nd element
    }
    
    results = {}
    for op_name, op_func in operations.items():
        time_taken = measure_operation(op_func)
        mem_usage = df.memory_usage(deep=True).sum() # Memory usage after operation
        results[op_name] = (time_taken, mem_usage)
    
        return results

# Benchmarking for float32 and float64
res32 = benchmark_operations('float32')
res64 = benchmark_operations('float64')

# Convert to DataFrame for easy display
operations = list(res32.keys())
time_32 = [res32[op][0] for op in operations]
time_64 = [res64[op][0] for op in operations]
mem_32 = [res32[op][1] for op in operations]
mem_64 = [res64[op][1] for op in operations]

# Calculate performance in percentage (time difference)
performance_percentage = [(t64 - t32) / t32 * -1 * 100 for t32, t64 in zip(time_32, time_64)]

benchmark_df = pd.DataFrame({
    'operation': operations,
    'time_float32_sec': time_32,
    'time_float64_sec': time_64,
    'memory_float32_bytes': mem_32,
    'memory_float64_bytes': mem_64,
    'performance_percentage': performance_percentage,
})

# Display to user
print(benchmark_df)